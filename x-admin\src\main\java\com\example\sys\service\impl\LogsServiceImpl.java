package com.example.sys.service.impl;

import com.example.sys.entity.Logs;
import com.example.sys.mapper.LogsMapper;
import com.example.sys.service.ILogsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class LogsServiceImpl extends ServiceImpl<LogsMapper, Logs> implements ILogsService {


    @Autowired
    private LogsMapper logsMapper;

    /**
     * 保存操作日志
     * @param log 日志实体
     */
    @Override
    public void saveLog(Logs log) {
        logsMapper.insert(log);
    }

    /**
     * 分页并根据条件查询日志列表
     */
    @Override
    public Map<String, Object> getLogsList(Long pageNo,
                                           Long pageSize,
                                           Integer id,
                                           String username,
                                           String realname,
                                           String moduleName,
                                           String operationType,
                                           String operationDescription,
                                           String operationIp,
                                           String operationResult,
                                           Boolean isSensitive) {
        // 构造分页对象
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<Logs> page =
                new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNo, pageSize);

        // 构造查询条件
        com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<Logs> wrapper =
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>();
        if (id != null) wrapper.eq(Logs::getId, id);
        if (org.springframework.util.StringUtils.hasText(username)) wrapper.eq(Logs::getUsername, username);
        if (org.springframework.util.StringUtils.hasText(realname)) wrapper.eq(Logs::getRealname, realname);
        if (org.springframework.util.StringUtils.hasText(moduleName)) wrapper.eq(Logs::getModuleName, moduleName);
        if (org.springframework.util.StringUtils.hasText(operationType)) wrapper.eq(Logs::getOperationType, operationType);
        if (org.springframework.util.StringUtils.hasText(operationDescription))
            wrapper.like(Logs::getOperationDescription, operationDescription);
        if (org.springframework.util.StringUtils.hasText(operationIp)) wrapper.eq(Logs::getOperationIp, operationIp);
        if (org.springframework.util.StringUtils.hasText(operationResult))
            wrapper.eq(Logs::getOperationResult, operationResult);
        if (isSensitive != null) wrapper.eq(Logs::getIsSensitive, isSensitive);

        wrapper.orderByDesc(Logs::getOperationTime);

        // 执行分页查询
        page = this.page(page, wrapper);

        // 组装返回结果
        Map<String, Object> data = new HashMap<>();
        data.put("total", page.getTotal());
        data.put("rows", page.getRecords());
        return data;
    }
    /**
     * 带过滤条件的导出数据查询
     */
    @Override
    public List<Logs> exportLogsList(
            Boolean isSensitive,
            LocalDateTime startTime,
            LocalDateTime endTime,
            String username,
            String realname
    ) {
        LambdaQueryWrapper<Logs> wrapper = new LambdaQueryWrapper<>();
        
        // 敏感操作过滤
        if (isSensitive != null) {
            wrapper.eq(Logs::getIsSensitive, isSensitive);
        }
        
        // 时间范围过滤（包含边界）
        if (startTime != null) {
            wrapper.ge(Logs::getOperationTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(Logs::getOperationTime, endTime);
        }
        
        // 用户名过滤
        if (StringUtils.hasText(username)) {
            wrapper.eq(Logs::getUsername, username);
        }
        
        // 真实姓名过滤
        if (StringUtils.hasText(realname)) {
            wrapper.eq(Logs::getRealname, realname);
        }
        
        // 按操作时间倒序排列
        wrapper.orderByDesc(Logs::getOperationTime);
        
        return logsMapper.selectList(wrapper);
    }
}
