package com.example.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

@Configuration
@EnableScheduling  // 启用任务调度功能
public class SchedulerConfig {

    @Bean
    public ThreadPoolTaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        // 配置核心线程数（根据业务需求调整，建议至少2个）
        scheduler.setPoolSize(5);
        // 配置线程名称前缀（方便日志排查）
        scheduler.setThreadNamePrefix("reminder-scheduler-");
        // 等待任务完成再关闭（避免任务中断）
        scheduler.setAwaitTerminationSeconds(60);
        // 关闭时是否等待任务完成
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        return scheduler;
    }
}
