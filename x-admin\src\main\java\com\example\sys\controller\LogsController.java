package com.example.sys.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;

import com.example.sys.service.ILogsService;
import com.example.sys.vo.LogsExcelVO;
import com.alibaba.excel.EasyExcel;
import com.example.commom.vo.Result;
import com.example.sys.annotation.OperationLog;
import com.example.sys.entity.Logs;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.net.URLEncoder;


/**
 * <p>
 * 用于记录管理系统中各种操作的详细日志信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@RestController
@RequestMapping("logs")
public class LogsController {
    @Autowired
    private ILogsService logsService;
    private static final Logger logger = LoggerFactory.getLogger(LogsController.class);
    @GetMapping("/list")
public Result<Map<String, Object>> listLogs(
        @RequestParam Long pageNo,
        @RequestParam Long pageSize,
        @RequestParam(required = false) Integer id,
        @RequestParam(required = false) String username,
        @RequestParam(required = false) String realname,
        @RequestParam(required = false) String moduleName,
        @RequestParam(required = false) String operationType,
        @RequestParam(required = false) String operationDescription,
        @RequestParam(required = false) String operationIp,
        @RequestParam(required = false) String operationResult,
        @RequestParam(required = false) Boolean isSensitive) {

    Map<String, Object> data = logsService.getLogsList(
        pageNo, pageSize, id, username, realname,
        moduleName, operationType, operationDescription,
        operationIp, operationResult, isSensitive
    );
    return Result.success(data);
}
/**
     * 带过滤条件的日志导出接口
     * @param response HTTP响应对象
     * @param isSensitive 是否敏感操作
     * @param startTime 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime 结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param username 操作用户名
     * @param realname 操作人姓名
     */
    @GetMapping("/export")
    @OperationLog(
            moduleName = "日志管理",
            operationType = "导出",
            desc = "导出日志数据",
            isSensitive = true
    )
    public void exportLogs(
            HttpServletResponse response,
            @RequestParam(required = false) Boolean isSensitive,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String realname
    ) {
        // 设置响应头
        response.setContentType("text/csv");
        response.setCharacterEncoding(StandardCharsets.UTF_8.toString());
        String filename = "操作日志导出_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".csv";
        response.setHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
        response.setHeader("Content-Transfer-Encoding", "binary");

        try (OutputStream os = response.getOutputStream()) {
            // 写入CSV表头
            os.write("操作ID,操作用户名,操作人姓名,模块名称,操作类型,操作描述,操作时间,操作IP,设备信息,关联数据ID,操作结果,错误信息,是否敏感\n".getBytes(StandardCharsets.UTF_8));

            // 获取过滤后的日志数据
            List<Logs> logList = logsService.exportLogsList(isSensitive, startTime, endTime, username, realname);

            // 日期格式化器
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            for (Logs log : logList) {
                String row = String.format(
                    "%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
                    log.getId(),
                    log.getUsername(),
                    log.getRealname(),
                    log.getModuleName(),
                    log.getOperationType(),
                    log.getOperationDescription(),
                    log.getOperationTime() != null ? dateFormatter.format(log.getOperationTime()) : "",
                    log.getOperationIp(),
                    log.getDeviceInfo() != null ? log.getDeviceInfo() : "",
                    log.getRelatedDataId() != null ? log.getRelatedDataId().toString() : "",
                    log.getOperationResult() != null ? log.getOperationResult() : "",
                    log.getErrorMessage() != null ? log.getErrorMessage() : "",
                    log.getIsSensitive() != null ? (log.getIsSensitive() ? "是" : "否") : ""
                );
                row = row.replaceAll(",", "，"); // 处理逗号防止CSV解析错误
                os.write(row.getBytes(StandardCharsets.UTF_8));
            }
            os.flush();
        } catch (IOException e) {
            e.printStackTrace(); // 生产环境建议集成日志系统
        }
    }

    /**
     * Excel格式日志导出接口
     */
    @GetMapping("/exportExcel")
    @OperationLog(
            moduleName = "日志管理",
            operationType = "导出",
            desc = "导出Excel格式日志数据",
            isSensitive = true
    )
    public void exportExcel(
            HttpServletResponse response,
            @RequestParam(required = false) Boolean isSensitive,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String realname
    ) {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
    
        // 生成文件名并编码
        String originalFilename = "操作日志导出_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
        try {
            String encodedFilename = URLEncoder.encode(originalFilename, "UTF-8")
                    .replaceAll("\\+", "%20"); // 处理空格兼容性问题
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFilename);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("文件名编码失败", e);
        }
    
        try {
            List<Logs> logList = logsService.exportLogsList(isSensitive, startTime, endTime, username, realname);
            if (logList == null || logList.isEmpty()) {
                throw new RuntimeException("查询结果为空");
            }
            List<LogsExcelVO> excelData = logList.stream()
                    .map(LogsExcelVO::new)
                    .collect(Collectors.toList());
            EasyExcel.write(response.getOutputStream(), LogsExcelVO.class)
                    .sheet("操作日志")
                    .doWrite(excelData);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

}
