package com.example.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-10
 */
@TableName("x_dictionary")
public class Dictionary implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "diction_id", type = IdType.AUTO)
    private Integer dictionId;

    private String term;

    private String form;

    private String descibe;

    /**
     * 是否删除
     */
    private Integer delate;

    public Integer getDictionId() {
        return dictionId;
    }

    public void setDictionId(Integer dictionId) {
        this.dictionId = dictionId;
    }
    public String getTerm() {
        return term;
    }

    public void setTerm(String term) {
        this.term = term;
    }
    public String getForm() {
        return form;
    }

    public void setForm(String form) {
        this.form = form;
    }
    public String getDescibe() {
        return descibe;
    }

    public void setDescibe(String descibe) {
        this.descibe = descibe;
    }
    public Integer getDelate() {
        return delate;
    }

    public void setDelate(Integer delate) {
        this.delate = delate;
    }

    @Override
    public String toString() {
        return "Dictionary{" +
            "dictionId=" + dictionId +
            ", term=" + term +
            ", form=" + form +
            ", descibe=" + descibe +
            ", delate=" + delate +
        "}";
    }
}
