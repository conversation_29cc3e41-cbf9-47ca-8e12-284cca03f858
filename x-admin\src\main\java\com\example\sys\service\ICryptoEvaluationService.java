package com.example.sys.service;

import com.example.sys.entity.CryptoEvaluation;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.Map;
/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface ICryptoEvaluationService extends IService<CryptoEvaluation> {
    Map<String, Object> getStatistics(String systemOwnerOrg, String systemName, String startTime, String endTime);
}
