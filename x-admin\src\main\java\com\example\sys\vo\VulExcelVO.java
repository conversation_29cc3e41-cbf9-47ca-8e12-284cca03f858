package com.example.sys.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.example.sys.entity.Vul;
import lombok.Data;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Data
public class VulExcelVO {
    @ExcelProperty("所属单位")
    private String systemOwnerOrg;

    @ExcelProperty("系统名称")
    private String systemName;

    @ExcelProperty("业务类型")
    private String businessType;

    @ExcelProperty("漏洞ID")
    private String vulId;

    @ExcelProperty("漏洞名称")
    private String vulName;

    @ExcelProperty("漏洞类型")
    private String vulType;

    @ExcelProperty("披露日期")
    private String disclosureDate;

    @ExcelProperty("漏洞级别")
    private String vulLevel;

    @ExcelProperty("漏洞状态")
    private String vulStatus;

    @ExcelProperty("漏洞描述")
    private String vulDescription;

    @ExcelProperty("CVE编号")
    private String cveNumber;

    @ExcelProperty("CNVD编号")
    private String cnvdNumber;

    @ExcelProperty("CNNVD编号")
    private String cnnvdNumber;

    @ExcelProperty("CVSS评分")
    private java.math.BigDecimal cvssScore;

    @ExcelProperty("补丁情况")
    private String patchSituation;

    @ExcelProperty("影响范围")
    private String impactScope;

    @ExcelProperty("解决方案建议")
    private String solutionSuggestions;

    @ExcelProperty("参考链接")
    private String referenceLink;

    public VulExcelVO() {}

    // 从实体类转换为VO（用于导出）
    public VulExcelVO(Vul entity) {
        this.systemOwnerOrg = entity.getSystemOwnerOrg();
        this.systemName = entity.getSystemName();
        this.businessType = entity.getBusinessType();
        this.vulId = entity.getVulId();
        this.vulName = entity.getVulName();
        this.vulType = entity.getVulType();
        LocalDate disclosureDate = entity.getDisclosureDate();
        this.disclosureDate = disclosureDate != null ? disclosureDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : null;
        this.vulLevel = entity.getVulLevel();
        this.vulStatus = entity.getVulStatus();
        this.vulDescription = entity.getVulDescription();
        this.cveNumber = entity.getCveNumber();
        this.cnvdNumber = entity.getCnvdNumber();
        this.cnnvdNumber = entity.getCnnvdNumber();
        this.cvssScore = entity.getCvssScore();
        this.patchSituation = entity.getPatchSituation();
        this.impactScope = entity.getImpactScope();
        this.solutionSuggestions = entity.getSolutionSuggestions();
        this.referenceLink = entity.getReferenceLink();
    }
}