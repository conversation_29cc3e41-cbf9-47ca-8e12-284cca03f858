package com.example.sys.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.commom.vo.Result;
import com.example.sys.annotation.OperationLog;
import com.example.sys.entity.Dictionary;
import com.example.sys.service.IDictionaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-10
 */
@RestController
@RequestMapping("/dictionary")
public class DictionaryController {
    @Autowired
    private IDictionaryService dictionaryService;

    @GetMapping("/list")
    public Result<Map<String, Object>> getDictionaryList(
            @RequestParam(value = "term", required = false) String term,
            @RequestParam(value = "form", required = false) String form,
            @RequestParam(value = "describe", required = false) String describe,
            @RequestParam(value = "pageNo") Long pageNo,
            @RequestParam(value = "pageSize") Long pageSize
    ) {
        LambdaQueryWrapper<Dictionary> wrapper = new LambdaQueryWrapper<>();

        if (term != null) {
            wrapper.eq(Dictionary::getTerm, term);
        }
        if (form != null) {
            wrapper.eq(Dictionary::getForm, form);
        }
        if (describe != null) {
            wrapper.eq(Dictionary::getDescibe, describe);
        }

        Page<Dictionary> page = new Page<>(pageNo, pageSize);//分页页数以及每页显示多少条
        Page<Dictionary> dictionaryPage = dictionaryService.page(page, wrapper);

        Map<String, Object> data = new HashMap<>();
        data.put("dictionary", dictionaryPage.getRecords());
        data.put("total", dictionaryPage.getTotal());
        data.put("pages", dictionaryPage.getPages());

        return Result.success(data);
    }

    // 新增字典
    @PostMapping("/add")
    @OperationLog(
            moduleName = "字典管理",
            operationType = "新增",
            desc = "'新增字典：' + #data.term",
            dataId = "#data.dictionaryId", // 保持与参数一致
            isSensitive = true
    )
    public Result<?> addDept(@RequestBody Dictionary dictionarys) {
        dictionaryService.save(dictionarys);
        return Result.success("新增成功");
    }

    // 删除字典
    @DeleteMapping("/{dictionaryId}")
    @OperationLog(
            moduleName = "字典管理",
            operationType = "删除",
            desc = "'删除字典：' + #data.term",
            dataId = "#data.dictionaryId", // 保持与参数一致
            isSensitive = true
    )
    public Result<?> delete(@PathVariable("dictionaryId") Integer dictionaryId) {
        Dictionary dictionary = dictionaryService.getById(dictionaryId);
        dictionaryService.removeById(dictionaryId);
        return Result.success(dictionary,"删除成功");
    }

    // 修改字典
    @PutMapping("/update")
    public Result<?> update(@RequestBody Dictionary dictionarys) {
        dictionaryService.updateById(dictionarys);
        return Result.success("修改成功");
    }

     // 查询单个字典
     @GetMapping("/{id}")
     @OperationLog(
             moduleName = "字典管理",
             operationType = "查询",
             desc = "'查询字典：' + #id",
             dataId = "#id"
     )
     public Result<Dictionary> getDectionayById(@PathVariable("id") Long id) {
         Dictionary dictionary = dictionaryService.getById(id);
         return Result.success(dictionary);
     }

     // 查询所有字典
     @GetMapping("/all")
     public Result<List<Dictionary>> getAllDictionary() {
         List<Dictionary> list = dictionaryService.list();
         return Result.success(list);
     }
}
