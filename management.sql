-- MySQL dump 10.13  Distrib 5.5.40, for Win64 (x86)
--
-- Host: localhost    Database: management
-- ------------------------------------------------------
-- Server version	5.5.40

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `role`
--

DROP TABLE IF EXISTS `role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `role` (
  `role_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `level` varchar(100) NOT NULL,
  UNIQUE KEY `Role_unique` (`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='系统权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role`
--

LOCK TABLES `role` WRITE;
/*!40000 ALTER TABLE `role` DISABLE KEYS */;
INSERT INTO `role` VALUES (1,'管理员','1'),(2,'普通用户','2');
/*!40000 ALTER TABLE `role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `task`
--

DROP TABLE IF EXISTS `task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `task` (
  `task_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `title` varchar(255) NOT NULL COMMENT '任务标题',
  `publisher` varchar(100) NOT NULL COMMENT '发布者',
  `due_date` date NOT NULL COMMENT '截止日期',
  `importance` tinyint(4) NOT NULL COMMENT '重要性(1-低,2-中,3-高)',
  `summary` text COMMENT '任务摘要',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `status` varchar(50) NOT NULL COMMENT '任务状态',
  `deleted` tinyint(4) DEFAULT '0' COMMENT '删除标记(0-未删除,1-已删除)',
  PRIMARY KEY (`task_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='任务信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `task`
--

LOCK TABLES `task` WRITE;
/*!40000 ALTER TABLE `task` DISABLE KEYS */;
INSERT INTO `task` VALUES (1,'项目需求分析','张经理','2023-08-15',3,'完成客户需求文档和功能规格说明书','2023-07-20 09:30:00','进行中',0),(2,'数据库设计评审','李总监','2023-08-05',2,'组织团队评审数据库ER图和表结构设计','2023-07-22 14:15:00','已完成',0),(3,'用户界面原型设计','王设计师','2023-08-10',2,'设计系统主要界面的原型和交互流程','2023-07-25 10:00:00','未开始',0),(4,'API接口开发','赵工程师','2023-08-25',3,'实现用户管理和权限控制的RESTful API','2023-07-18 16:45:00','进行中',0),(5,'系统测试计划','钱测试','2023-08-08',1,'编写测试用例和制定测试策略','2023-07-28 11:20:00','已暂停',0);
/*!40000 ALTER TABLE `task` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `permission` varchar(50) NOT NULL,
  `organization` varchar(100) NOT NULL,
  `time` varchar(100) NOT NULL,
  `reviewer` varchar(100) NOT NULL,
  `status` varchar(100) NOT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE KEY `email` (`email`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'大大怪','123456','<EMAIL>','管理员','武汉研究所','2019.9.1','张三','正常'),(2,'小小怪','123456','<EMAIL>','用户','湖北工业大学实验室','2020.9.9','大大怪','正常'),(3,'张三','123456','<EMAIL>','超级管理员','公司','2023.6.1','老板','正常');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `x_crypto_evaluation`
--

DROP TABLE IF EXISTS `x_crypto_evaluation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `x_crypto_evaluation` (
  `id` int(100) NOT NULL,
  `system_owner_org` varchar(100) DEFAULT NULL,
  `system_name` varchar(200) DEFAULT NULL,
  `business_type` varchar(50) DEFAULT NULL,
  `filing_number` varchar(50) DEFAULT NULL,
  `classification_level` varchar(50) DEFAULT NULL,
  `evaluation_time` date DEFAULT NULL,
  `evaluation_organization` varchar(100) DEFAULT NULL,
  `evaluation_status` varchar(20) DEFAULT NULL,
  `system_status` varchar(20) DEFAULT NULL,
  `category` varchar(50) DEFAULT NULL,
  `control_point` varchar(100) DEFAULT NULL,
  `evaluation_item` varchar(200) DEFAULT NULL,
  `result_record` text,
  `risk_level` varchar(10) DEFAULT NULL,
  `rectification_suggestion` text,
  `crypto_score` decimal(5,2) DEFAULT NULL,
  `estimated_score_after_fix` decimal(5,2) DEFAULT NULL,
  `evaluation_result` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `x_crypto_evaluation`
--

LOCK TABLES `x_crypto_evaluation` WRITE;
/*!40000 ALTER TABLE `x_crypto_evaluation` DISABLE KEYS */;
INSERT INTO `x_crypto_evaluation` VALUES (1,'辽宁核电','厂站监控系统','监控','LN2023-001','三级','2023-12-01','辽宁密码测评中心','已完成','运行中','物理和环境安全','身份鉴别','未采用密码技术进行物理访问身份鉴别','存在非法人员进入物理环境，对软硬件设备和数据进行直接破坏的风险','高','建议采用国家密码管理局认可的密码技术实现身份标识和鉴别信息绑定',1.00,2.00,'不符合'),(2,'辽宁核电','厂站监控系统','监控','LN2023-001','三级','2023-12-01','辽宁密码测评中心','已完成','运行中','物理和环境安全','身份鉴别','未采用密码技术进行物理访问身份鉴别','存在机房进出记录遭到篡改，以掩盖非法人员进出情况的风险','中','建议采用国家密码管理局认可的密码产品或技术对进出记录进行完整性保护',1.00,2.00,'符合'),(3,'辽宁核电','厂站监控系统','监控','LN2023-001','三级','2023-12-01','辽宁密码测评中心','已完成','运行中','物理和环境安全','身份鉴别','未采用密码技术进行物理访问身份鉴别','存在机房视频监控记录遭到篡改，以掩盖人员行为情况的风险','中','建议采用国家密码管理局认可的密码产品或技术对视频影像记录进行完整性保护',1.00,2.00,'不符合'),(4,'辽宁核电','厂站监控系统','监控','LN2023-001','三级','2023-12-01','辽宁密码测评中心','已完成','运行中','物理和环境安全','身份鉴别','未采用密码技术进行物理访问身份鉴别','无法保证使用的密码算法的可靠性，可能被恶意人员截获、破解','中','建议通信实体两端签发基于SM2WithSM3算法的数字证书',1.50,2.00,'符合'),(5,'辽宁核电','厂站监控系统','监控','LN2023-001','三级','2023-12-01','辽宁密码测评中心','已完成','运行中','物理和环境安全','身份鉴别','未采用密码技术进行物理访问身份鉴别','可能造成通信数据在通信过程中被非授权篡改的风险','高','建议采用基于SM3密码算法的HMAC或数字签名算法',1.50,2.00,'不符合'),(6,'辽宁核电','厂站监控系统','监控','LN2023-001','三级','2023-12-01','辽宁密码测评中心','已完成','运行中','物理和环境安全','身份鉴别','未采用密码技术进行物理访问身份鉴别','存在通信数据在信息系统外部被非授权的截取的风险','中','建议采用SM4密码算法保证通信过程中重要数据的机密性',1.50,2.00,'符合'),(7,'辽宁核电','厂站监控系统','监控','LN2023-001','三级','2023-12-01','辽宁密码测评中心','已完成','运行中','物理和环境安全','身份鉴别','未采用密码技术进行物理访问身份鉴别','可能被恶意篡改，并造成网络访问控制措施失效','中','建议利用具备商用密码产品认证证书的密码产品进行完整性保护',1.50,2.00,'不符合'),(8,'辽宁核电','厂站监控系统','监控','LN2023-001','三级','2023-12-01','辽宁密码测评中心','已完成','运行中','物理和环境安全','身份鉴别','未采用密码技术进行物理访问身份鉴别','可能导致身份冒用、鉴别信息泄露等安全事件的发生','高','建议采用动态口令机制、基于对称密码算法的消息鉴别码机制等',1.50,2.00,'符合'),(9,'辽宁核电','厂站监控系统','监控','LN2023-001','三级','2023-12-01','辽宁密码测评中心','已完成','运行中','物理和环境安全','身份鉴别','未采用密码技术进行物理访问身份鉴别','管理指令信息及其他敏感信息明文传输易被窃听','高','建议采用SSH、TLS等支持加密传输功能的远程访问协议',1.50,2.00,'不符合'),(10,'辽宁核电','厂站监控系统','监控','LN2023-001','三级','2023-12-01','辽宁密码测评中心','已完成','运行中','物理和环境安全','身份鉴别','未采用密码技术进行物理访问身份鉴别','存在应用系统被非法人员登录的可能','高','采用商用密码技术对登录用户进行身份鉴别',1.50,2.00,'符合'),(1750003713,'辽宁核电','厂站监控系统','监控','LN2023-001','三级','2023-12-01','辽宁密码测评中心','已完成','运行中','物理和环境安全','身份鉴别','未采用密码技术进行物理访问身份鉴别。','存在非法人员进入物理环境，对软硬件设备和数据进行直接破坏的风险。','高','建议采用国家密码管理局认可的密码技术实现身份标识和鉴别信息绑定',1.00,2.00,'不符合');
/*!40000 ALTER TABLE `x_crypto_evaluation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `x_dept`
--

DROP TABLE IF EXISTS `x_dept`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `x_dept` (
  `dept_id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `pid` int(11) DEFAULT NULL COMMENT '上级部门',
  `name` varchar(255) NOT NULL COMMENT '名称',
  `status` varchar(1) NOT NULL COMMENT '状态',
  `phone` int(11) NOT NULL COMMENT '联系电话',
  `address` varchar(100) DEFAULT NULL COMMENT '地址',
  `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '删除标签',
  PRIMARY KEY (`dept_id`) USING BTREE,
  KEY `inx_enabled` (`status`) USING BTREE,
  KEY `inx_pid` (`pid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='部门';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `x_dept`
--

LOCK TABLES `x_dept` WRITE;
/*!40000 ALTER TABLE `x_dept` DISABLE KEYS */;
INSERT INTO `x_dept` VALUES (2,7,'研发部','1',123456789,'北京',0),(5,7,'运维部','1',123456789,'上海',0),(6,8,'测试部','1',123456789,'广州',0),(7,NULL,'华南分部','1',123456789,'天津',0),(8,NULL,'华南分部','1',123456789,'天津',0),(15,8,'UI部门','1',123456789,'青岛',0),(17,2,'研发一组','1',123456789,'重庆',0),(18,15,'新部门','1',1234567564,'上饶',0),(19,7,'test','1',123456,'上饶',1);
/*!40000 ALTER TABLE `x_dept` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `x_dictionary`
--

DROP TABLE IF EXISTS `x_dictionary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `x_dictionary` (
  `diction_id` int(11) NOT NULL AUTO_INCREMENT,
  `term` varchar(255) NOT NULL,
  `form` varchar(255) NOT NULL,
  `descibe` varchar(255) DEFAULT NULL,
  `delate` int(11) NOT NULL COMMENT '是否删除',
  PRIMARY KEY (`diction_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `x_dictionary`
--

LOCK TABLES `x_dictionary` WRITE;
/*!40000 ALTER TABLE `x_dictionary` DISABLE KEYS */;
INSERT INTO `x_dictionary` VALUES (1,'用户ID','UUID','用户唯一标识符',0),(2,'IP地址','字符串','用户登录系统时所使用的网络地址',0);
/*!40000 ALTER TABLE `x_dictionary` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `x_files`
--

DROP TABLE IF EXISTS `x_files`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `x_files` (
  `file_id` int(11) NOT NULL AUTO_INCREMENT,
  `task_id` int(11) DEFAULT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_type` varchar(50) DEFAULT NULL,
  `file_path` varchar(255) NOT NULL,
  `upload_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '删除标签',
  PRIMARY KEY (`file_id`) USING BTREE,
  KEY `task_id` (`task_id`) USING BTREE,
  CONSTRAINT `x_files_ibfk_1` FOREIGN KEY (`task_id`) REFERENCES `task` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `x_files`
--

LOCK TABLES `x_files` WRITE;
/*!40000 ALTER TABLE `x_files` DISABLE KEYS */;
/*!40000 ALTER TABLE `x_files` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `x_logs`
--

DROP TABLE IF EXISTS `x_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `x_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '操作日志的唯一标识，采用自增方式生成，用于唯一区分每条日志记录',
  `username` varchar(255) DEFAULT NULL COMMENT '执行操作的用户的用户名，可用于追溯操作人',
  `realname` varchar(255) DEFAULT NULL COMMENT '执行操作的用户的真实姓名，辅助识别用户身份',
  `module_name` varchar(255) NOT NULL COMMENT '操作所属的系统模块，例如用户管理、商品管理等，便于对操作进行分类统计和分析',
  `operation_type` varchar(50) NOT NULL COMMENT '具体的操作类型，如登录、创建、删除、更新等，描述操作的性质',
  `operation_description` text COMMENT '对操作的详细描述，记录操作的具体内容和相关参数，帮助理解操作细节',
  `operation_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作发起的时间，精确到秒，记录操作发生的具体时刻',
  `operation_ip` varchar(45) DEFAULT NULL COMMENT '操作发起时的 IP 地址，用于安全审计和追踪异常操作',
  `device_info` varchar(255) DEFAULT NULL COMMENT '操作所使用的设备信息，包括浏览器类型、操作系统等，有助于了解操作环境',
  `related_data_id` bigint(20) DEFAULT NULL COMMENT '若操作涉及具体的数据记录，此为关联数据的 ID，方便进行关联查询',
  `operation_result` varchar(20) NOT NULL COMMENT '操作的结果，如成功、失败等，可根据实际情况自定义结果状态',
  `error_message` text COMMENT '当操作失败时，记录具体的错误信息，便于排查问题',
  `is_sensitive` tinyint(1) DEFAULT '0' COMMENT '标记该操作是否为敏感操作，可用于特殊监控和处理',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=377 DEFAULT CHARSET=utf8 COMMENT='用于记录管理系统中各种操作的详细日志信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `x_logs`
--

LOCK TABLES `x_logs` WRITE;
/*!40000 ALTER TABLE `x_logs` DISABLE KEYS */;
INSERT INTO `x_logs` VALUES (1,'admin','张山','用户管理','更新','更新用户','2025-04-14 04:23:00','127.0.0.1','chorm',2,'成功',NULL,0),(2,NULL,NULL,'用户管理','登录','用户登录：{#user.getUsername()}','2025-04-15 08:38:13','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'失败','Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379',1),(3,NULL,NULL,'用户管理','删除','删除用户：{#user.getUsername()}','2025-04-15 08:38:49','0:0:0:0:0:0:0:1','Unknown null/Unknown',8,'成功',NULL,1),(4,NULL,NULL,'用户管理','删除','删除用户：{#user.getUsername()}','2025-04-15 08:40:02','0:0:0:0:0:0:0:1','Unknown null/Unknown',8,'成功',NULL,1),(5,NULL,NULL,'用户管理','删除','删除用户：#user.getUsername()','2025-04-15 08:45:24','0:0:0:0:0:0:0:1','Unknown null/Unknown',8,'成功',NULL,1),(6,NULL,NULL,'用户管理','删除','删除用户：#data.username','2025-04-15 08:59:07','0:0:0:0:0:0:0:1','Unknown null/Unknown',8,'成功',NULL,1),(7,NULL,NULL,'用户管理','删除','删除用户：#data.username','2025-04-15 09:07:53','0:0:0:0:0:0:0:1','Unknown null/Unknown',8,'成功',NULL,1),(8,NULL,NULL,'用户管理','删除','删除用户：#data.username','2025-04-15 09:13:07','0:0:0:0:0:0:0:1','Unknown null/Unknown',8,'成功',NULL,1),(9,NULL,NULL,'用户管理','删除','删除用户：#data.username','2025-04-15 09:15:54','0:0:0:0:0:0:0:1','Unknown null/Unknown',8,'成功',NULL,1),(10,NULL,NULL,'用户管理','删除','删除用户：hzh','2025-04-15 09:20:03','0:0:0:0:0:0:0:1','Unknown null/Unknown',8,'成功',NULL,1),(11,NULL,NULL,'用户管理','登录','用户登录：{#user.getUsername()}','2025-04-15 09:21:58','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(12,NULL,NULL,'用户管理','删除','删除用户：hzh','2025-04-15 09:26:26','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',8,'成功',NULL,1),(13,NULL,NULL,'用户管理','登录','\'用户登录：\' + #data.username','2025-04-15 09:40:02','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(14,NULL,NULL,'用户管理','登录','\'用户登录：\' + #data.username','2025-04-15 09:44:10','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(15,NULL,NULL,'用户管理','退出','\'用户退出：\' + #data.token','2025-04-15 09:45:33','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(16,NULL,NULL,'用户管理','新增','\'添加用户：\' + #data.username','2025-04-15 09:48:35','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'失败','\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'role\' doesn\'t have a default value\r\n### The error may exist in com/example/sys/mapper/UserMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.UserMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_user  ( username, password, realname, email )  VALUES  ( ?, ?, ?, ? )\r\n### Cause: java.sql.SQLException: Field \'role\' doesn\'t have a default value\n; Field \'role\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'role\' doesn\'t have a default value',1),(17,NULL,NULL,'用户管理','新增','\'添加用户：\' + #data.username','2025-04-15 09:52:43','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'失败','\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'role\' doesn\'t have a default value\r\n### The error may exist in com/example/sys/mapper/UserMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.UserMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_user  ( username, password, realname, email )  VALUES  ( ?, ?, ?, ? )\r\n### Cause: java.sql.SQLException: Field \'role\' doesn\'t have a default value\n; Field \'role\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'role\' doesn\'t have a default value',1),(18,NULL,NULL,'用户管理','修改','修改用户：updated_user','2025-04-15 09:56:14','0:0:0:0:0:0:0:1','Unknown null/Unknown',7,'成功',NULL,0),(19,NULL,NULL,'用户管理','删除','删除用户：hzh','2025-04-15 09:57:02','0:0:0:0:0:0:0:1','Unknown null/Unknown',8,'成功',NULL,1),(20,NULL,NULL,'用户管理','新增','\'添加用户：\' + #data.username','2025-04-15 10:01:19','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'失败','\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'role\' doesn\'t have a default value\r\n### The error may exist in com/example/sys/mapper/UserMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.UserMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_user  ( username, password )  VALUES  ( ?, ? )\r\n### Cause: java.sql.SQLException: Field \'role\' doesn\'t have a default value\n; Field \'role\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'role\' doesn\'t have a default value',1),(21,NULL,NULL,'用户管理','新增','\'添加用户：\' + #data.username','2025-04-15 10:03:23','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'失败','\r\n### Error updating database.  Cause: java.sql.SQLException: Field \'role\' doesn\'t have a default value\r\n### The error may exist in com/example/sys/mapper/UserMapper.java (best guess)\r\n### The errorx_sys_protect may involve com.example.sys.mapper.UserMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_user  ( username, password )  VALUES  ( ?, ? )\r\n### Cause: java.sql.SQLException: Field \'role\' doesn\'t have a default value\n; Field \'role\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'role\' doesn\'t hawn null/Unknown',1),(35,NULL,NULL,'用户管理','删除','删除用户：hzh','2025-04-15 11:22:29','0:0:0:0:0:0:0:1','Unknown null/Unknown',8,'成功',NULL,1),(36,NULL,NULL,'用户管理','新增','添加用户：test2','2025-04-15 11:23:04','0:0:0:0:0:0:0:1','Unknown null/Unknown',10,'成功',NULL,1),(37,NULL,NULL,'用户管理','查询','查询用户信息：user:061fdb93-84d3-4031-89e2-463f4a53eabf','2025-04-15 11:35:22','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(38,NULL,NULL,'用户管理','登录','用户登录：','2025-04-15 11:35:24','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(39,NULL,NULL,'用户管理','查询','查询用户信息：user:f8c59c66-92d2-4366-b6ec-07341da1c2e7','2025-04-15 11:35:25','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(40,NULL,NULL,'用户管理','删除','删除用户：test2','2025-04-15 11:36:33','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',10,'成功',NULL,1),(41,NULL,NULL,'用户管理','退出','用户退出：','2025-04-15 11:41:42','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(42,NULL,NULL,'用户管理','登录','用户登录：','2025-04-15 11:41:44','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(43,NULL,NULL,'用户管理','查询','查询用户信息：user:1525966f-78d5-4020-9dff-07ec9e8ea71c','2025-04-15 11:41:44','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(44,NULL,NULL,'用户管理','删除','删除用户：test','2025-04-15 11:41:50','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',9,'成功',NULL,1),(45,NULL,NULL,'用户管理','登录','用户登录：','2025-04-15 11:50:14','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(46,NULL,NULL,'用户管理','登录','用户登录：','2025-04-15 12:03:06','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(47,NULL,NULL,'用户管理','登录','用户登录：','2025-04-15 12:18:20','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(48,NULL,NULL,'用户管理','登录','用户登录：','2025-04-15 12:20:43','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(49,'admin',' admin','用户管理','删除','删除用户：hzh','2025-04-15 12:20:48','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',8,'成功',NULL,1),(50,'admin',' admin','用户管理','查询','查询用户详情：ID=7','2025-04-15 12:21:06','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',7,'成功',NULL,0),(51,NULL,NULL,'用户管理','登录','用户登录：','2025-04-15 12:24:32','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(52,'admin',' admin','用户管理','新增','添加用户：test3','2025-04-15 12:25:05','0:0:0:0:0:0:0:1','Unknown null/Unknown',11,'成功',NULL,1),(53,NULL,NULL,'用户管理','退出','用户退出：','2025-04-15 12:28:42','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(54,NULL,NULL,'用户管理','登录','\'用户登录：\'+ #data.username','2025-04-15 12:28:52','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(55,NULL,NULL,'用户管理','退出','用户退出：','2025-04-15 12:30:28','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(56,NULL,NULL,'用户管理','登录','\'用户登录：\'+ #user.username','2025-04-15 12:30:30','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(57,NULL,NULL,'用户管理','退出','用户退出：','2025-04-15 12:35:22','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(58,'admin',NULL,'用户管理','登录','用户登录：admin','2025-04-15 12:35:36','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(59,NULL,NULL,'用户管理','退出','用户退出：','2025-04-15 12:35:47','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(60,'admin',' admin','用户管理','登录','用户登录：admin','2025-04-15 12:43:22','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(61,NULL,NULL,'用户管理','退出','用户退出：','2025-04-15 12:43:33','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(62,'admin',' admin','用户管理','登录','用户登录：admin','2025-04-15 12:43:45','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(63,'admin',' admin','用户管理','删除','\'删除用户：\' + #data.username','2025-04-15 12:43:52','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',8,'成功',NULL,1),(64,'admin',' admin','用户管理','登录','用户登录：admin','2025-04-15 12:46:01','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(65,'admin',' admin','用户管理','登录','用户登录：admin','2025-04-15 12:46:19','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(66,'admin',' admin','用户管理','删除','\'删除用户：\' + #data.username','2025-04-15 12:48:08','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',8,'成功',NULL,1),(67,'admin',' admin','用户管理','删除','删除用户：hzh','2025-04-15 12:57:47','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',8,'成功',NULL,1),(68,NULL,NULL,'用户管理','登录','\'用户登录：\'+ #user.username','2025-04-15 12:58:15','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(69,'admin',' admin','用户管理','新增','添加用户：test3','2025-04-15 12:58:34','0:0:0:0:0:0:0:1','Unknown null/Unknown',12,'成功',NULL,1),(70,NULL,NULL,'用户管理','登录','\'用户登录：\'+ #user.username','2025-04-15 12:59:00','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(71,NULL,NULL,'用户管理','登录','\'用户登录：\'+ #user.username','2025-04-15 13:09:28','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(72,NULL,NULL,'用户管理','删除','删除用户：test3','2025-04-15 13:09:52','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',12,'成功',NULL,1),(73,'admin',' admin','用户管理','登录','用户登录：admin','2025-04-15 13:16:31','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(74,'admin',' admin','用户管理','删除','\'删除用户：\' + #data.username','2025-04-15 13:16:39','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',12,'成功',NULL,1),(75,'admin',' admin','用户管理','删除','\'删除用户：\' + #data.username','2025-04-15 13:25:19','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',8,'成功',NULL,1),(76,'admin',' admin','用户管理','登录','用户登录：admin','2025-04-15 13:26:08','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(77,'admin',' admin','用户管理','删除','删除用户：test3','2025-04-15 13:26:11','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',12,'成功',NULL,1),(78,'admin',' admin','用户管理','登录','用户登录：admin','2025-04-15 13:26:43','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(79,'admin',' admin','用户管理','新增','添加用户：test3','2025-04-15 13:26:55','0:0:0:0:0:0:0:1','Unknown null/Unknown',13,'成功',NULL,1),(80,'admin',' admin','用户管理','登录','用户登录：admin','2025-04-15 13:40:25','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(81,'hzh','付九','用户管理','登录','用户登录：hzh','2025-04-16 01:36:56','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(82,'hzh','付九','用户管理','登录','用户登录：hzh','2025-04-16 01:37:27','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(83,'hzh','付九','部门管理','修改','\'修改部门：\' + #data.name','2025-04-16 01:37:59','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(84,'hzh','付九','部门管理','修改','修改部门：华南分部','2025-04-16 01:39:44','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(85,'hzh','付九','部门管理','删除','删除部门：新部门','2025-04-16 01:41:03','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(86,'hzh','付九','部门管理','修改','修改部门：test','2025-04-16 01:45:20','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(87,'hzh','付九','部门管理','删除','删除部门：test','2025-04-16 01:45:22','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(88,'hzh','付九','用户管理','删除','删除用户：test3','2025-04-16 01:46:41','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',13,'成功',NULL,1),(89,'hzh','付九','部门管理','删除','删除部门：test','2025-04-16 01:48:31','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',19,'成功',NULL,1),(90,'hzh','付九','部门管理','修改','修改部门：运维一组','2025-04-16 01:50:11','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',11,'成功',NULL,1),(91,'hzh','付九','部门管理','修改','修改部门：1','2025-04-16 01:51:39','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',20,'成功',NULL,1),(92,'aaa','胡八','用户管理','登录','用户登录：aaa','2025-04-16 01:53:37','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(93,'aaa','胡八','部门管理','新增','新增部门：运维一组','2025-04-16 01:56:33','0:0:0:0:0:0:0:1','Unknown null/Unknown',20,'成功',NULL,1),(94,'aaa','胡八','用户管理','登录','用户登录：aaa','2025-04-16 02:09:45','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(95,'aaa','胡八','文件管理','上传','上传文件：file','2025-04-16 02:12:32','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,0),(96,'aaa','胡八','文件管理','上传','上传文件：2.21系统流程图.png','2025-04-16 02:26:07','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,0),(97,'aaa','胡八','文件管理','上传','上传文件：2.21系统流程图.png','2025-04-16 02:27:02','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,0),(98,'aaa','胡八','文件管理','上传','上传文件：2.21系统流程图.png','2025-04-16 02:31:09','0:0:0:0:0:0:0:1','Unknown null/Unknown',26,'成功',NULL,0),(99,'aaa','胡八','文件管理','下载','\'下载文件：\' + @com.example.sys.service.IFilesService.getFileNameByFileId(#fileId)','2025-04-16 02:33:20','0:0:0:0:0:0:0:1','Unknown null/Unknown',26,'成功',NULL,0),(100,'aaa','胡八','文件管理','删除','\'删除文件：\' + @com.example.sys.service.IFilesService.getFileNameByFileId(#fileId)','2025-04-16 02:33:54','0:0:0:0:0:0:0:1','Unknown null/Unknown',26,'成功',NULL,0),(101,'aaa','胡八','文件管理','上传','上传文件：2.21系统流程图.png','2025-04-16 02:37:46','0:0:0:0:0:0:0:1','Unknown null/Unknown',27,'成功',NULL,0),(102,'aaa','胡八','文件管理','下载','\'下载文件：\' + @filesServiceImpl.getFileNameByFileId(#fileId)','2025-04-16 02:38:12','0:0:0:0:0:0:0:1','Unknown null/Unknown',27,'成功',NULL,0),(103,NULL,NULL,'文件管理','删除','删除文件：','2025-04-16 02:40:06','0:0:0:0:0:0:0:1','Unknown null/Unknown',27,'成功',NULL,0),(104,NULL,NULL,'任务管理','新增','新增任务：季度总结会议','2025-04-16 02:44:20','0:0:0:0:0:0:0:1','Unknown null/Unknown',3,'成功',NULL,1),(105,'aaa','胡八','用户管理','登录','用户登录：aaa','2025-04-16 02:44:50','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(106,'aaa','胡八','任务管理','新增','新增任务：季度总结会议','2025-04-16 02:45:23','0:0:0:0:0:0:0:1','Unknown null/Unknown',4,'成功',NULL,1),(107,'aaa','胡八','任务管理','删除','删除任务：季度总结会议','2025-04-16 02:46:13','0:0:0:0:0:0:0:1','Unknown null/Unknown',4,'成功',NULL,1),(108,'aaa','胡八','任务管理','修改','修改任务：null','2025-04-16 02:46:57','0:0:0:0:0:0:0:1','Unknown null/Unknown',1,'成功',NULL,0),(109,'aaa','胡八','任务管理','修改','修改任务：null','2025-04-16 02:49:29','0:0:0:0:0:0:0:1','Unknown null/Unknown',1,'成功',NULL,0),(110,'aaa','胡八','部门管理','修改','修改部门：null','2025-04-16 02:54:28','0:0:0:0:0:0:0:1','Unknown null/Unknown',2,'成功',NULL,1),(111,'admin',' admin','用户管理','登录','用户登录：admin','2025-04-16 03:08:41','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',NULL,'成功',NULL,1),(112,'admin',' admin','部门管理','修改','修改部门：运维一组','2025-04-16 03:09:04','0:0:0:0:0:0:0:1','Chrome 13 *********/Windows 10',20,'成功',NULL,1),(113,'aaa','胡八','用户管理','登录','用户登录：aaa','2025-04-16 10:06:30','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'失败','Unable to connect to Redis; nested exception is io.lettuce.core.RedisConnectionException: Unable to connect to localhost:6379',1),(114,'aaa','胡八','用户管理','登录','用户登录：aaa','2025-04-16 10:07:10','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(115,'aaa','胡八','日志管理','导出','导出Excel格式日志数据','2025-04-16 10:07:39','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(116,'aaa','胡八','日志管理','导出','导出Excel格式日志数据','2025-04-16 10:08:47','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(117,'aaa','胡八','日志管理','导出','导出日志数据','2025-04-16 10:09:25','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(118,'aaa','胡八','用户管理','登录','用户登录：aaa','2025-04-17 08:51:33','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(119,'aaa','胡八','日志管理','导出','导出日志数据','2025-04-17 08:51:54','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(120,'aaa','胡八','日志管理','导出','导出Excel格式日志数据','2025-04-17 08:53:45','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,1),(121,NULL,NULL,'等保管理','修改','\'修改等保：\' + #data.systemName','2025-04-29 07:40:14','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,0),(122,NULL,NULL,'等保管理','修改','\'修改等保：\' + #data.systemName','2025-04-29 07:40:50','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,0),(123,NULL,NULL,'等保管理','修改','\'修改等保：\' + #data.systemName','2025-04-29 07:41:46','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,0),(124,NULL,NULL,'等保管理','修改','\'修改等保：\' + #data.systemName','2025-04-29 07:42:22','0:0:0:0:0:0:0:1','Unknown null/Unknown',NULL,'成功',NULL,0),(125,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-12 23:20:34','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(126,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-12 23:59:22','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(127,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-13 00:39:50','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(128,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-13 03:27:08','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(129,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-13 03:59:26','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(130,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-13 04:40:11','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(131,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-13 05:46:21','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(132,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-13 06:15:05','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(133,'admin',' admin','等保管理','修改','\'修改等保：\' + #data.systemName','2025-06-13 06:15:18','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(134,'admin',' admin','等保管理','删除','删除等保：111','2025-06-13 06:16:09','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',11,'成功',NULL,1),(135,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-13 06:38:46','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(136,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-13 07:07:12','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(137,'admin',' admin','等保管理','修改','\'修改等保：\' + #data.systemName','2025-06-13 07:07:40','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(138,'admin',' admin','等保管理','删除','删除等保：111','2025-06-13 07:07:55','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',12,'成功',NULL,1),(139,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-16 03:32:10','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(140,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-16 06:46:56','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(141,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-16 08:43:16','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(142,'admin',' admin','等保管理','导出','导出等保','2025-06-16 08:45:38','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(143,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-18 00:51:03','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(144,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-18 05:35:39','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(145,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-18 06:18:38','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(146,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-19 05:16:34','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(147,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-19 07:08:07','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(148,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-20 00:58:01','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(149,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-20 05:24:00','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(150,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-20 07:01:20','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(151,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-20 07:44:38','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(152,'admin',' admin','等保管理','导出','导出等保','2025-06-20 07:53:25','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(153,'admin',' admin','等保管理','导出','导出等保','2025-06-20 08:02:49','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(154,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-20 09:09:27','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(155,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-23 00:50:41','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(156,NULL,NULL,'等保管理','导出','导出等保','2025-06-23 07:12:54','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(157,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-23 08:08:31','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(158,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-23 08:50:09','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(159,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-24 00:45:11','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(160,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-24 02:13:25','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(161,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-24 05:18:16','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(162,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-24 05:51:57','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(163,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-24 08:13:48','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(164,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-25 00:37:05','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(165,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-25 01:16:57','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(166,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-25 01:51:30','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(167,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-25 02:34:31','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(168,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-25 03:07:24','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(169,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-25 05:14:11','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(170,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-25 05:47:48','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(171,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-25 05:53:49','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(172,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-25 06:41:29','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(173,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-25 08:11:11','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(174,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-25 08:40:22','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(175,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-26 00:40:01','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(176,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-26 01:51:42','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(177,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-26 02:30:28','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(178,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 02:30:46','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(179,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-26 02:31:08','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'失败','\r\n### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Incorrect date value: \'\' for column \'planned_evaluation_time\' at row 1\r\n### The error may exist in com/example/sys/mapper/SysProtectMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.SysProtectMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_sys_protect  ( system_owner_org, member_unit, system_name, system_short_name, network_belonging, business_type, filing_number, classification_level, evaluation_time, evaluation_result, planned_evaluation_time, evaluation_organization, evaluation_status, system_status, category, control_point, evaluation_item, compliance_status )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )\r\n### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Incorrect date value: \'\' for column \'planned_evaluation_time\' at row 1\n; Data truncation: Incorrect date value: \'\' for column \'planned_evaluation_time\' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Incorrect date value: \'\' for column \'planned_evaluation_time\' at row 1',1),(180,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 02:32:47','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(181,NULL,NULL,'等保管理','导出','导出等保','2025-06-26 02:35:45','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(182,NULL,NULL,'等保管理','导出','导出等保','2025-06-26 02:36:22','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(183,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 02:36:53','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(184,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-26 02:37:04','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(185,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-26 03:04:34','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(186,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-26 03:36:50','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(187,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-26 05:08:37','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(188,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 05:52:09','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(189,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 05:53:02','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(190,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 06:00:03','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(191,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 06:02:27','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(192,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 06:06:57','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(193,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-26 06:22:37','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(194,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 06:23:33','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(195,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-26 06:23:55','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'失败','\r\n### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\r\n### The error may exist in com/example/sys/mapper/SysProtectMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.SysProtectMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_sys_protect  ( application_id, system_owner_org, member_unit, system_name, system_short_name, network_belonging, business_type, filing_number, classification_level, evaluation_time, evaluation_result, planned_evaluation_time, evaluation_organization, evaluation_status, system_status, category, control_point, evaluation_item, compliance_status, result_record )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )\r\n### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\n; Duplicate entry \'1\' for key \'PRIMARY\'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'',1),(196,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-26 06:37:04','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(197,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 06:37:22','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(198,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 06:42:21','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(199,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-26 07:25:32','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(200,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 07:25:53','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(201,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 07:27:23','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(202,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 07:36:03','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(203,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-26 07:36:13','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(204,'admin',' admin','等保管理','删除','删除等保：秦山核电生产管理系统','2025-06-26 07:36:22','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',21,'成功',NULL,1),(205,'admin',' admin','等保管理','删除','删除等保：秦山核电生产管理系统','2025-06-26 07:36:40','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',22,'成功',NULL,1),(206,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 07:39:42','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(207,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 07:41:09','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(208,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-26 08:03:17','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(209,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 08:03:29','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(210,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-26 08:06:36','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(211,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 08:06:46','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(212,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-26 08:35:51','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(213,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 08:36:02','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(214,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-26 08:49:42','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(215,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 08:49:53','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(216,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-26 08:50:05','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'失败','\r\n### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\r\n### The error may exist in com/example/sys/mapper/SysProtectMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.SysProtectMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_sys_protect  ( application_id, system_owner_org, member_unit, system_name, system_short_name, network_belonging, business_type, filing_number, classification_level, evaluation_time, evaluation_result, planned_evaluation_time, evaluation_organization, evaluation_status, system_status, category, control_point, evaluation_item, compliance_status, result_record )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )\r\n### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\n; Duplicate entry \'1\' for key \'PRIMARY\'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'',1),(217,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 09:03:57','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(218,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-26 09:03:59','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'失败','\r\n### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'17\' for key \'PRIMARY\'\r\n### The error may exist in com/example/sys/mapper/SysProtectMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.SysProtectMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_sys_protect  ( application_id, system_owner_org, member_unit, system_name, system_short_name, network_belonging, business_type, filing_number, classification_level, evaluation_time, evaluation_result, planned_evaluation_time, evaluation_organization, evaluation_status, system_status, category, control_point, evaluation_item, compliance_status, result_record )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )\r\n### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'17\' for key \'PRIMARY\'\n; Duplicate entry \'17\' for key \'PRIMARY\'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'17\' for key \'PRIMARY\'',1),(219,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 09:07:06','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(220,NULL,NULL,'等保管理','导入','导入等保','2025-06-26 09:09:41','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(221,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-26 09:09:43','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'失败','\r\n### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\r\n### The error may exist in com/example/sys/mapper/SysProtectMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.SysProtectMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_sys_protect  ( application_id, system_owner_org, member_unit, system_name, system_short_name, network_belonging, business_type, filing_number, classification_level, evaluation_time, evaluation_result, planned_evaluation_time, evaluation_organization, evaluation_status, system_status, category, control_point, evaluation_item, compliance_status, result_record )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )\r\n### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\n; Duplicate entry \'1\' for key \'PRIMARY\'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'',1),(222,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-27 00:42:47','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(223,NULL,NULL,'等保管理','导出','导出等保','2025-06-27 01:03:46','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(224,NULL,NULL,'等保管理','导入','导入等保','2025-06-27 01:03:59','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(225,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-27 01:04:04','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(226,NULL,NULL,'等保管理','导入','导入等保','2025-06-27 01:04:27','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(227,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-27 01:04:30','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'失败','\r\n### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'21\' for key \'PRIMARY\'\r\n### The error may exist in com/example/sys/mapper/SysProtectMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.SysProtectMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_sys_protect  ( application_id, system_owner_org, member_unit, system_name, system_short_name, network_belonging, business_type, filing_number, classification_level, evaluation_time, evaluation_result, planned_evaluation_time, evaluation_organization, evaluation_status, system_status, category, control_point, evaluation_item, compliance_status, result_record )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )\r\n### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'21\' for key \'PRIMARY\'\n; Duplicate entry \'21\' for key \'PRIMARY\'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'21\' for key \'PRIMARY\'',1),(228,'admin',' admin','等保管理','删除','删除等保：秦山核电生产管理系统','2025-06-27 01:04:42','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',21,'成功',NULL,1),(229,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-27 01:32:13','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(230,NULL,NULL,'等保管理','导入','导入等保','2025-06-27 01:32:34','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(231,NULL,NULL,'等保管理','导入','导入等保','2025-06-27 01:36:32','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,0),(232,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-27 01:36:38','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'失败','\r\n### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\r\n### The error may exist in com/example/sys/mapper/SysProtectMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.SysProtectMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_sys_protect  ( application_id, system_owner_org, member_unit, system_name, system_short_name, network_belonging, business_type, filing_number, classification_level, evaluation_time, evaluation_result, planned_evaluation_time, evaluation_organization, evaluation_status, system_status, category, control_point, evaluation_item, compliance_status, result_record )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )\r\n### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\n; Duplicate entry \'1\' for key \'PRIMARY\'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'',1),(233,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-27 06:49:40','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(234,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-27 07:04:30','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(235,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-27 08:02:46','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',NULL,'成功',NULL,1),(236,NULL,NULL,'部门管理','修改','修改部门：华南分部','2025-06-27 08:43:56','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',8,'成功',NULL,1),(237,NULL,NULL,'部门管理','修改','修改部门：华南分部','2025-06-27 08:44:09','0:0:0:0:0:0:0:1','Chrome 13 137.0.0.0/Windows 10',9,'成功',NULL,1),(238,NULL,NULL,'用户管理','登录','用户登录：admin1','2025-06-30 00:43:43','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(239,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-30 00:43:49','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(240,NULL,NULL,'等保管理','导出','导出等保','2025-06-30 01:44:28','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(241,NULL,NULL,'等保管理','导入','导入等保','2025-06-30 01:44:41','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(242,NULL,NULL,'等保管理','导入','导入等保','2025-06-30 01:45:28','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(243,NULL,NULL,'等保管理','导入','导入等保','2025-06-30 01:45:54','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(244,NULL,NULL,'等保管理','导入','导入等保','2025-06-30 01:52:39','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(245,NULL,NULL,'等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-30 01:52:41','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(246,NULL,NULL,'等保管理','导入','导入等保','2025-06-30 01:54:02','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(247,NULL,NULL,'等保管理','导出','导出等保','2025-06-30 01:58:10','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(248,NULL,NULL,'等保管理','导出','导出等保','2025-06-30 01:58:22','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(249,NULL,NULL,'等保管理','修改','\'修改等保：\' + #data.systemName','2025-06-30 02:09:05','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(250,NULL,NULL,'等保管理','导入','导入等保','2025-06-30 02:14:10','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(251,NULL,NULL,'等保管理','删除','删除等保：秦山核电生产管理系统','2025-06-30 02:25:39','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',22,'成功',NULL,1),(252,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-30 05:19:09','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(253,NULL,NULL,'等保管理','导入','导入等保','2025-06-30 05:19:25','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(254,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-30 05:19:50','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'失败','\r\n### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\r\n### The error may exist in com/example/sys/mapper/SysProtectMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.SysProtectMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_sys_protect  ( application_id, system_owner_org, member_unit, system_name, system_short_name, network_belonging, business_type, filing_number, classification_level, evaluation_time, evaluation_result, planned_evaluation_time, evaluation_organization, evaluation_status, system_status, category, control_point, evaluation_item, compliance_status, result_record )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )\r\n### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\n; Duplicate entry \'1\' for key \'PRIMARY\'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'',1),(255,NULL,NULL,'等保管理','导入','导入等保','2025-06-30 05:20:48','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(256,NULL,NULL,'等保管理','导入','导入等保','2025-06-30 05:24:39','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(257,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-30 05:24:42','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'失败','\r\n### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'17\' for key \'PRIMARY\'\r\n### The error may exist in com/example/sys/mapper/SysProtectMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.SysProtectMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_sys_protect  ( application_id, system_owner_org, member_unit, system_name, system_short_name, network_belonging, business_type, filing_number, classification_level, evaluation_time, evaluation_result, planned_evaluation_time, evaluation_organization, evaluation_status, system_status, category, control_point, evaluation_item, compliance_status, result_record )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )\r\n### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'17\' for key \'PRIMARY\'\n; Duplicate entry \'17\' for key \'PRIMARY\'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'17\' for key \'PRIMARY\'',1),(258,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-30 05:26:33','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(259,NULL,NULL,'等保管理','导入','导入等保','2025-06-30 05:26:53','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(260,NULL,NULL,'等保管理','导入','导入等保','2025-06-30 05:32:10','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(261,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-30 05:33:03','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'失败','\r\n### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\r\n### The error may exist in com/example/sys/mapper/SysProtectMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.SysProtectMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_sys_protect  ( application_id, system_owner_org, member_unit, system_name, system_short_name, network_belonging, business_type, filing_number, classification_level, evaluation_time, evaluation_result, planned_evaluation_time, evaluation_organization, evaluation_status, system_status, category, control_point, evaluation_item, compliance_status, result_record )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )\r\n### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\n; Duplicate entry \'1\' for key \'PRIMARY\'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'',1),(262,NULL,NULL,'等保管理','导入','导入等保','2025-06-30 05:34:01','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(263,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-30 06:48:51','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(264,NULL,NULL,'等保管理','导入','导入PDF测评报告','2025-06-30 06:48:59','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(265,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-30 06:49:02','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'失败','\r\n### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\r\n### The error may exist in com/example/sys/mapper/SysProtectMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.SysProtectMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_sys_protect  ( application_id, system_owner_org, member_unit, system_name, system_short_name, network_belonging, business_type, filing_number, classification_level, evaluation_time, evaluation_result, planned_evaluation_time, evaluation_organization, evaluation_status, system_status, category, control_point, evaluation_item, compliance_status, result_record )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )\r\n### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\n; Duplicate entry \'1\' for key \'PRIMARY\'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'',1),(266,NULL,NULL,'等保管理','导入','导入等保','2025-06-30 06:53:08','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(267,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-30 06:53:10','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'失败','\r\n### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\r\n### The error may exist in com/example/sys/mapper/SysProtectMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.SysProtectMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_sys_protect  ( application_id, system_owner_org, member_unit, system_name, system_short_name, network_belonging, business_type, filing_number, classification_level, evaluation_time, evaluation_result, planned_evaluation_time, evaluation_organization, evaluation_status, system_status, category, control_point, evaluation_item, compliance_status, result_record )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )\r\n### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\n; Duplicate entry \'1\' for key \'PRIMARY\'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'',1),(268,NULL,NULL,'等保管理','导入','导入等保','2025-06-30 07:08:57','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(269,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-30 07:08:58','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'失败','\r\n### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\r\n### The error may exist in com/example/sys/mapper/SysProtectMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.SysProtectMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_sys_protect  ( application_id, system_owner_org, member_unit, system_name, system_short_name, network_belonging, business_type, filing_number, classification_level, evaluation_time, evaluation_result, planned_evaluation_time, evaluation_organization, evaluation_status, system_status, category, control_point, evaluation_item, compliance_status, result_record )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )\r\n### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\n; Duplicate entry \'1\' for key \'PRIMARY\'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'',1),(270,NULL,NULL,'等保管理','导入','导入等保','2025-06-30 07:12:11','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(271,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-30 07:12:14','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'失败','\r\n### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\r\n### The error may exist in com/example/sys/mapper/SysProtectMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.SysProtectMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_sys_protect  ( application_id, system_owner_org, member_unit, system_name, system_short_name, network_belonging, business_type, filing_number, classification_level, evaluation_time, evaluation_result, planned_evaluation_time, evaluation_organization, evaluation_status, system_status, category, control_point, evaluation_item, compliance_status, result_record )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )\r\n### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'\n; Duplicate entry \'1\' for key \'PRIMARY\'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry \'1\' for key \'PRIMARY\'',1),(272,NULL,NULL,'等保管理','导入','导入等保','2025-06-30 07:20:50','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(273,NULL,NULL,'等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-30 07:20:52','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(274,NULL,NULL,'等保管理','删除','删除等保：秦山核电生产管理系统','2025-06-30 07:21:50','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',21,'成功',NULL,1),(275,NULL,NULL,'等保管理','导入','导入PDF测评报告','2025-06-30 07:22:37','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(276,NULL,NULL,'等保管理','新增','\'新增等保：\' + #data.systemName','2025-06-30 07:22:39','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(277,NULL,NULL,'等保管理','删除','删除等保：秦山核电生产管理系统','2025-06-30 07:23:01','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',22,'成功',NULL,1),(278,NULL,NULL,'用户管理','登录','用户登录：admin1','2025-06-30 08:18:42','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(279,NULL,NULL,'用户管理','登录','用户登录：admin1','2025-06-30 08:18:57','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(280,NULL,NULL,'用户管理','登录','用户登录：admin1','2025-06-30 08:19:13','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(281,'admin',' admin','用户管理','登录','用户登录：admin','2025-06-30 08:19:19','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(282,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-02 01:08:53','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(283,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-02 02:29:32','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(284,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-02 05:15:08','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(285,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-02 06:15:23','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(286,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-02 06:58:41','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(287,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-03 00:58:13','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(288,NULL,NULL,'等保管理','导出','导出等保','2025-07-03 01:11:13','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(289,NULL,NULL,'等保管理','导入','导入等保','2025-07-03 01:16:12','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(290,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-07-03 01:16:15','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(291,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-03 02:35:27','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(292,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-03 06:08:04','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(293,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-03 06:51:12','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(294,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-03 07:53:33','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(295,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-03 08:47:57','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(296,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-07 05:28:45','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(297,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-08 00:49:25','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(298,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-11 00:43:39','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(299,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-11 01:14:09','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(300,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-11 01:28:42','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(301,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-11 02:14:47','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(302,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-14 07:40:08','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(303,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-14 08:28:49','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(304,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-15 02:10:01','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(305,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-15 03:07:44','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(306,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-15 05:08:17','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(307,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-15 05:12:41','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(308,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-15 05:34:09','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(309,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-15 05:36:40','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(310,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-15 05:45:57','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(311,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-15 06:27:11','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(312,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-17 01:45:37','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(313,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-17 03:15:36','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(314,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-21 05:50:49','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(315,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-21 05:52:42','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(316,'admin',' admin','系统保护管理','新增/更新','处理系统保护记录：#sysProtect.systemName','2025-07-21 05:53:23','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(317,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-07-21 05:53:24','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'失败','\r\n### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Incorrect date value: \'\' for column \'planned_evaluation_time\' at row 1\r\n### The error may exist in com/example/sys/mapper/SysProtectMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.SysProtectMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_sys_protect  ( system_owner_org, member_unit, system_name, system_short_name, network_belonging, business_type, filing_number, classification_level, evaluation_time, evaluation_result, planned_evaluation_time, evaluation_organization, evaluation_status, system_status, category, control_point, evaluation_item, compliance_status, result_record )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )\r\n### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Incorrect date value: \'\' for column \'planned_evaluation_time\' at row 1\n; Data truncation: Incorrect date value: \'\' for column \'planned_evaluation_time\' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Incorrect date value: \'\' for column \'planned_evaluation_time\' at row 1',1),(318,'admin',' admin','任务管理','删除','删除任务：ID=2','2025-07-21 05:56:47','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',2,'成功',NULL,1),(319,'admin',' admin','等保管理','修改','\'修改等保：\' + #data.systemName','2025-07-21 05:56:47','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(320,NULL,NULL,'任务管理','测评报告导入','导入PDF测评报告','2025-07-21 05:59:13','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(321,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-21 05:59:34','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(322,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-21 06:00:52','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(323,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-21 06:19:38','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(324,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-22 00:46:40','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(325,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-22 00:48:55','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(326,NULL,NULL,'等保管理','导入','导入等保','2025-07-22 00:49:39','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(327,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-22 01:05:25','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(328,'admin',' admin','系统保护管理','新增/更新','处理系统保护记录：#sysProtect.systemName','2025-07-22 01:05:29','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(329,'admin',' admin','等保管理','新增','\'新增等保：\' + #data.systemName','2025-07-22 01:05:29','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'失败','\r\n### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Incorrect date value: \'\' for column \'evaluation_time\' at row 1\r\n### The error may exist in com/example/sys/mapper/SysProtectMapper.java (best guess)\r\n### The error may involve com.example.sys.mapper.SysProtectMapper.insert-Inline\r\n### The error occurred while setting parameters\r\n### SQL: INSERT INTO x_sys_protect  ( system_owner_org, member_unit, system_name, system_short_name, network_belonging, business_type, filing_number, classification_level, evaluation_time, evaluation_result, planned_evaluation_time, evaluation_organization, evaluation_status, system_status, category, control_point, evaluation_item, compliance_status, result_record )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )\r\n### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Incorrect date value: \'\' for column \'evaluation_time\' at row 1\n; Data truncation: Incorrect date value: \'\' for column \'evaluation_time\' at row 1; nested exception is com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Incorrect date value: \'\' for column \'evaluation_time\' at row 1',1),(330,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-22 01:09:31','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(331,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-22 01:12:39','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(332,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-22 01:12:54','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(333,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-22 01:13:42','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(334,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-22 01:28:57','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(335,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-22 01:31:25','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(336,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-22 01:33:42','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(337,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-22 01:33:49','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(338,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-22 01:36:06','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(339,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-22 01:41:00','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(340,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-22 01:43:53','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(341,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-22 01:51:47','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(342,NULL,NULL,'任务管理','测评报告导入','导入PDF测评报告','2025-07-22 01:53:46','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(343,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-22 07:14:31','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(344,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-22 07:40:37','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(345,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-23 01:25:45','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(346,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-24 00:43:41','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(347,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-24 01:22:34','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(348,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-24 01:23:13','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(349,NULL,NULL,'等保管理','导入','导入等保','2025-07-24 01:27:13','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(350,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-24 01:30:44','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(351,NULL,NULL,'等保管理','导入','导入等保','2025-07-24 02:00:46','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(352,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-07-24 02:01:39','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(353,'admin',' admin','用户管理','登录','用户登录：admin','2025-07-30 01:23:20','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(354,NULL,NULL,'密码测评管理','导入','导入密码测评数据','2025-07-30 01:27:52','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(355,'admin',' admin','密码测评管理','新增','\'新增密码测评：\' + #data.systemName','2025-07-30 01:27:56','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(356,'admin',' admin','用户管理','登录','用户登录：admin','2025-08-01 01:36:33','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(357,NULL,NULL,'任务管理','问题清单导入','导入问题清单','2025-08-01 02:07:23','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(358,NULL,NULL,'任务管理','删除','删除任务：ID=1','2025-08-01 02:17:30','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',1,'成功',NULL,1),(359,NULL,NULL,'等保管理','修改','\'修改等保：\' + #data.systemName','2025-08-01 02:17:30','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(360,'admin',' admin','用户管理','登录','用户登录：admin','2025-08-01 02:48:34','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(361,'admin',' admin','用户管理','登录','用户登录：admin','2025-08-01 03:09:07','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(362,'admin',' admin','用户管理','登录','用户登录：admin','2025-08-01 05:27:23','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(363,'admin',' admin','用户管理','登录','用户登录：admin','2025-08-01 06:15:07','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(364,NULL,NULL,'密码测评管理','导出','导出密码测评数据','2025-08-01 06:30:27','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(365,NULL,NULL,'密码测评管理','导出','导出密码测评数据','2025-08-01 06:30:35','127.0.0.1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(366,'admin',' admin','用户管理','登录','用户登录：admin','2025-08-04 00:52:24','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(367,'admin',' admin','用户管理','登录','用户登录：admin','2025-08-04 01:21:10','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(368,NULL,NULL,'密码测评管理','导入','导入密码测评数据','2025-08-04 02:59:17','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(369,NULL,NULL,'密码测评管理','导入','导入密码测评数据','2025-08-04 02:59:43','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(370,NULL,NULL,'密码测评管理','导入','导入密码测评数据','2025-08-04 03:02:58','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(371,NULL,NULL,'密码测评管理','导入','导入密码测评数据','2025-08-04 03:05:47','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(372,NULL,NULL,'密码测评管理','导入','导入密码测评数据','2025-08-04 06:15:10','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,0),(373,'admin',' admin','用户管理','登录','用户登录：admin','2025-08-04 07:05:58','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(374,'admin',' admin','用户管理','登录','用户登录：admin','2025-08-05 00:40:39','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(375,'admin',' admin','用户管理','登录','用户登录：admin','2025-08-06 03:25:31','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1),(376,'admin',' admin','用户管理','登录','用户登录：admin','2025-08-11 07:23:03','0:0:0:0:0:0:0:1','Chrome 13 138.0.0.0/Windows 10',NULL,'成功',NULL,1);
/*!40000 ALTER TABLE `x_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `x_menu`
--

DROP TABLE IF EXISTS `x_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `x_menu` (
  `menu_id` int(11) NOT NULL AUTO_INCREMENT,
  `component` varchar(100) DEFAULT NULL,
  `path` varchar(100) DEFAULT NULL,
  `redirect` varchar(100) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `title` varchar(100) DEFAULT NULL,
  `icon` varchar(100) DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `is_leaf` varchar(1) DEFAULT NULL,
  `hidden` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `x_menu`
--

LOCK TABLES `x_menu` WRITE;
/*!40000 ALTER TABLE `x_menu` DISABLE KEYS */;
INSERT INTO `x_menu` VALUES (1,'Layout','/user','/user/list','userManage','用户管理','userManage',0,'N',0),(2,'user/user','list','','userList','用户列表','userList',1,'Y',0),(3,'user/role','role',NULL,'roleList','角色列表','role',1,'Y',0),(4,'user/permission','permission',NULL,'permissionList','权限列表','permission',1,'Y',0);
/*!40000 ALTER TABLE `x_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `x_role`
--

DROP TABLE IF EXISTS `x_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `x_role` (
  `role_id` int(11) NOT NULL AUTO_INCREMENT,
  `role_name` varchar(50) DEFAULT NULL,
  `role_desc` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `x_role`
--

LOCK TABLES `x_role` WRITE;
/*!40000 ALTER TABLE `x_role` DISABLE KEYS */;
INSERT INTO `x_role` VALUES (1,'admin','超级管理员'),(2,'hr','人事专员'),(3,'normal','普通用户');
/*!40000 ALTER TABLE `x_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `x_role_menu`
--

DROP TABLE IF EXISTS `x_role_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `x_role_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) DEFAULT NULL,
  `menu_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `x_role_menu`
--

LOCK TABLES `x_role_menu` WRITE;
/*!40000 ALTER TABLE `x_role_menu` DISABLE KEYS */;
/*!40000 ALTER TABLE `x_role_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `x_sys_protect`
--

DROP TABLE IF EXISTS `x_sys_protect`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `x_sys_protect` (
  `application_id` int(255) NOT NULL AUTO_INCREMENT,
  `system_owner_org` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `member_unit` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `system_name` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `system_short_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `network_belonging` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `business_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `filing_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `classification_level` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `evaluation_time` date DEFAULT NULL,
  `evaluation_result` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `planned_evaluation_time` date DEFAULT NULL,
  `evaluation_organization` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `evaluation_status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `system_status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `category` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `control_point` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `evaluation_item` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `compliance_status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `result_record` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`application_id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `x_sys_protect`
--

LOCK TABLES `x_sys_protect` WRITE;
/*!40000 ALTER TABLE `x_sys_protect` DISABLE KEYS */;
INSERT INTO `x_sys_protect` VALUES (1,'秦山核电','运行部','秦山核电生产管理系统','生产系统','生产网','生产作业','BA2023-001','三级','2023-06-01','符合','2024-06-01','国家核安全局','逾期','已下线','核电站系统','辐射监测','控制系统漏洞扫描','符合','扫描完成，发现3个低危漏洞已修复'),(2,'江苏核电','技术部','核电机组监控系统','监测系统','专网','指挥调度','BA2023-002','二级','2023-03-01','基本符合','2024-11-01','中核集团测评中心','逾期','运行中','监控系统','数据加密','权限管理审计','不符合','发现2个高危权限漏洞，需紧急修复'),(3,'中核武汉','研发中心','核燃料循环分析系统','燃料系统','管理网','其他','BA2023-003','二级','2023-10-01','符合','2024-01-01','中国核能行业协会','待测评','已下线','科研系统','日志审计','数据完整性检查','符合','数据校验机制完整，日志保存180天'),(4,'福清核电','安全部','核电应急响应系统','应急系统','生产网','指挥调度','BA2023-004','四级','2023-09-01','不符合','2024-06-01','中核集团测评中心','未知分级','已下线','安全系统','身份认证','通信协议安全','基本符合','TLS1.0仍在使用，需升级至TLS1.2'),(5,'海南核电','运维部','海南核电设备管理系统','设备系统','专网','生产作业','BA2023-005','一级','2023-12-01','符合','2024-12-01','海南核安全中心','未知分级','已下线','运维系统','备份恢复','设备漏洞修复','符合','所有高危漏洞已在30天内修复'),(7,'中核武汉','调度中心','核电电力调度平台','调度平台','管理网','公众服务','BA2023-007','三级','2023-11-01','符合','2024-11-01','中核集团测评中心','逾期','已注销','环保系统','输入验证','数据采集安全','基本符合','输入过滤机制存在2处绕过风险'),(8,'中核新能源','信息部','能源环境监测系统','监测系统','生产网','生产作业','BA2023-008','二级','2023-08-01','基本符合','2024-12-01','中核集团测评中心','待测评','运行中','工程系统','物理安全','施工安全审计','符合','审计覆盖率达100%，符合规范'),(9,'中核运行','控制中心','核电站运行监控平台','运行平台','专网','公众服务','BA2023-009','一级','2023-08-01','符合','2024-11-01','中核集团测评中心','未知分级','已注销','能源系统','会话管理','API接口安全','符合','API认证机制完善，未发现越权'),(10,'中核武汉','研发中心','核能综合利用平台','综合利用平台','管理网','指挥调度','BA2023-010','四级','2023-09-01','不符合','2024-12-01','中核集团测评中心','未知分级','运行中','运行系统','通信加密','实时数据保护','不符合','实时数据传输未加密，高风险'),(11,'秦山核电','运行部','秦山核电生产管理系统','生产系统','生产网','生产作业','BA2023-011','三级','2023-06-15','基本符合','2024-06-01','国家核安全局','逾期','已下线','核电站系统','访问控制','用户权限复核','基本符合','发现3个账户存在权限过大问题'),(12,'秦山核电','安全部','秦山核电生产管理系统','生产系统','生产网','生产作业','BA2023-012','三级','2023-10-10','符合','2024-06-01','国家核安全局','逾期','已下线','核电站系统','备份恢复','灾备演练审计','符合','RTO=4小时/RPO=15分钟达标'),(13,'江苏核电','技术部','核电机组监控系统','监测系统','专网','指挥调度','BA2023-013','二级','2023-04-01','不符合','2024-11-01','中核集团测评中心','逾期','运行中','监控系统','安全审计','日志完整性检查','不符合','日志存储空间不足，覆盖周期仅7天'),(14,'福清核电','信息中心','核电文档管理系统','文档系统','管理网','行政管理','BA2023-014','二级','2023-05-01','符合','2024-05-01','福建核安全局','逾期','运行中','办公系统','文档加密','文件传输安全','符合','采用国密SM4算法加密传输'),(15,'海南核电','设计院','核电站设计仿真平台','仿真平台','研发网','科研开发','BA2023-015','三级','2023-09-15','基本符合','2024-09-01','中核集团测评中心','逾期','运行中','科研系统','数据安全','敏感数据防护','基本符合','设计图纸未完全脱敏处理'),(16,'中核武汉','数据中心','核电大数据分析平台','分析平台','管理网','数据分析','BA2023-016','三级','2023-11-20','符合','2024-11-01','中核集团测评中心','逾期','运行中','分析系统','计算安全','算法完整性校验','符合','所有分析模型均通过签名验证'),(17,'中核运行','维护部','核电站维护管理系统','维护系统','生产网','设备维护','BA2023-017','二级','2023-10-10','不符合','2024-10-01','国家核安全局','待测评','运行中','运维系统','漏洞管理','补丁更新审计','不符合','发现15台服务器超6个月未更新'),(18,'中核新能源','监控中心','新能源辐射监测系统','辐射监测','生产网','环境监测','BA2023-018','四级','2023-12-05','基本符合','2024-12-01','环保部核安全中心','未知分级','运行中','监测系统','设备安全','传感器认证','基本符合','30%传感器未启用双向认证'),(19,'江苏核电','安防部','核电安防监控系统','安防系统','专网','安全防护','BA2023-019','三级','2023-08-20','符合','2024-08-01','江苏核安全局','逾期','运行中','安防系统','物理防护','门禁系统审计','符合','生物识别+IC卡双因素认证达标'),(20,'福清核电','工程部','核电工程建设管理系统','工程系统','工程网','工程建设','BA2023-020','二级','2023-07-01','不符合','2024-07-01','中核集团测评中心','逾期','已下线','工程系统','网络安全','边界防护检查','不符合','未部署工业防火墙，边界模糊'),(23,'秦山核电','运行部','秦山核电生产管理系统','生产系统','生产网','生产作业','BA2023-001','三级','2023-06-01','符合','2024-06-01','国家核安全局','逾期','已下线','安全物理环境','物理位置选择','a)机房场地应选择在具有防震、防风和防雨等能力的建筑内；','符合','且具备防震、防风和防雨的能力。该建筑建成较早具备建筑说明');
/*!40000 ALTER TABLE `x_sys_protect` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `x_tasks`
--

DROP TABLE IF EXISTS `x_tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `x_tasks` (
  `task_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '任务唯一主键（自增）',
  `task_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '未开始' COMMENT '任务状态（待处理/已完成）',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '任务创建时间（自动记录）',
  `end_time` datetime DEFAULT NULL COMMENT '任务结束时间',
  `responsible_person` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任务负责人',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '软删除标记（0=未删除，1=已删除）',
  `system_owner_org` varchar(100) DEFAULT NULL,
  `system_name` varchar(200) DEFAULT NULL,
  `system_status` varchar(50) DEFAULT NULL,
  `task_type` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`task_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='任务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `x_tasks`
--

LOCK TABLES `x_tasks` WRITE;
/*!40000 ALTER TABLE `x_tasks` DISABLE KEYS */;
INSERT INTO `x_tasks` VALUES (1,'待处理','2025-06-17 20:57:20','2025-06-20 20:57:20','张三',1,'秦山核电','秦山核电生产管理系统','运行中','等保测评'),(2,'待处理','2025-06-17 20:57:20','2025-06-20 20:57:20','张三',0,'秦山核电','秦山核电生产管理系统','运行中','等保测评'),(3,'待处理','2025-06-17 20:57:20','2025-06-20 20:57:20','张三',0,'秦山核电','秦山核电生产管理系统','运行中','等保测评'),(4,'已完成','2025-06-18 16:30:15','2025-06-22 17:00:00','李四',0,'中核武汉','核燃料循环分析系统','已下线','等保测评'),(5,'已完成','2025-06-18 16:30:15','2025-06-22 17:00:00','李四',0,'中核武汉','核电站维护管理系统','运行中','等保测评');
/*!40000 ALTER TABLE `x_tasks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `x_user`
--

DROP TABLE IF EXISTS `x_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `x_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(100) DEFAULT NULL,
  `email` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `status` int(1) DEFAULT NULL,
  `avatar` varchar(200) DEFAULT NULL,
  `role` varchar(10) NOT NULL,
  `organization` varchar(100) NOT NULL COMMENT '所属单位',
  `reviewer` varchar(100) NOT NULL COMMENT '用户审核人',
  `deleted` int(11) NOT NULL DEFAULT '0' COMMENT '删除标签',
  `realname` varchar(100) NOT NULL COMMENT '用户的姓名',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `x_user`
--

LOCK TABLES `x_user` WRITE;
/*!40000 ALTER TABLE `x_user` DISABLE KEYS */;
INSERT INTO `x_user` VALUES (1,'admin','$2a$10$7BvaY/t5obufT02MvkltPu.MWgjoTK/eMSPVyt989auAUtGAnUdJO','<EMAIL>','18677778888',1,'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif','管理员','总公司','BOSS',0,' admin'),(2,'zhangsan','$2a$10$7BvaY/t5obufT02MvkltPu.MWgjoTK/eMSPVyt989auAUtGAnUdJO','<EMAIL>','13966667777',1,'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif','用户','总公司','admin',0,'张三'),(3,'lisi','$2a$10$7BvaY/t5obufT02MvkltPu.MWgjoTK/eMSPVyt989auAUtGAnUdJO','<EMAIL>','13966667778',1,'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif','用户','武汉子公司','admin',0,'李四'),(4,'wangwu','$2a$10$7BvaY/t5obufT02MvkltPu.MWgjoTK/eMSPVyt989auAUtGAnUdJO','<EMAIL>','13966667772',1,'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif','用户','北京子公司','admin',0,'王五'),(5,'zhaoer','$2a$10$7BvaY/t5obufT02MvkltPu.MWgjoTK/eMSPVyt989auAUtGAnUdJO','<EMAIL>','13966667776',1,'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif','用户','上海子公司','admin',0,'季六'),(6,'son','$2a$10$7BvaY/t5obufT02MvkltPu.MWgjoTK/eMSPVyt989auAUtGAnUdJO','<EMAIL>','13966667771',1,'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif','用户','天津子公司','admin',0,'刘七'),(7,'aaa','$2a$10$7BvaY/t5obufT02MvkltPu.MWgjoTK/eMSPVyt989auAUtGAnUdJO','<EMAIL>',NULL,1,NULL,'admin','dadad ','adad ad ',0,'胡八'),(8,'hzh','$2a$10$7BvaY/t5obufT02MvkltPu.MWgjoTK/eMSPVyt989auAUtGAnUdJO','<EMAIL>','1241321',1,NULL,'管理员','测试组织2','dghajkgdfk',0,'付九'),(13,'test3','$2a$10$Y2Je2GfDWSKByb1M1wM4bONhQ/TZBKXxKal6BwzxiuReX7zLNa3Hy','<EMAIL>','111',1,NULL,'sss','测试组织2','sda',0,'dada'),(17,'test31','test123','<EMAIL>',NULL,1,'https://example.com/avatar.png','用户','测试组织1','系统管理员',0,'测试用户');
/*!40000 ALTER TABLE `x_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `x_user_role`
--

DROP TABLE IF EXISTS `x_user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `x_user_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `role_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `x_user_role`
--

LOCK TABLES `x_user_role` WRITE;
/*!40000 ALTER TABLE `x_user_role` DISABLE KEYS */;
INSERT INTO `x_user_role` VALUES (1,1,1);
/*!40000 ALTER TABLE `x_user_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `x_vul`
--

DROP TABLE IF EXISTS `x_vul`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `x_vul` (
  `id` int(45) NOT NULL,
  `system_owner_org` varchar(100) DEFAULT NULL,
  `system_name` varchar(100) DEFAULT NULL,
  `business_type` varchar(50) DEFAULT NULL,
  `vul_id` varchar(45) DEFAULT NULL,
  `vul_name` varchar(45) DEFAULT NULL,
  `vul_type` varchar(45) DEFAULT NULL,
  `disclosure_date` date DEFAULT NULL,
  `vul_level` varchar(10) DEFAULT NULL,
  `vul_status` varchar(10) DEFAULT NULL,
  `vul_description` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `x_vul`
--

LOCK TABLES `x_vul` WRITE;
/*!40000 ALTER TABLE `x_vul` DISABLE KEYS */;
INSERT INTO `x_vul` VALUES (1,'中核武汉','核电大数据分析平台','数据分析','ZH-VUL-20250001','Apache Log4j2 远程代码执行漏洞','CWE-502','2021-12-09','高','已修复','反序列化漏洞导致远程代码执行'),(2,'中核运行','核电站监控系统','安全监控','ZH-VUL-20250002','Spring Framework RCE漏洞','CWE-918','2022-03-30','高','未修复','服务端请求伪造漏洞'),(3,'中核武汉','辐射监测平台','辐射监测平台','ZH-VUL-20250004','SQL注入漏洞','CWE-89','2022-05-20','中','未修复','用户输入未过滤导致数据库泄露');
/*!40000 ALTER TABLE `x_vul` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-11 16:04:36
