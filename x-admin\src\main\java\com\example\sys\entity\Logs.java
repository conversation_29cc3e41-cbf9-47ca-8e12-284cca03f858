package com.example.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用于记录管理系统中各种操作的详细日志信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
@TableName("x_logs")
public class Logs implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 操作日志的唯一标识，采用自增方式生成，用于唯一区分每条日志记录
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 执行操作的用户的用户名，可用于追溯操作人
     */
    private String username;

    /**
     * 执行操作的用户的真实姓名，辅助识别用户身份
     */
    private String realname;

    /**
     * 操作所属的系统模块，例如用户管理、商品管理等，便于对操作进行分类统计和分析
     */
    private String moduleName;

    /**
     * 具体的操作类型，如登录、创建、删除、更新等，描述操作的性质
     */
    private String operationType;

    /**
     * 对操作的详细描述，记录操作的具体内容和相关参数，帮助理解操作细节
     */
    private String operationDescription;

    /**
     * 操作发起的时间，精确到秒，记录操作发生的具体时刻
     */
    private LocalDateTime operationTime;

    /**
     * 操作发起时的 IP 地址，用于安全审计和追踪异常操作
     */
    private String operationIp;

    /**
     * 操作所使用的设备信息，包括浏览器类型、操作系统等，有助于了解操作环境
     */
    private String deviceInfo;

    /**
     * 若操作涉及具体的数据记录，此为关联数据的 ID，方便进行关联查询
     */
    private Long relatedDataId;

    /**
     * 操作的结果，如成功、失败等，可根据实际情况自定义结果状态
     */
    private String operationResult;

    /**
     * 当操作失败时，记录具体的错误信息，便于排查问题
     */
    private String errorMessage;

    /**
     * 标记该操作是否为敏感操作，可用于特殊监控和处理
     */
    private Boolean isSensitive;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
    public String getRealname() {
        return realname;
    }
    public void setRealname(String realname) {
        this.realname = realname;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }
    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }
    public String getOperationDescription() {
        return operationDescription;
    }

    public void setOperationDescription(String operationDescription) {
        this.operationDescription = operationDescription;
    }
    public LocalDateTime getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(LocalDateTime operationTime) {
        this.operationTime = operationTime;
    }
    public String getOperationIp() {
        return operationIp;
    }

    public void setOperationIp(String operationIp) {
        this.operationIp = operationIp;
    }
    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }
    public Long getRelatedDataId() {
        return relatedDataId;
    }

    public void setRelatedDataId(Long relatedDataId) {
        this.relatedDataId = relatedDataId;
    }
    public String getOperationResult() {
        return operationResult;
    }

    public void setOperationResult(String operationResult) {
        this.operationResult = operationResult;
    }
    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    public Boolean getIsSensitive() {
        return isSensitive;
    }

    public void setIsSensitive(Boolean isSensitive) {
        this.isSensitive = isSensitive;
    }

    @Override
    public String toString() {
        return "Logs{" +
            "id=" + id +
            ", username=" + username +
            ", realname=" + realname +
            ", moduleName=" + moduleName +
            ", operationType=" + operationType +
            ", operationDescription=" + operationDescription +
            ", operationTime=" + operationTime +
            ", operationIp=" + operationIp +
            ", deviceInfo=" + deviceInfo +
            ", relatedDataId=" + relatedDataId +
            ", operationResult=" + operationResult +
            ", errorMessage=" + errorMessage +
            ", isSensitive=" + isSensitive +
        "}";
    }
}
