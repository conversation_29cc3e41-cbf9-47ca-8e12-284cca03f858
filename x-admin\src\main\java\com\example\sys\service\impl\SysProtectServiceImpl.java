package com.example.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.sys.entity.SysProtect;
import com.example.sys.mapper.SysProtectMapper;
import com.example.sys.service.ISysProtectService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SysProtectServiceImpl extends ServiceImpl<SysProtectMapper, SysProtect> implements ISysProtectService {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    // 原有方法（自动更新状态）
    @Override
    public void autoUpdateEvaluationStatus() {
        List<SysProtect> protectList = baseMapper.selectList(null);
        if (CollectionUtils.isEmpty(protectList)) return;

        LocalDate currentDate = LocalDate.now();
        for (SysProtect protect : protectList) {
            String newStatus = calculateEvaluationStatus(protect, currentDate);
            protect.setEvaluationStatus(newStatus);
        }
        updateBatchById(protectList);
    }

    private String calculateEvaluationStatus(SysProtect protect, LocalDate currentDate) {
        String evaluationTimeStr = protect.getEvaluationTime();
        if (evaluationTimeStr == null || evaluationTimeStr.trim().isEmpty()) {
            return "未填写测评时间";
        }

        LocalDate evaluationTime;
        try {
            evaluationTime = LocalDate.parse(evaluationTimeStr, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            return "日期格式错误（需为yyyy-MM-dd）";
        }

        long monthsBetween = ChronoUnit.MONTHS.between(evaluationTime, currentDate);
        String classificationLevel = protect.getClassificationLevel();

        if ("二级".equals(classificationLevel)) {
            return monthsBetween > 24? "逾期" : monthsBetween > 20? "待测评" : "已完成";
        } else if ("三级".equals(classificationLevel)) {
            return monthsBetween > 12? "逾期" : monthsBetween > 8? "待测评" : "已完成";
        } else {
            return "未知分级";
        }
    }

    @Override
    public Map<String, Object> getStatistics(String systemOwnerOrg, String systemName,
                                         String startTime, String endTime) {
    // 构建查询条件
    QueryWrapper<SysProtect> wrapper = new QueryWrapper<>();

    if (StringUtils.hasText(systemOwnerOrg)) {
        wrapper.eq("system_owner_org", systemOwnerOrg);
    }
    if (StringUtils.hasText(systemName)) {
        wrapper.eq("system_name", systemName);
    }
    if (StringUtils.hasText(startTime) && StringUtils.hasText(endTime)) {
        wrapper.between("evaluation_time", startTime, endTime);
    } else if (StringUtils.hasText(startTime)) {
        wrapper.ge("evaluation_time", startTime);
    } else if (StringUtils.hasText(endTime)) {
        wrapper.le("evaluation_time", endTime);
    }

    // 查询数据
    List<SysProtect> dataList = this.list(wrapper);

    // 初始化结果集
    Map<String, Object> result = new LinkedHashMap<>();

    // 1. controlPoint 计数
    Map<String, Long> controlPointCount = dataList.stream()
        .filter(item -> item.getControlPoint() != null)
        .collect(Collectors.groupingBy(
            SysProtect::getControlPoint,
            Collectors.counting()
        ));
    result.put("controlPointStatistics", controlPointCount);

    // 2. evaluationTime 年份计数
    Map<String, Long> yearCount = dataList.stream()
        .filter(item -> item.getEvaluationTime() != null)
        .map(item -> {
            try {
                return item.getEvaluationTime().substring(0, 4); // 提取年份
            } catch (Exception e) {
                return "Invalid Date";
            }
        })
        .collect(Collectors.groupingBy(
            year -> year,
            Collectors.counting()
        ));
    result.put("evaluationYearStatistics", yearCount);

    // 3. evaluationResult 计数
    Map<String, Long> evaluationResultCount = dataList.stream()
        .filter(item -> item.getEvaluationResult() != null)
        .collect(Collectors.groupingBy(
            SysProtect::getEvaluationResult,
            Collectors.counting()
        ));
    result.put("evaluationResultStatistics", evaluationResultCount);

    // 4. businessType 计数
    Map<String, Long> businessTypeCount = dataList.stream()
        .filter(item -> item.getBusinessType() != null)
        .collect(Collectors.groupingBy(
            SysProtect::getBusinessType,
            Collectors.counting()
        ));
    result.put("businessTypeStatistics", businessTypeCount);

    // 5. classificationLevel 计数
    Map<String, Long> classificationLevelCount = dataList.stream()
        .filter(item -> item.getClassificationLevel() != null)
        .collect(Collectors.groupingBy(
            SysProtect::getClassificationLevel,
            Collectors.counting()
        ));
    result.put("classificationLevelStatistics", classificationLevelCount);

    // 6. 按systemOwnerOrg分组的classificationLevel计数
    Map<String, Map<String, Long>> orgClassificationCount = dataList.stream()
        .filter(item -> item.getSystemOwnerOrg() != null && item.getClassificationLevel() != null)
        .collect(Collectors.groupingBy(
            SysProtect::getSystemOwnerOrg,
            Collectors.groupingBy(
                SysProtect::getClassificationLevel,
                Collectors.counting()
            )
        ));
    result.put("orgClassificationStatistics", orgClassificationCount);

    return result;
}
}
