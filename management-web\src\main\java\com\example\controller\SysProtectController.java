package com.example.controller;

import com.example.common.core.domain.AjaxResult;
import com.example.service.ISysProtectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequestMapping("/sysProtect")
public class SysProtectController {

    @Autowired
    private ISysProtectService sysProtectService;

    /**
     * 导入等保数据
     */
    @PostMapping("/import")
    public AjaxResult importData(MultipartFile file) throws Exception {
        return sysProtectService.importData(file);
    }

    /**
     * 导出等保数据
     */
    @GetMapping("/export")
    public void export(HttpServletResponse response) throws IOException {
        sysProtectService.export(response);
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/template")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        sysProtectService.downloadTemplate(response);
    }
} 