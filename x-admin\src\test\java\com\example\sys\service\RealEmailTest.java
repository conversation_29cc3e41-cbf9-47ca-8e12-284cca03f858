package com.example.sys.service;

import com.example.sys.entity.SysProtect;
import com.example.sys.entity.User;
import com.example.sys.mapper.SysProtectMapper;
import com.example.sys.mapper.UserMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest // 加载Spring应用上下文
@ActiveProfiles("test") // 使用测试环境配置（需包含真实邮件服务器信息）
public class RealEmailTest {

    @Autowired
    private IReminderService reminderService; // 定时任务服务

    @Autowired
    private UserMapper userMapper; // 用户表Mapper

    @Autowired
    private SysProtectMapper sysProtectMapper; // 测评任务表Mapper

    // 测试用真实邮箱配置（收件人邮箱）
    private static final String RECEIVER_EMAIL = "<EMAIL>"; // 目标收件箱

    /**
     * 测试前：插入关联测试数据（用户与测评任务）
     */
    //  @BeforeEach
    //  public void setup() {
    //     // 1. 插入测试用户（与测评任务的organization关联）
    //     User testUser = new User();
    //     testUser.setUsername("test31");
    //     testUser.setRealname("测试用户");
    //     testUser.setEmail(RECEIVER_EMAIL); // 收件人邮箱必须与测试目标一致
    //     testUser.setOrganization("测试组织1"); // 关键：与测评任务的evaluation_organization一致
    //     testUser.setRole("用户");
    //     testUser.setPassword("test123");
    //     testUser.setStatus(1); // 1表示启用状态
    //     testUser.setDeleted(0); // 0表示未删除（逻辑未删除）
    //     testUser.setAvatar("https://example.com/avatar.png");
    //     testUser.setReviewer("系统管理员");
    //     userMapper.insert(testUser);

         // 2. 插入测评任务（触发提醒的条件：未完成+计划时间已过期）
    //      SysProtect testProtect = new SysProtect();
    //      testProtect.setEvaluationOrganization("测试组织2"); // 与用户organization一致
    //      testProtect.setEvaluationStatus("未开始"); // 未完成状态触发提醒
    //      testProtect.setPlannedEvaluationTime("2025-05-08 00:00:00"); // 计划时间早于当前测试时间（2025-05-07）
    //      sysProtectMapper.insert(testProtect);
    //  }

    /**
     * 测试场景：验证真实邮件发送与接收
     */
    @Test
    public void testRealEmailSending() throws Exception {
        // 手动触发定时任务（模拟定时提醒）
        reminderService.sendEvaluationReminders();

        // 等待邮件到达（根据经验设置，如1分钟，方便手动查看）
        System.out.println("邮件已触发发送，请在1分钟内登录邮箱 " + RECEIVER_EMAIL + " 手动检查收件箱！");
        Thread.sleep(60000); // 等待60秒（可根据实际延迟调整）
           }
}
