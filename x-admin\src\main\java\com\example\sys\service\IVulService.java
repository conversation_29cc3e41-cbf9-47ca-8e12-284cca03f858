package com.example.sys.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.sys.entity.Vul;
import java.util.Map;

/**
 * <p>
 * 漏洞服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
public interface IVulService extends IService<Vul> {

    /**
     * 统计漏洞数据
     * @param systemOwnerOrg 所属单位
     * @param systemName 系统名称
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    Map<String, Object> getStatistics(String systemOwnerOrg, String systemName, String startDate, String endDate);
}