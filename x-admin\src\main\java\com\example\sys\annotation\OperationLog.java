package com.example.sys.annotation;

import java.lang.annotation.*;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLog {
    String moduleName() default "";       // 模块名称
    String operationType() default "";   // 操作类型
    String desc() default "";     // 操作描述（支持SpEL表达式）
    boolean isSensitive() default false; // 是否敏感操作
    String dataId() default "";    // 关联数据ID（支持SpEL表达式）
}