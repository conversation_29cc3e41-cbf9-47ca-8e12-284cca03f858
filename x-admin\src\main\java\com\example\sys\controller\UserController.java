package com.example.sys.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.commom.vo.Result;
import com.example.sys.annotation.OperationLog;
import com.example.sys.entity.User;
import com.example.sys.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/user")
public class UserController {
    @Autowired
    private IUserService userService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    // 1. 查询所有用户接口添加日志
    @GetMapping("/all")
    // @OperationLog(
    //     moduleName = "用户管理",
    //     operationType = "查询",
    //     desc = "'查询所有用户'",
    //     isSensitive = false
    // )
    public Result<List<User>> getAll() {
        List<User> list = userService.list();
        return Result.success(list, "查询成功");
    }

    @PostMapping("/login")
    @OperationLog(
        moduleName = "用户管理",
        operationType = "登录",
        desc = "'用户登录：'+ #user.username", // 修正为直接使用参数user
        isSensitive = true
    )
    public Result<Map<String, Object>> login(@RequestBody User user) {
        Map<String, Object> data = userService.login(user);
        if (data != null) {
            return Result.success(data);
        }
        return Result.fail(20002, "用户名或密码错误");
    }

    @GetMapping("/info")
    public Result<Map<String, Object>> getUserInfo(@RequestParam("token") String token) {
        Map<String, Object> data = userService.getUserInfo(token);
        if (data != null) {
            return Result.success(data);
        }
        return Result.fail(20003, "登录信息无效，请重新登录");
    }

    @PostMapping("/logout")
    public Result<?> logout(@RequestHeader("X-Token") String token) {
        userService.logout(token);
        return Result.success();
    }

    @GetMapping("/list")
    public Result<Map<String, Object>> getList(
        @RequestParam(value = "username", required = false) String username,
        @RequestParam(value = "realname", required = false) String realname,
        @RequestParam(value = "email", required = false) String email,
        @RequestParam(value = "phone", required = false) String phone,
        @RequestParam(value = "status", required = false) String status,
        @RequestParam(value = "role", required = false) String role,
        @RequestParam(value = "organization", required = false) String organization,
        @RequestParam(value = "reviewer", required = false) String reviewer,
        @RequestParam(value = "pageNo") Long pageNo,
        @RequestParam(value = "pageSize") Long pageSize
    ) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.hasLength(username), User::getUsername, username);
        wrapper.eq(StringUtils.hasLength(realname), User::getRealname, realname);
        wrapper.eq(StringUtils.hasLength(email), User::getEmail, email);
        wrapper.eq(StringUtils.hasLength(phone), User::getPhone, phone);
        wrapper.eq(StringUtils.hasLength(status), User::getStatus, status);
        wrapper.eq(StringUtils.hasLength(role), User::getRole, role);
        wrapper.eq(StringUtils.hasLength(organization), User::getOrganization, organization);
        wrapper.eq(StringUtils.hasLength(reviewer), User::getReviewer, reviewer);
        wrapper.orderByDesc(User::getId);

        Page<User> page = new Page<>(pageNo, pageSize);
        userService.page(page, wrapper);

        Map<String, Object> data = new HashMap<>();
        data.put("total", page.getTotal());
        data.put("rows", page.getRecords());

        return Result.success(data);
    }

    @PutMapping("/update")
    @OperationLog(
        moduleName = "用户管理",
        operationType = "修改",
        desc = "'修改用户：' + #data.username",
        dataId = "#data.id",
        isSensitive = true
    )
    public Result<User> updateUserInfo(@RequestBody User user) {
        user.setPassword(null);
        userService.updateById(user);
        return Result.success(user);
    }

    @PostMapping("/add")
    @OperationLog(
        moduleName = "用户管理",
        operationType = "新增",
        desc = "'添加用户：' + #data.username",
        dataId = "#data.id",
        isSensitive = true
    )
    public Result<User> addUser(@RequestBody User user) {
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        userService.save(user);
        return Result.success(user);
    }

    @GetMapping("/{id}")
    @OperationLog(
        moduleName = "用户管理",
        operationType = "查询",
        desc = "'查询用户详情：ID=' + #id",
        dataId = "#id",
        isSensitive = false
    )
    public Result<User> getUserById(@PathVariable("id") Long id) {
        User user = userService.getById(id);
        return Result.success(user);
    }

    @DeleteMapping("/{id}")
    @OperationLog(
        moduleName = "用户管理",
        operationType = "删除",
        desc = "'删除用户：' + #data.username",
        dataId = "#id", // 保持与参数一致
        isSensitive = true
    )
    public Result<User> deleteUser(@PathVariable("id") Long id) {
        User user = userService.getById(id);
        if (user == null) {
            return Result.fail("用户不存在");
        }
        userService.removeById(id);
        return Result.success(user, "删除用户成功");
    }
}