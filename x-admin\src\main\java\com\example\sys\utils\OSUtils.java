package com.example.sys.utils;

import cn.hutool.system.oshi.CpuInfo;
import cn.hutool.system.oshi.OshiUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import oshi.SystemInfo;
import oshi.software.os.FileSystem;
import oshi.software.os.OSFileStore;
import oshi.software.os.OperatingSystem;

import java.net.InetAddress;
import java.text.DecimalFormat;
import java.util.List;
import java.util.Properties;
import java.lang.management.ManagementFactory;

public class OSUtils {

    /**
     * 获取CPU使用率
     */
    public static String getCpuUsage() {
        CpuInfo cpuInfo = OshiUtil.getCpuInfo();
        double free = cpuInfo.getFree();
        DecimalFormat format = new DecimalFormat("#.00");
        return format.format(100D - free) + " %";
    }

    /**
     * 获取内存信息
     */
    public static JSONObject getMemoryInfo() {
        JSONObject memoryInfo = new JSONObject();
// 由于 OshiUtil.getSystemInfo() 方法未定义，直接使用 SystemInfo 类来获取系统信息
SystemInfo systemInfo = new SystemInfo();
oshi.hardware.GlobalMemory memory = systemInfo.getHardware().getMemory();
        memoryInfo.put("total", formatByte(memory.getTotal()));
        memoryInfo.put("free", formatByte(memory.getAvailable()));
        memoryInfo.put("used", formatByte(memory.getTotal() - memory.getAvailable()));
        memoryInfo.put("usage", new DecimalFormat("#.## %").format(
                1.0 - memory.getAvailable() * 1.0 / memory.getTotal()
        ));
        return memoryInfo;
    }

    /**
     * 获取磁盘信息
     */
    public static JSONArray getDiskInfo() {
        JSONArray diskInfoArr = new JSONArray();
        SystemInfo systemInfo = new SystemInfo();
        OperatingSystem os = systemInfo.getOperatingSystem();
        FileSystem fileSystem = os.getFileSystem();
        List<OSFileStore> fileStores = fileSystem.getFileStores();

        for (OSFileStore fileStore : fileStores) {
            JSONObject diskInfo = new JSONObject();
            diskInfo.put("mountName", fileStore.getMount());
            diskInfo.put("diskType", fileStore.getType());
            diskInfo.put("total", formatByte(fileStore.getTotalSpace()));
            diskInfo.put("free", formatByte(fileStore.getUsableSpace()));
            diskInfo.put("used", formatByte(fileStore.getTotalSpace() - fileStore.getUsableSpace()));
            diskInfo.put("usage", new DecimalFormat("#.## %").format(
                    fileStore.getTotalSpace() == 0 ? 0 :
                            (fileStore.getTotalSpace() - fileStore.getUsableSpace()) * 1.0 / fileStore.getTotalSpace()
            ));
            diskInfoArr.add(diskInfo);
        }
        return diskInfoArr;
    }

    /**
     * 获取系统信息
     */
    public static JSONObject getSysInfo() {
        JSONObject sysInfo = new JSONObject();
        try {
            Properties props = System.getProperties();
            sysInfo.put("osName", props.getProperty("os.name"));
            sysInfo.put("osArch", props.getProperty("os.arch"));
            sysInfo.put("hostName", InetAddress.getLocalHost().getHostName());
            sysInfo.put("hostAddress", getRealHostAddress()); // 替换为真实 IP 获取方法
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sysInfo;
    }

    /**
     * 获取Java虚拟机信息
     */
    public static JSONObject getJvmInfo() {
        JSONObject jvmInfo = new JSONObject();
        Properties props = System.getProperties();
        Runtime runtime = Runtime.getRuntime();
        long jvmMaxMemory = runtime.maxMemory();
        long jvmTotalMemory = runtime.totalMemory();
        long jvmFreeMemory = runtime.freeMemory();

        jvmInfo.put("max", formatByte(jvmMaxMemory));
        jvmInfo.put("total", formatByte(jvmTotalMemory));
        jvmInfo.put("free", formatByte(jvmFreeMemory));
        jvmInfo.put("used", formatByte(jvmTotalMemory - jvmFreeMemory));
        jvmInfo.put("usage", new DecimalFormat("#.## %").format(
                (jvmTotalMemory - jvmFreeMemory) * 1.0 / jvmTotalMemory
        ));
        jvmInfo.put("jdkHome", props.getProperty("java.home"));
        jvmInfo.put("jdkVersion", props.getProperty("java.version"));
        jvmInfo.put("pid", ManagementFactory.getRuntimeMXBean().getName().split("@")[0]); // 可靠的 PID 获取方式
        jvmInfo.put("projectDir", props.getProperty("user.dir"));
        jvmInfo.put("timeZone", props.getProperty("user.timezone"));
        jvmInfo.put("userName", props.getProperty("user.name"));
        return jvmInfo;
    }

    /**
     * 获取真实 IP（非环回地址）
     */
    private static String getRealHostAddress() {
        try {
            java.util.Enumeration<java.net.NetworkInterface> interfaces = java.net.NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                java.net.NetworkInterface iface = interfaces.nextElement();
                if (iface.isLoopback() || !iface.isUp()) continue;

                java.util.Enumeration<java.net.InetAddress> addresses = iface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    java.net.InetAddress addr = addresses.nextElement();
                    if (!addr.isLoopbackAddress() && addr instanceof java.net.Inet4Address) {
                        return addr.getHostAddress();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "unknown";
    }

    /**
     * 字节单位转换
     */
    private static String formatByte(long byteNumber) {
        double FORMAT = 1024.0;
        if (byteNumber < FORMAT) return byteNumber + " B";
        double kb = byteNumber / FORMAT;
        if (kb < FORMAT) return new DecimalFormat("#.## KB").format(kb);
        double mb = kb / FORMAT;
        if (mb < FORMAT) return new DecimalFormat("#.## MB").format(mb);
        double gb = mb / FORMAT;
        if (gb < FORMAT) return new DecimalFormat("#.## GB").format(gb);
        double tb = gb / FORMAT;
        return new DecimalFormat("#.## TB").format(tb);
    }
}