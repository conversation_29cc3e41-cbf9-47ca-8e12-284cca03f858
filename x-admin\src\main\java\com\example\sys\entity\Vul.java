package com.example.sys.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@TableName("x_vul")
public class Vul implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String systemOwnerOrg;

    private String systemName;

    private String businessType;

    private String vulId;

    private String vulName;

    private String vulType;

    private LocalDate disclosureDate;

    private String vulLevel;

    private String vulStatus;

    private String vulDescription;

    private String cveNumber;

    private String cnvdNumber;

    private String cnnvdNumber;

    private BigDecimal cvssScore;

    private String patchSituation;

    private String impactScope;

    private String solutionSuggestions;

    private String referenceLink;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getSystemOwnerOrg() {
        return systemOwnerOrg;
    }

    public void setSystemOwnerOrg(String systemOwnerOrg) {
        this.systemOwnerOrg = systemOwnerOrg;
    }
    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }
    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
    public String getVulId() {
        return vulId;
    }

    public void setVulId(String vulId) {
        this.vulId = vulId;
    }
    public String getVulName() {
        return vulName;
    }

    public void setVulName(String vulName) {
        this.vulName = vulName;
    }
    public String getVulType() {
        return vulType;
    }

    public void setVulType(String vulType) {
        this.vulType = vulType;
    }
    public LocalDate getDisclosureDate() {
        return disclosureDate;
    }

    public void setDisclosureDate(LocalDate disclosureDate) {
        this.disclosureDate = disclosureDate;
    }
    public String getVulLevel() {
        return vulLevel;
    }

    public void setVulLevel(String vulLevel) {
        this.vulLevel = vulLevel;
    }
    public String getVulStatus() {
        return vulStatus;
    }

    public void setVulStatus(String vulStatus) {
        this.vulStatus = vulStatus;
    }
    public String getVulDescription() {
        return vulDescription;
    }

    public void setVulDescription(String vulDescription) {this.vulDescription = vulDescription;}

    @Override
    public String toString() {
        return "Vul{" +
            "id=" + id +
            ", systemOwnerOrg=" + systemOwnerOrg +
            ", systemName=" + systemName +
            ", businessType=" + businessType +
            ", vulId=" + vulId +
            ", vulName=" + vulName +
            ", vulType=" + vulType +
            ", disclosureDate=" + disclosureDate +
            ", vulLevel=" + vulLevel +
            ", vulStatus=" + vulStatus +
            ", vulDescription=" + vulDescription +
        "}";
    }

    // 修复了此处多余的右括号，并统一了方法格式
    public void setCveNumber(String cveNumber) {
        this.cveNumber = cveNumber;
    }

    public void setCnvdNumber(String cnvdNumber) {
        this.cnvdNumber = cnvdNumber;
    }

    public void setCnnvdNumber(String cnnvdNumber) {
        this.cnnvdNumber = cnnvdNumber;
    }

    public void setImpactScope(String impactScope) {
        this.impactScope = impactScope;
    }

    public void setSolutionSuggestions(String solutionSuggestions) {
        this.solutionSuggestions = solutionSuggestions;
    }

    public void setReferenceLink(String referenceLink) {
        this.referenceLink = referenceLink;
    }

    // 修复了所有getter方法缺少分号的问题
    public String getCveNumber() {
        return cveNumber;
    }

    public String getCnvdNumber() {
        return cnvdNumber;
    }

    public String getCnnvdNumber() {
        return cnnvdNumber;
    }

    public BigDecimal getCvssScore() {
        return cvssScore;
    }

    public String getPatchSituation() {
        return patchSituation;
    }



    public String getImpactScope() {
        return impactScope;
    }

    public String getSolutionSuggestions() {
        return solutionSuggestions;
    }

    public String getReferenceLink() {
        return referenceLink;
    }
}


