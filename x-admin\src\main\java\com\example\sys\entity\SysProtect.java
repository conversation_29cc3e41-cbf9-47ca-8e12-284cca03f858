package com.example.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
@TableName("x_sys_protect")
public class SysProtect implements Serializable {

    private static final long serialVersionUID = 1L;
    // 添加主键注解
    @TableId(type = IdType.AUTO)
    private Integer applicationId;

    private String systemOwnerOrg;

    private String memberUnit;

    private String systemName;

    private String systemShortName;

    private String networkBelonging;

    private String businessType;

    private String filingNumber;

    private String classificationLevel;

    private String evaluationTime;

    private String evaluationResult;

    private String plannedEvaluationTime;

    private String evaluationOrganization;

    private String evaluationStatus;

    private String systemStatus;

    private String category;

    private String controlPoint;

    private String evaluationItem;

    private String complianceStatus;

    private String resultRecord;

    public Integer getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Integer applicationId) {
        this.applicationId = applicationId;
    }
    public String getSystemOwnerOrg() {
        return systemOwnerOrg;
    }

    public void setSystemOwnerOrg(String systemOwnerOrg) {
        this.systemOwnerOrg = systemOwnerOrg;
    }
    public String getMemberUnit() {
        return memberUnit;
    }

    public void setMemberUnit(String memberUnit) {
        this.memberUnit = memberUnit;
    }
    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }
    public String getSystemShortName() {
        return systemShortName;
    }

    public void setSystemShortName(String systemShortName) {
        this.systemShortName = systemShortName;
    }
    public String getNetworkBelonging() {
        return networkBelonging;
    }

    public void setNetworkBelonging(String networkBelonging) {
        this.networkBelonging = networkBelonging;
    }
    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
    public String getFilingNumber() {
        return filingNumber;
    }

    public void setFilingNumber(String filingNumber) {
        this.filingNumber = filingNumber;
    }
    public String getClassificationLevel() {
        return classificationLevel;
    }

    public void setClassificationLevel(String classificationLevel) {
        this.classificationLevel = classificationLevel;
    }
    public String getEvaluationTime() {
        return evaluationTime;
    }

    public void setEvaluationTime(String evaluationTime) {
        this.evaluationTime = evaluationTime;
    }
    public String getEvaluationResult() {
        return evaluationResult;
    }

    public void setEvaluationResult(String evaluationResult) {
        this.evaluationResult = evaluationResult;
    }
    public String getPlannedEvaluationTime() {
        return plannedEvaluationTime;
    }

    public void setPlannedEvaluationTime(String plannedEvaluationTime) {
        this.plannedEvaluationTime = plannedEvaluationTime;
    }
    public String getEvaluationOrganization() {
        return evaluationOrganization;
    }

    public void setEvaluationOrganization(String evaluationOrganization) {
        this.evaluationOrganization = evaluationOrganization;
    }
    public String getEvaluationStatus() {
        return evaluationStatus;
    }

    public void setEvaluationStatus(String evaluationStatus) {
        this.evaluationStatus = evaluationStatus;
    }
    public String getSystemStatus() {
        return systemStatus;
    }

    public void setSystemStatus(String systemStatus) {
        this.systemStatus = systemStatus;
    }
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
    public String getControlPoint() {
        return controlPoint;
    }

    public void setControlPoint(String controlPoint) {
        this.controlPoint = controlPoint;
    }
    public String getEvaluationItem() {
        return evaluationItem;
    }

    public void setEvaluationItem(String evaluationItem) {
        this.evaluationItem = evaluationItem;
    }
    public String getComplianceStatus() {
        return complianceStatus;
    }

    public void setComplianceStatus(String complianceStatus) {
        this.complianceStatus = complianceStatus;
    }

    public String getResultRecord() {
        return resultRecord;
    }

    public void setResultRecord(String resultRecord) {
        this.resultRecord = resultRecord;
    }

    @Override
    public String toString() {
        return "SysProtect{" +
            "applicationId=" + applicationId +
            ", systemOwnerOrg=" + systemOwnerOrg +
            ", memberUnit=" + memberUnit +
            ", systemName=" + systemName +
            ", systemShortName=" + systemShortName +
            ", networkBelonging=" + networkBelonging +
            ", businessType=" + businessType +
            ", filingNumber=" + filingNumber +
            ", classificationLevel=" + classificationLevel +
            ", evaluationTime=" + evaluationTime +
            ", evaluationResult=" + evaluationResult +
            ", plannedEvaluationTime=" + plannedEvaluationTime +
            ", evaluationOrganization=" + evaluationOrganization +
            ", evaluationStatus=" + evaluationStatus +
            ", systemStatus=" + systemStatus +
            ", category=" + category +
            ", controlPoint=" + controlPoint +
            ", evaluationItem=" + evaluationItem +
            ", complianceStatus=" + complianceStatus +
            ", resultRecord=" + resultRecord +
        "}";
    }
}
