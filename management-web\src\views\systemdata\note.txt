<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>组织管理</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="handleAdd">新增</el-button>
      </div>
      <el-input placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
      <el-table
        :data="filteredTableData"
        style="width: 100%"
        row-key="deptId"
        border
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        @row-click="handleNodeClick"
      >
        <el-table-column prop="name" label="部门名称" width="180"></el-table-column>
        <el-table-column prop="deptId" label="部门ID" width="180"></el-table-column>
        <el-table-column prop="phone" label="电话" width="180"></el-table-column>
        <el-table-column prop="address" label="地址" width="180"></el-table-column>
        <el-table-column prop="status" label="状态" width="180"></el-table-column>
        <el-table-column fixed="right" label="操作" width="150">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加或编辑表单 -->
    <el-dialog :title="formTitle" :visible.sync="showForm" width="50%">
      <el-form :model="form" :rules="rules" ref="deptForm" label-width="100px">
        <el-form-item label="部门名称" prop="name">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="部门ID" prop="deptId">
          <el-input v-model="form.deptId" :disabled="!isAdding"></el-input>
        </el-form-item>
        <el-form-item label="父部门名称" prop="parentDeptName">
          <el-input v-model="form.parentDeptName" disabled></el-input>
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input v-model="form.phone"></el-input>
        </el-form-item>
        <el-form-item label="地址" prop="address">
          <el-input v-model="form.address"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status">
            <el-option label="启用" value="启用"></el-option>
            <el-option label="禁用" value="禁用"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showForm = false">取 消</el-button>
        <el-button type="primary" @click="saveDept">确 定</el-button>
      </div>
    </el-dialog>

    <div class="spacer"></div>
  </div>
</template>

<script>
import deptApi from '@/api/deptManage';

export default {
  name: 'deptManage',
  data() {
    return {
      filterText: '',
      form: {
        deptId: null,
        name: '',
        pid: '',
        parentDeptName: '',
        phone: '',
        address: '',
        status: ''
      },
      showForm: false,
      formTitle: '新增部门',
      isAdding: true,
      listQuery: {
        deptId: null,
        pid: null,
        name: null,
        phone: null,
        address: null,
        status: null
      },
      rules: {
        name: [{ required: true, message: '请输入部门名称', trigger: 'blur' }],
        phone: [{ min: 6, max: 16, message: '长度在6到16个字符', trigger: 'blur' }],
        address: [{ required: true, message: '请输入地址', trigger: 'blur' }]
      },
      deptList: [],
      tableData: [],
      filteredTableData: []
    };
  },
  created() {
    this.getDeptList();
  },
  computed: {
    filteredTableData() {
      if (!this.filterText) {
        return this.tableData;
      }
      return this.tableData.filter(item => {
        return item.name.toLowerCase().includes(this.filterText.toLowerCase());
      });
    }
  },
  methods: {
    getDeptList() {
      deptApi.getDeptList(this.listQuery).then(response => {
        this.deptList = response.data.depts;
        this.tableData = this.buildTree(this.deptList);
        this.filteredTableData = this.tableData;
      });
    },
    buildTree(deptList) {
      const tree = [];
      const map = {};

      deptList.forEach(dept => {
        map[dept.deptId] = { ...dept, children: [] };
      });

      deptList.forEach(dept => {
        if (dept.pid === null) {
          tree.push(map[dept.deptId]);
        } else {
          if (!map[dept.pid].children) {
            map[dept.pid].children = [];
          }
          map[dept.pid].children.push(map[dept.deptId]);
        }
      });

      return tree;
    },
    handleNodeClick(row) {
      this.form = { ...row };
      this.form.parentDeptName = this.getParentDeptName(row.pid);
      this.showForm = true;
      this.formTitle = '编辑部门';
      this.isAdding = false;
    },
    getParentDeptName(pid) {
      const parentDept = this.deptList.find(dept => dept.deptId === pid);
      return parentDept ? parentDept.name : '';
    },
    handleAdd() {
      this.form = {
        deptId: null,
        name: '',
        pid: '',
        parentDeptName: '',
        phone: '',
        address: '',
        status: ''
      };
      this.showForm = true;
      this.formTitle = '新增部门';
      this.isAdding = true;
    },
    handleEdit(row) {
      this.form = { ...row };
      this.form.parentDeptName = this.getParentDeptName(row.pid);
      this.showForm = true;
      this.formTitle = '编辑部门';
      this.isAdding = false;
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该部门, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deptApi.deleteDeptbyDeptID(row.deptId).then(response => {
          this.$message.success('部门已删除');
          this.getDeptList();
        });
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    saveDept() {
      this.$refs.deptForm.validate(valid => {
        if (valid) {
          if (this.form.deptId) {
            // 编辑部门
            deptApi.updateDept(this.form).then(response => {
              this.$message.success('部门信息已更新');
              this.getDeptList();
              this.showForm = false;
            });
          } else {
            // 新增部门
            deptApi.addDept(this.form).then(response => {
              this.$message.success('部门已添加');
              this.getDeptList();
              this.showForm = false;
            });
          }
        }
      });
    }
  }
};
</script>

<style scoped>
.app-container {
  display: flex;
  padding: 20px;
  gap: 20px;
}

.filter-tree {
  margin-top: 20px;
}

.info-card,
.box-card {
  flex: 1;
  max-width: calc(100% - 10px);
}

.dept-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 10px;
  padding: 5px 0;
}

.dept-actions {
  display: flex;
  gap: 10px;
}

.el-tree-node {
  margin-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.el-tree-node__content {
  padding: 5px 0;
}

.el-tree-node__children {
  padding-left: 20px;
}
</style>