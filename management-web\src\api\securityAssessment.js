import request from '@/utils/request'

export function getSysProtectList(query) {
  return request({
    url: '/sysProtect/list',
    method: 'get',
    params: query
  })
}

export function getSysProtectSta(query) {
  return request({
    url: '/sysProtect/statistics',
    method: 'get',
    params: query
  })
}

export function getSysProtectById(id) {
  return request({
    url: `/sysProtect/${id}`,
    method: 'get'
  })
}
// 新增
export function addSysProtect(data) {
  return request({
    url: '/sysProtect/add',
    method: 'post',
    data
  })
}

// 修改
export function updateSysProtect(data) {
  return request({
    url: '/sysProtect/update',
    method: 'put',
    data
  })
}

// 删除
export function deleteSysProtect(applicationId) {
  return request({
    url: `/sysProtect/${applicationId}`,
    method: 'delete'
  })
}

// 删除
export function saveSysProtect(data) {
  if(data.applicationId == null && data.applicationId == undefined){
    return this.addSysProtect(data)
  }else{
    return this.updateSysProtect(data)
  }
}

export function exportSysProtect(query) {
  return request({
    url: '/sysProtect/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  }).then(response => {
    return response
  }).catch(error => {
    console.error('错误详情：', error.response || error.message)
    throw error
  })
}

// 下线修改
export function offline(data) {
  return request({
    url: '/sysProtect/offline',
    method: 'put',
    data
  })
}
