package com.example.sys.scheduler;

import com.example.sys.service.ISysProtectService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 测评状态定时任务（每天自动更新）
 */
@Component
public class EvaluationStatusScheduler {

    @Resource
    private ISysProtectService sysProtectService;

    // 新增：项目启动时执行一次测评状态更新
    @PostConstruct
    public void initUpdateStatus() {
        sysProtectService.autoUpdateEvaluationStatus();
    }

    /**
     * 每天0点自动更新测评状态
     * cron表达式：0 0 0 * * ? 表示每天0点0分0秒执行
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void autoUpdateStatus() {
        sysProtectService.autoUpdateEvaluationStatus();
    }
}