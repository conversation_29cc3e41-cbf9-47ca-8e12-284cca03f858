package com.example.sys.controller;

import com.alibaba.fastjson2.JSONObject;
import com.example.sys.utils.OSUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/monitor")
// @CrossOrigin(origins = "*") // 允许所有跨域请求（生产环境需限制具体域名）
public class SystemMonitorController {

    /**
     * 获取系统监控信息
     */
    @GetMapping("/system-info")
    public JSONObject getSystemMonitorInfo() {
        JSONObject result = new JSONObject();
        result.put("cpuUsage", OSUtils.getCpuUsage());
        result.put("memoryInfo", OSUtils.getMemoryInfo());
        result.put("diskInfo", OSUtils.getDiskInfo());
        result.put("sysInfo", OSUtils.getSysInfo());
        result.put("jvmInfo", OSUtils.getJvmInfo());
        return result;
    }
}