// src/views/dashboard.js
import echarts from 'echarts'
import 'echarts/map/js/china.js'

// 地图数据加载
export const loadMap = () => {
  return new Promise((resolve) => {
    // 可在此处加载真实地理坐标数据
    resolve()
  })
}

// 地图配置（示例）
export const getMapOption = (data) => {
  return {
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        return `${params.name}<br/>系统数量: ${params.value[2]}<br/>漏洞数量: ${params.value[3]}`
      }
    },
    visualMap: {
      type: 'piecewise',
      pieces: [
        { min: 100, color: '#c23531' },
        { min: 50, max: 99, color: '#dd6b66' },
        { min: 10, max: 49, color: '#e6b600' },
        { max: 9, color: '#91c7ae' }
      ]
    },
    series: [{
      type: 'map',
      map: 'china',
      data: data,
      label: { show: true },
      itemStyle: {
        areaColor: '#f5f7fa',
        borderColor: '#ccc'
      }
    }]
  }
}
