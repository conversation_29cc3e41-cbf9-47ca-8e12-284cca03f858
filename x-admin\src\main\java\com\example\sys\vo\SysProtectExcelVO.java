package com.example.sys.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.example.sys.entity.SysProtect;

public class SysProtectExcelVO {
    @ExcelProperty("系统所属组织")
    private String systemOwnerOrg;

    @ExcelProperty("成员单位")
    private String memberUnit;

    @ExcelProperty("系统名称")
    private String systemName;

    @ExcelProperty("系统简称")
    private String systemShortName;

    @ExcelProperty("网络归属")
    private String networkBelonging;

    @ExcelProperty("业务类型")
    private String businessType;

    @ExcelProperty("备案编号")
    private String filingNumber;

    @ExcelProperty("等级")
    private String classificationLevel;

    @ExcelProperty("测评时间")
    private String evaluationTime;

    @ExcelProperty("测评结果")
    private String evaluationResult;

    @ExcelProperty("计划测评时间")
    private String plannedEvaluationTime;

    @ExcelProperty("测评机构")
    private String evaluationOrganization;

    @ExcelProperty("测评状态")
    private String evaluationStatus;

    @ExcelProperty("系统状态")
    private String systemStatus;

    @ExcelProperty("分类")
    private String category;

    @ExcelProperty("控制点")
    private String controlPoint;

    @ExcelProperty("测评项")
    private String evaluationItem;

    @ExcelProperty("符合情况")
    private String complianceStatus;

    @ExcelProperty("结果记录")
    private String resultRecord;
    public SysProtectExcelVO() {
    }
    // 转换构造方法
    public SysProtectExcelVO(SysProtect entity) {
        this.systemOwnerOrg = entity.getSystemOwnerOrg();
        this.memberUnit = entity.getMemberUnit();
        this.systemName = entity.getSystemName();
        this.systemShortName = entity.getSystemShortName();
        this.networkBelonging = entity.getNetworkBelonging();
        this.businessType = entity.getBusinessType();
        this.filingNumber = entity.getFilingNumber();
        this.classificationLevel = entity.getClassificationLevel();
        this.evaluationTime = entity.getEvaluationTime();
        this.evaluationResult = entity.getEvaluationResult();
        this.plannedEvaluationTime = entity.getPlannedEvaluationTime();
        this.evaluationOrganization = entity.getEvaluationOrganization();
        this.evaluationStatus = entity.getEvaluationStatus();
        this.systemStatus = entity.getSystemStatus();
        this.category = entity.getCategory();
        this.controlPoint = entity.getControlPoint();
        this.evaluationItem = entity.getEvaluationItem();
        this.complianceStatus = entity.getComplianceStatus();
        this.resultRecord = entity.getResultRecord();
    }

    // Getter和Setter省略...
    public String getSystemOwnerOrg() {
        return systemOwnerOrg;
    }

    public void setSystemOwnerOrg(String systemOwnerOrg) {
        this.systemOwnerOrg = systemOwnerOrg;
    }
    public String getMemberUnit() {
        return memberUnit;
    }

    public void setMemberUnit(String memberUnit) {
        this.memberUnit = memberUnit;
    }
    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }
    public String getSystemShortName() {
        return systemShortName;
    }

    public void setSystemShortName(String systemShortName) {
        this.systemShortName = systemShortName;
    }
    public String getNetworkBelonging() {
        return networkBelonging;
    }

    public void setNetworkBelonging(String networkBelonging) {
        this.networkBelonging = networkBelonging;
    }
    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
    public String getFilingNumber() {
        return filingNumber;
    }

    public void setFilingNumber(String filingNumber) {
        this.filingNumber = filingNumber;
    }
    public String getClassificationLevel() {
        return classificationLevel;
    }

    public void setClassificationLevel(String classificationLevel) {
        this.classificationLevel = classificationLevel;
    }
    public String getEvaluationTime() {
        return evaluationTime;
    }

    public void setEvaluationTime(String evaluationTime) {
        this.evaluationTime = evaluationTime;
    }
    public String getEvaluationResult() {
        return evaluationResult;
    }

    public void setEvaluationResult(String evaluationResult) {
        this.evaluationResult = evaluationResult;
    }
    public String getPlannedEvaluationTime() {
        return plannedEvaluationTime;
    }

    public void setPlannedEvaluationTime(String plannedEvaluationTime) {
        this.plannedEvaluationTime = plannedEvaluationTime;
    }
    public String getEvaluationOrganization() {
        return evaluationOrganization;
    }

    public void setEvaluationOrganization(String evaluationOrganization) {
        this.evaluationOrganization = evaluationOrganization;
    }
    public String getEvaluationStatus() {
        return evaluationStatus;
    }

    public void setEvaluationStatus(String evaluationStatus) {
        this.evaluationStatus = evaluationStatus;
    }
    public String getSystemStatus() {
        return systemStatus;
    }

    public void setSystemStatus(String systemStatus) {
        this.systemStatus = systemStatus;
    }
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
    public String getControlPoint() {
        return controlPoint;
    }

    public void setControlPoint(String controlPoint) {
        this.controlPoint = controlPoint;
    }
    public String getEvaluationItem() {
        return evaluationItem;
    }

    public void setEvaluationItem(String evaluationItem) {
        this.evaluationItem = evaluationItem;
    }
    public String getComplianceStatus() {
        return complianceStatus;
    }

    public void setComplianceStatus(String complianceStatus) {
        this.complianceStatus = complianceStatus;
    }

    public String getResultRecord() {
        return resultRecord;
    }

    public void setResultRecord(String resultRecord) {
        this.resultRecord = resultRecord;
    }
}
