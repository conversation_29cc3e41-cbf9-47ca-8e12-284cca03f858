<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header">
        <span class="card-title">等保系统列表</span>
        <!-- 导出按钮 -->
        <el-button
            type="primary"
            @click="exportData"
            :loading="exportLoading"
            :disabled="exportLoading"
        >
          {{ exportLoading ? '导出中...' : '导出数据' }}
        </el-button>
      </div>

      <!-- 数据表格 -->
      <el-table
          v-loading="loading"
          :data="tableData"
          border
          style="width: 100%; margin-top: 20px;"
          :header-cell-style="{ background: '#f5f7fa' }"
      >
        <!-- 表格列定义 -->
        <el-table-column prop="systemOwnerOrg" label="所属单位" min-width="120" align="center" />
        <el-table-column prop="memberUnit" label="成员单位" min-width="120" align="center" />
        <el-table-column prop="systemName" label="系统名称" min-width="120" align="center" />
        <el-table-column prop="systemShortName" label="系统简称" min-width="120" align="center" />
        <el-table-column prop="networkBelonging" label="网络归属" min-width="120" align="center" />
        <el-table-column prop="businessType" label="业务类型" min-width="120" align="center" />
        <el-table-column prop="filingNumber" label="备案号" min-width="120" align="center" />
        <el-table-column prop="classificationLevel" label="定级级别" width="100" align="center" />
        <el-table-column prop="evaluationTime" label="测评时间" min-width="120" align="center" />
        <el-table-column prop="evaluationResult" label="测评结果" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getResultType(scope.row.evaluationResult)">
              {{ scope.row.evaluationResult }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="plannedEvaluationTime" label="计划测评时间" min-width="120" align="center" />
        <el-table-column prop="evaluationOrganization" label="测评单位" min-width="120" align="center" />
        <el-table-column prop="evaluationStatus" label="测评状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag>{{ scope.row.evaluationStatus }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="systemStatus" label="系统状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getSystemStatusType(scope.row.systemStatus)">
              {{ scope.row.systemStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="分类" width="100" align="center" />
        <el-table-column prop="controlPoint" label="控制点" min-width="120" align="center" />
        <el-table-column prop="evaluationItem" label="测评项" min-width="120" align="center" />
        <el-table-column prop="complianceStatus" label="符合性状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.complianceStatus === '符合' ? 'success' : 'danger'">
              {{ scope.row.complianceStatus }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParams.pageNo"
          :page-sizes="[5, 10, 20, 50]"
          :page-size="queryParams.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
      >
      </el-pagination>
    </el-card>
  </div>
</template>

<script>
import { exportSysProtect, getSysProtectList } from '@/api/securityAssessment'
exportSysProtect
export default {
  name: 'SecurityAssessmentTable',
  data() {
    return {
      // 遮罩层，用于显示加载状态
      loading: false,
      // 总条数
      total: 0,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 5
      },
      // 表格数据
      tableData: [],
      exportLoading: false // 导出加载状态
    }
  },
  created() {
    // 组件创建时获取列表数据
    this.getList()
  },
  methods: {
    // 获取列表数据
    getList() {
      this.loading = true
      getSysProtectList(this.queryParams).then(response => {
        console.log('API响应数据:', response)
        if (response && response.data) {
          this.tableData = response.data.sysProtect || []
          this.total = response.data.total || 0
        } else {
          this.tableData = []
          this.total = 0
        }
        this.loading = false
      }).catch(error => {
        console.error('获取数据失败:', error)
        this.loading = false
        this.$message.error('获取数据失败')
      })
    },

    handleSizeChange(val) {
      // 分页大小改变时更新查询参数并重新获取数据
      this.queryParams.pageSize = val
      this.getList()
    },

    handleCurrentChange(val) {
      // 当前页码改变时更新查询参数并重新获取数据
      this.queryParams.pageNo = val
      this.getList()
    },

    getResultType(result) {
      // 根据测评结果返回对应的标签类型
      const typeMap = {
        '符合': 'success',
        '基本符合': 'warning',
        '不符合': 'danger'
      }
      return typeMap[result] || 'info'
    },

    getSystemStatusType(status) {
      // 根据系统状态返回对应的标签类型
      const typeMap = {
        '运行中': 'success',
        '已下线': 'info',
        '已注销': 'danger'
      }
      return typeMap[status] || 'info'
    },

    // 导出数据方法
    async exportData() {
      try {
        this.exportLoading = true; // 显示加载状态

        // 1. 准备导出参数（排除分页参数）
        const exportParams = this.prepareExportParams();
        console.log('导出参数:', exportParams);

        // 2. 调用导出API
        const response = await exportSysProtect(exportParams);

        // 3. 处理文件下载
        this.downloadFile(response);

        Message.success('导出成功');
      } catch (error) {
        console.error('导出失败:', error);
        Message.error(`导出失败: ${error.message || '请稍后重试'}`);
      } finally {
        this.exportLoading = false;
      }
    },

    // 修正的参数准备方法
    prepareExportParams() {
      // 确保queryParams是对象
      if (!this.queryParams || typeof this.queryParams !== 'object') {
        return {};
      }

      // 创建参数副本（避免直接修改原始数据）
      const params = JSON.parse(JSON.stringify(this.queryParams));

      // 移除分页参数
      delete params.pageNo;
      delete params.pageSize;

      // 过滤空值参数（更安全的实现）
      const filteredParams = {};
      for (const key in params) {
        const value = params[key];
        if (value !== null && value !== undefined && value !== '') {
          filteredParams[key] = value;
        }
      }

      return filteredParams;
    },

    // 处理文件下载
    downloadFile(response) {
      // 1. 创建Blob对象
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });

      // 2. 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // 3. 设置文件名（尝试从响应头获取）
      let fileName = '系统保护数据.xlsx';
      try {
        const contentDisposition = response.headers['content-disposition'];
        if (contentDisposition) {
          const fileNameMatch = contentDisposition.match(/filename\*?=utf-8''(.+?\.xlsx)/i);
          if (fileNameMatch && fileNameMatch[1]) {
            fileName = decodeURIComponent(fileNameMatch[1]);
          }
        }
      } catch (e) {
        console.warn('文件名解析失败:', e);
      }

      // 4. 设置下载属性
      link.setAttribute('download', fileName);
      document.body.appendChild(link);

      // 5. 触发下载
      link.click();

      // 6. 清理资源
      setTimeout(() => {
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }, 100);
    }

  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f0f2f5;
}

.box-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .card-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 20px;
}

.el-table {
  margin-top: 20px;

  th {
    background-color: #f5f7fa !important;
    color: #606266;
    font-weight: 500;
  }
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
