package com.example.sys.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@TableName("x_crypto_evaluation")
public class CryptoEvaluation implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String systemOwnerOrg;

    private String systemName;

    private String businessType;

    private String filingNumber;

    private String classificationLevel;

    private LocalDate evaluationTime;

    private String evaluationOrganization;

    private String evaluationStatus;

    private String evaluationResult;

    private String systemStatus;

    private String category;

    private String controlPoint;

    private String evaluationItem;

    private String resultRecord;

    private String riskLevel;

    private String rectificationSuggestion;

    private BigDecimal cryptoScore;

    private BigDecimal estimatedScoreAfterFix;

    public Integer getId() {
        return id;
    }

    public void setEvaluationResult(String evaluationResult) {this.evaluationResult = evaluationResult;}

    public void setId(Integer id) {
        this.id = id;
    }
    public String getSystemOwnerOrg() {
        return systemOwnerOrg;
    }
    public String getEvaluationResult() {
    return evaluationResult;
}
    public void setSystemOwnerOrg(String systemOwnerOrg) {
        this.systemOwnerOrg = systemOwnerOrg;
    }
    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }
    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
    public String getFilingNumber() {
        return filingNumber;
    }

    public void setFilingNumber(String filingNumber) {
        this.filingNumber = filingNumber;
    }
    public String getClassificationLevel() {
        return classificationLevel;
    }

    public void setClassificationLevel(String classificationLevel) {
        this.classificationLevel = classificationLevel;
    }
    public LocalDate getEvaluationTime() {
        return evaluationTime;
    }

    public void setEvaluationTime(LocalDate evaluationTime) {
        this.evaluationTime = evaluationTime;
    }
    public String getEvaluationOrganization() {
        return evaluationOrganization;
    }

    public void setEvaluationOrganization(String evaluationOrganization) {
        this.evaluationOrganization = evaluationOrganization;
    }
    public String getEvaluationStatus() {
        return evaluationStatus;
    }

    public void setEvaluationStatus(String evaluationStatus) {
        this.evaluationStatus = evaluationStatus;
    }
    public String getSystemStatus() {
        return systemStatus;
    }

    public void setSystemStatus(String systemStatus) {
        this.systemStatus = systemStatus;
    }
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
    public String getControlPoint() {
        return controlPoint;
    }

    public void setControlPoint(String controlPoint) {
        this.controlPoint = controlPoint;
    }
    public String getEvaluationItem() {
        return evaluationItem;
    }

    public void setEvaluationItem(String evaluationItem) {
        this.evaluationItem = evaluationItem;
    }
    public String getResultRecord() {
        return resultRecord;
    }

    public void setResultRecord(String resultRecord) {
        this.resultRecord = resultRecord;
    }
    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }
    public String getRectificationSuggestion() {
        return rectificationSuggestion;
    }

    public void setRectificationSuggestion(String rectificationSuggestion) {
        this.rectificationSuggestion = rectificationSuggestion;
    }
    public BigDecimal getCryptoScore() {
        return cryptoScore;
    }

    public void setCryptoScore(BigDecimal cryptoScore) {
        this.cryptoScore = cryptoScore;
    }
    public BigDecimal getEstimatedScoreAfterFix() {
        return estimatedScoreAfterFix;
    }

    public void setEstimatedScoreAfterFix(BigDecimal estimatedScoreAfterFix) {
        this.estimatedScoreAfterFix = estimatedScoreAfterFix;
    }

    @Override
    public String toString() {
        return "CryptoEvaluation{" +
            "id=" + id +
            ", systemOwnerOrg=" + systemOwnerOrg +
            ", systemName=" + systemName +
            ", businessType=" + businessType +
            ", filingNumber=" + filingNumber +
            ", classificationLevel=" + classificationLevel +
            ", evaluationTime=" + evaluationTime +
            ", evaluationOrganization=" + evaluationOrganization +
            ", evaluationStatus=" + evaluationStatus +
            ", systemStatus=" + systemStatus +
            ", category=" + category +
            ", controlPoint=" + controlPoint +
            ", evaluationItem=" + evaluationItem +
            ", resultRecord=" + resultRecord +
            ", riskLevel=" + riskLevel +
            ", rectificationSuggestion=" + rectificationSuggestion +
            ", cryptoScore=" + cryptoScore +
            ", estimatedScoreAfterFix=" + estimatedScoreAfterFix +
        "}";
    }
}
