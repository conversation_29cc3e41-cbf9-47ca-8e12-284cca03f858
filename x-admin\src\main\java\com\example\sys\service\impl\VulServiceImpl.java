package com.example.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.sys.entity.Vul;
import com.example.sys.mapper.VulMapper;
import com.example.sys.service.IVulService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 漏洞服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@Service
public class VulServiceImpl extends ServiceImpl<VulMapper, Vul> implements IVulService {

    @Override
    public Map<String, Object> getStatistics(String systemOwnerOrg, String systemName, 
                                             String startDate, String endDate) {
        // 构建查询条件
        QueryWrapper<Vul> wrapper = new QueryWrapper<>();
        
        if (StringUtils.hasText(systemOwnerOrg)) {
            wrapper.eq("system_owner_org", systemOwnerOrg);
        }
        if (StringUtils.hasText(systemName)) {
            wrapper.eq("system_name", systemName);
        }
        
        // 处理日期条件（披露日期）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        try {
            if (StringUtils.hasText(startDate) && StringUtils.hasText(endDate)) {
                LocalDate start = LocalDate.parse(startDate, formatter);
                LocalDate end = LocalDate.parse(endDate, formatter);
                wrapper.between("disclosure_date", start, end);
            } else if (StringUtils.hasText(startDate)) {
                LocalDate start = LocalDate.parse(startDate, formatter);
                wrapper.ge("disclosure_date", start);
            } else if (StringUtils.hasText(endDate)) {
                LocalDate end = LocalDate.parse(endDate, formatter);
                wrapper.le("disclosure_date", end);
            }
        } catch (DateTimeParseException e) {
            // 日期格式错误时忽略日期条件
            log.warn("日期格式解析错误: " + e.getMessage());
        }
        
        // 查询数据
        List<Vul> dataList = this.list(wrapper);
        
        // 初始化结果集
        Map<String, Object> result = new LinkedHashMap<>();
        
        // 1. 漏洞级别(vulLevel)分布统计
        Map<String, Long> vulLevelCount = dataList.stream()
            .filter(item -> StringUtils.hasText(item.getVulLevel()))
            .collect(Collectors.groupingBy(
                Vul::getVulLevel,
                Collectors.counting()
            ));
        result.put("vulLevelStatistics", vulLevelCount);
        
        // 2. 漏洞类型(vulType)计数统计
        Map<String, Long> vulTypeCount = dataList.stream()
            .filter(item -> StringUtils.hasText(item.getVulType()))
            .collect(Collectors.groupingBy(
                Vul::getVulType,
                Collectors.counting()
            ));
        result.put("vulTypeStatistics", vulTypeCount);
        
        // 3. 披露日期(disclosureDate)年份统计
        Map<String, Long> disclosureYearCount = dataList.stream()
            .filter(item -> item.getDisclosureDate() != null)
            .map(item -> String.valueOf(item.getDisclosureDate().getYear()))
            .collect(Collectors.groupingBy(
                year -> year,
                Collectors.counting()
            ));
        result.put("disclosureYearStatistics", disclosureYearCount);
        
        // 4. 漏洞状态(vulStatus)统计
        Map<String, Long> vulStatusCount = dataList.stream()
            .filter(item -> StringUtils.hasText(item.getVulStatus()))
            .collect(Collectors.groupingBy(
                Vul::getVulStatus,
                Collectors.counting()
            ));
        result.put("vulStatusStatistics", vulStatusCount);
        
        // 5. 按所属单位(systemOwnerOrg)分组的漏洞级别统计
        Map<String, Map<String, Long>> orgVulLevelCount = dataList.stream()
            .filter(item -> StringUtils.hasText(item.getSystemOwnerOrg()) && 
                           StringUtils.hasText(item.getVulLevel()))
            .collect(Collectors.groupingBy(
                Vul::getSystemOwnerOrg,
                Collectors.groupingBy(
                    Vul::getVulLevel,
                    Collectors.counting()
                )
            ));
        result.put("orgVulLevelStatistics", orgVulLevelCount);
        
        // 6. 业务类型(businessType)漏洞数量统计
        Map<String, Long> businessTypeCount = dataList.stream()
            .filter(item -> StringUtils.hasText(item.getBusinessType()))
            .collect(Collectors.groupingBy(
                Vul::getBusinessType,
                Collectors.counting()
            ));
        result.put("businessTypeStatistics", businessTypeCount);

        return result;
    }
}