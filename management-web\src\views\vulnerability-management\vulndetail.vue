<template>
  <div class="app-container">
    <div v-loading="loading" class="vuln-detail-container">
      <!-- 顶部标题和标签 -->
      <div class="vuln-header">
        <div class="vuln-title">{{ vulnDetail.vul_name || '加载中...' }}</div>
        <div class="vuln-tags">
          <el-tag :type="getRiskLevelType(vulnDetail.vul_level)">{{ vulnDetail.vul_level }}</el-tag>
          <el-tag type="success">{{ vulnDetail.vul_status }}</el-tag>
        </div>
      </div>

      <!-- 关键信息行 -->
      <el-row class="vuln-info-row" gutter="16">
        <el-col :span="6">ZHW编号：{{ vulnDetail.vul_id }}</el-col>
        <el-col :span="6">CVE编号：{{ vulnDetail.cve_number }}</el-col>
        <el-col :span="6">CNVD编号：{{ vulnDetail.cnvd_number }}</el-col>
        <el-col :span="6">CNNVD编号：{{ vulnDetail.cnnvd_number }}</el-col>
      </el-row>
      <el-row class="vuln-info-row" gutter="16">
        <el-col :span="6">CVSS评分：{{ vulnDetail.cvss_score }}</el-col>
        <el-col :span="6">补丁情况：{{ vulnDetail.patch_situation }}</el-col>
        <el-col :span="6">披露时间：{{ vulnDetail.disclosure_date }}</el-col>
      </el-row>

      <!-- 分割线 -->
      <el-divider />

      <!-- 漏洞描述 -->
      <div class="vuln-section">
        <div class="vuln-section-title">漏洞描述</div>
        <div class="vuln-section-content">
          {{ vulnDetail.vul_description }}
        </div>
      </div>

      <!-- 影响范围 -->
      <div class="vuln-section">
        <div class="vuln-section-title">影响范围</div>
        <div class="vuln-section-content">
          {{ vulnDetail.impact_scope }}
        </div>
      </div>

      <!-- 解决建议 -->
      <div class="vuln-section">
        <div class="vuln-section-title">解决建议</div>
        <div class="vuln-section-content">
          {{ vulnDetail.solution_suggestions }}
        </div>
      </div>

      <!-- 参考链接 -->
      <div class="vuln-section">
        <div class="vuln-section-title">参考链接</div>
        <div class="vuln-section-content">
          <template v-if="vulnDetail.reference_link">
            <div v-for="(link, index) in vulnDetail.reference_link.split('\n')" :key="index">
              <a v-if="link.trim()" :href="link.trim()" target="_blank" class="reference-link">{{ link.trim() }}</a>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as vuln from '@/api/vuln'

export default {
  name: 'VulnDetail',
  data() {
    return {
      loading: false,
      vulnDetail: {
        vul_id: 'ZH-Vul-2025-53869',
        vul_name: 'Apache Jackrabbit XXE 漏洞',
        vul_status: 'CWE-654',
        vul_level: '高',
        cve_number: 'CVE-2025-53869',
        cnvd_number: 'N/A',
        cnnvd_number: 'N/A',
        cvss_score: '7.1',
        patch_situation: '官方补丁',
        disclosure_date: '2025-07-14',
        vul_description: '2025年7月，Apache Jackrabbit XXE漏洞（CVE-2025-53689）在互联网上披露。攻击者' +
          '可以通过构造恶意请求利用XXE获取敏感信息。官方已发布安全更新，建议升级到最新版本。',
        impact_scope:
          '2.20.0 <= Apache Jackrabbit < 2.20.17\n' +
          '2.22.0 <= Apache Jackrabbit < 2.22.1\n' +
          '2.23.0-beta <= Apache Jackrabbit < 2.23.2-beta',
        solution_suggestions:
          '升级到最新版本。\n' +
          '使用安全组设置，仅允许受信任地址访问。',
        reference_link:
          'https://jackrabbit.apache.org/jcr/index.html\n' +
          'https://lists.apache.org/thread/5pf9n76ny13pzzk7650g2h3gxdxw7p24'
      }
    }
  },
  created() {
    this.getVulnDetail()
  },
  methods: {
    getVulnDetail() {
      const Id = this.$route.query.id
      if (!Id) {
        this.$message.error('漏洞ID不能为空')
        return
      }
      vuln.fetchVulnerabilityDetail(Id).then(response => {
        this.vulnDetail.vul_id = response.data.vulId
        this.vulnDetail.vul_name = response.data.vulName
        this.vulnDetail.vul_status = response.data.vulType
        this.vulnDetail.vul_level = response.data.vulLevel
        this.vulnDetail.cve_number = response.data.cveNumber
        this.vulnDetail.cnvd_number = response.data.cnvdNumber
        this.vulnDetail.cnnvd_number = response.data.cnnvdNumber
        this.vulnDetail.cvss_score = response.data.cvssScore
        this.vulnDetail.patch_situation = response.data.patchSituation
        this.vulnDetail.disclosure_date = response.data.disclosureDate
        this.vulnDetail.vul_description = response.data.vulDescription
        this.vulnDetail.impact_scope = response.data.impactScope
        this.vulnDetail.solution_suggestions = response.data.solutionSuggestions
        this.vulnDetail.reference_link = response.data.referenceLink
        console.log(response.data)
      }).catch(error => {
        console.error(error)
        this.$message.error('获取漏洞详情失败')
      })
    },
    getRiskLevelType(level) {
      const typeMap = {
        '高': 'danger',
        '中': 'warning',
        '低': 'info'
      }
      return typeMap[level] || 'info'
    }
  }
}
</script>

<style scoped>
.app-container {
    padding: 24px;
    background-color: #f5f7fa;
}

.vuln-detail-container {
    background: #fff;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.vuln-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.vuln-title {
    font-size: 22px;
    font-weight: bold;
    color: #222;
}

.vuln-tags > .el-tag {
    margin-left: 8px;
}

.vuln-info-row {
    font-size: 14px;
    color: #555;
    margin-bottom: 8px;
}

.vuln-section {
    margin-top: 24px;
}

.vuln-section-title {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 12px;
    color: #333;
    border-left: 4px solid #409eff;
    padding-left: 12px;
}

.vuln-section-content {
    font-size: 14px;
    color: #333;
    line-height: 1.8;
    white-space: pre-line;
}

.reference-link {
    color: #409eff;
    text-decoration: none;
}

.reference-link:hover {
    text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .vuln-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .vuln-tags {
        margin-top: 8px;
    }

    .vuln-info-row .el-col {
        margin-bottom: 8px;
    }
}
</style>
