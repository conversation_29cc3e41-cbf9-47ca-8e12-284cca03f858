<template>
    <l-map :zoom="zoom" :center="mapCenter">
      <l-tile-layer :url="tileLayerUrl"></l-tile-layer>
    </l-map>
  </template>
  
  <script>
  import { LMap, LTileLayer } from 'vue2-leaflet';
  import 'leaflet/dist/leaflet.css';
  
  export default {
    components: {
      LMap,
      LTileLayer,
    },
    data() {
      return {
        mapCenter: [35.8617, 104.1954],
        zoom: 4,
        tileLayerUrl: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
      };
    },
  };
  </script>
  
  <style>
  l-map {
    height: 400px;
    width: 100%;
  }
  </style>