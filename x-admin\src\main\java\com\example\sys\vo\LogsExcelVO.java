package com.example.sys.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.example.sys.entity.Logs;
import java.time.LocalDateTime;

/**
 * Excel导出专用VO（包含格式转换注解）
 */
public class LogsExcelVO {

    @ExcelProperty("操作ID")
    private Long id;

    @ExcelProperty("操作用户名")
    private String username;

    @ExcelProperty("操作人姓名")
    private String realname;

    @ExcelProperty("模块名称")
    private String moduleName;

    @ExcelProperty("操作类型")
    private String operationType;

    @ExcelProperty("操作描述")
    private String operationDescription;

    @DateTimeFormat("yyyy-MM-dd HH:mm:ss") // 日期格式化
    @ExcelProperty("操作时间")
    private LocalDateTime operationTime;

    @ExcelProperty("操作IP")
    private String operationIp;

    @ExcelProperty("设备信息")
    private String deviceInfo;

    @ExcelProperty("关联数据ID")
    private Long relatedDataId;

    @ExcelProperty("操作结果")
    private String operationResult;

    @ExcelProperty("错误信息")
    private String errorMessage;

    @ExcelProperty("是否敏感")
    private String isSensitive; // 使用中文显示

    // 实体类转换构造方法
    public LogsExcelVO(Logs log) {
        this.id = log != null && log.getId() != null ? log.getId() : 0L; // 如果 id 为 null，默认设置为 0
        this.username = log != null && log.getUsername() != null ? log.getUsername() : ""; // 如果 username 为 null，默认设置为空字符串
        this.realname = log != null && log.getRealname() != null ? log.getRealname() : ""; // 如果 realname 为 null，默认设置为空字符串
        this.moduleName = log != null && log.getModuleName() != null ? log.getModuleName() : ""; // 如果 moduleName 为 null，默认设置为空字符串
        this.operationType = log != null && log.getOperationType() != null ? log.getOperationType() : ""; // 如果 operationType 为 null，默认设置为空字符串
        this.operationDescription = log != null && log.getOperationDescription() != null ? log.getOperationDescription() : ""; // 如果 operationDescription 为 null，默认设置为空字符串
        this.operationTime = log != null && log.getOperationTime() != null ? log.getOperationTime() : LocalDateTime.now(); // 如果 operationTime 为 null，默认设置为当前时间
        this.operationIp = log != null && log.getOperationIp() != null ? log.getOperationIp() : ""; // 如果 operationIp 为 null，默认设置为空字符串
        this.deviceInfo = log != null && log.getDeviceInfo() != null ? log.getDeviceInfo() : ""; // 如果 deviceInfo 为 null，默认设置为空字符串
        this.relatedDataId = log != null && log.getRelatedDataId() != null ? log.getRelatedDataId() : 0L; // 如果 relatedDataId 为 null，默认设置为 0
        this.operationResult = log != null && log.getOperationResult() != null ? log.getOperationResult() : ""; // 如果 operationResult 为 null，默认设置为空字符串
        this.errorMessage = log != null && log.getErrorMessage() != null ? log.getErrorMessage() : ""; // 如果 errorMessage 为 null，默认设置为空字符串
        this.isSensitive = log != null && log.getIsSensitive() != null ? (log.getIsSensitive() ? "是" : "否") : ""; // 如果 isSensitive 为 null，默认设置为空字符串
    }
    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getRealname() {
        return realname;
    }

    public void setRealname(String realname) {
        this.realname = realname;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getOperationDescription() {
        return operationDescription;
    }

    public void setOperationDescription(String operationDescription) {
        this.operationDescription = operationDescription;
    }

    public LocalDateTime getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(LocalDateTime operationTime) {
        this.operationTime = operationTime;
    }

    public String getOperationIp() {
        return operationIp;
    }

    public void setOperationIp(String operationIp) {
        this.operationIp = operationIp;
    }

    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public Long getRelatedDataId() {
        return relatedDataId;
    }

    public void setRelatedDataId(Long relatedDataId) {
        this.relatedDataId = relatedDataId;
    }

    public String getOperationResult() {
        return operationResult;
    }

    public void setOperationResult(String operationResult) {
        this.operationResult = operationResult;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getIsSensitive() {
        return isSensitive;
    }

    public void setIsSensitive(String isSensitive) {
        this.isSensitive = isSensitive;
    }
}

