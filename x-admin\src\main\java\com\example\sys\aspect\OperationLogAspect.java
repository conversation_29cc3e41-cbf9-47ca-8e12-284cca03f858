package com.example.sys.aspect;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.example.sys.annotation.OperationLog;
import com.example.sys.entity.Logs;
import com.example.sys.entity.User;
import com.example.sys.mapper.UserMapper;
import com.example.sys.service.ILogsService;
import com.example.sys.utils.IPUtils;
import com.example.sys.utils.UserAgentUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import com.example.commom.vo.Result;

@Aspect
@Component
@Slf4j
public class OperationLogAspect {

    private final ILogsService logsService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final UserAgentUtils userAgentUtils;
    private final SpelExpressionParser spelParser = new SpelExpressionParser();
    @Autowired
    private UserMapper userMapper; // 新增数据库查询依赖

    // 与 UserServiceImpl 统一的 Redis 键前缀
    private static final String USER_TOKEN_PREFIX = "user:";

    @Autowired
    public OperationLogAspect(ILogsService logsService,
                             RedisTemplate<String, Object> redisTemplate,
                             UserAgentUtils userAgentUtils) {
        this.logsService = logsService;
        this.redisTemplate = redisTemplate;
        this.userAgentUtils = userAgentUtils;
    }

    @Around("@annotation(com.example.sys.annotation.OperationLog)")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        Logs logEntity = new Logs();
        Object result = null;
        Throwable ex = null;
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String tokenForLogout = request.getHeader("X-Token"); // 提前获取退出时的 token

        try {
            result = pjp.proceed();
            return result;
        } catch (Throwable e) {
            ex = e;
            throw e;
        } finally {
            try {
                Method method = ((MethodSignature) pjp.getSignature()).getMethod();
                OperationLog annotation = method.getAnnotation(OperationLog.class);
                if (annotation == null) return null;

                buildLog(pjp, annotation, logEntity, result, ex, tokenForLogout); // 传递 token 给构建方法
                logsService.saveLog(logEntity);
            } catch (Exception e) {
                log.error("操作日志记录失败: {}", e.getMessage(), e);
            }
        }
    }

    private void buildLog(ProceedingJoinPoint pjp,
                          OperationLog annotation,
                          Logs logEntity,
                          Object result,
                          Throwable ex,
                          String tokenForLogout) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

        // 设置基础信息
        logEntity.setOperationTime(LocalDateTime.now());
        logEntity.setOperationIp(IPUtils.getClientIp(request));
        logEntity.setDeviceInfo(userAgentUtils.parseUserAgent(request.getHeader("User-Agent")));
        logEntity.setModuleName(annotation.moduleName());
        logEntity.setOperationType(annotation.operationType());
        logEntity.setIsSensitive(annotation.isSensitive());

        EvaluationContext context = createSpelContext(pjp, request, result);

        // 解析 SpEL 表达式
        resolveSpel(annotation.desc(), context, logEntity::setOperationDescription);
        resolveSpel(annotation.dataId(), context, value -> {
            try {
                if (value != null) {
                    logEntity.setRelatedDataId(Long.parseLong(value.toString()));
                }
            } catch (NumberFormatException e) {
                log.warn("dataId格式转换失败: {}", value);
            }
        });

        // 处理用户信息（核心修正部分）
        User currentUser = null;
        if ("登录".equals(annotation.operationType())) {
            // 显式获取 String 类型的 username
            String usernameStr = null;
            Object userParam = context.lookupVariable("user");
            if (userParam instanceof User) {
                usernameStr = ((User) userParam).getUsername();
            }
            if (StringUtils.isNotEmpty(usernameStr)) { // 修正后的判断
                currentUser = userMapper.selectOne(
                    new LambdaQueryWrapper<User>().eq(User::getUsername, usernameStr)
                );
            }
        } else if ("退出".equals(annotation.operationType())) {
            // 退出时使用提前获取的 token（未被删除）
            currentUser = getCurrentUserByToken(tokenForLogout);
        } else {
            // 其他操作正常从请求头获取
            currentUser = getCurrentUser(request);
        }

        if (currentUser != null) {
            logEntity.setUsername(currentUser.getUsername());
            logEntity.setRealname(currentUser.getRealname());
        }

        // 设置操作结果
        logEntity.setOperationResult(ex == null ? "成功" : "失败");
        if (ex != null) {
            logEntity.setErrorMessage(ex.getMessage() != null ? ex.getMessage() : ex.getClass().getSimpleName());
        }
    }

    private EvaluationContext createSpelContext(ProceedingJoinPoint pjp, HttpServletRequest request, Object result) {
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        EvaluationContext context = new StandardEvaluationContext();
        
        // 1. 注入方法参数
        String[] paramNames = signature.getParameterNames();
        Object[] args = pjp.getArgs();
        for (int i = 0; i < paramNames.length; i++) {
            context.setVariable(paramNames[i], args[i]);
        }
        
        // 2. 注入通用变量（result、request）
        context.setVariable("result", result);
        context.setVariable("request", request);
        
        // 3. 关键：解析 Result 对象的 data 字段，注入到上下文（变量名固定为 data）
        if (result instanceof com.example.commom.vo.Result<?>) { // 确保包路径正确
            com.example.commom.vo.Result<?> resultObj = (com.example.commom.vo.Result<?>) result;
            Object data = resultObj.getData(); // 获取 Result 中的 data 字段
            context.setVariable("data", data); // 注入到上下文，变量名为 data
        }
        
        return context;
    }

    private void resolveSpel(String expression, EvaluationContext context, SpelSetter setter) {
        if (StringUtils.isEmpty(expression)) return;
        try {
            Expression exp = spelParser.parseExpression(expression);
            Object value = exp.getValue(context);
            setter.accept(value != null ? value.toString() : expression);
        } catch (Exception e) {
            log.warn("SpEL 解析失败: 表达式 [{}]，异常: {}", expression, e.getMessage(), e);
            setter.accept(expression);
        }
    }

    private User getCurrentUser(HttpServletRequest request) {
        String token = request.getHeader("X-Token");
        if (StringUtils.isNotEmpty(token)) {
            String userKey = USER_TOKEN_PREFIX + token;
            Object userObj = redisTemplate.opsForValue().get(userKey);
            return userObj instanceof User ? (User) userObj : null;
        }
        return null;
    }

    private User getCurrentUserByToken(String token) {
        if (StringUtils.isEmpty(token)) return null;
        String userKey = USER_TOKEN_PREFIX + token;
        Object userObj = redisTemplate.opsForValue().get(userKey);
        return userObj instanceof User ? (User) userObj : null;
    }

    @FunctionalInterface
    private interface SpelSetter {
        void accept(String value);
    }
}