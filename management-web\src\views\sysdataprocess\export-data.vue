<template>
  <div class="app-container">

    <!-- 搜索栏 -->
    <div class="search-group">
      <el-input v-model="searchKeyword" placeholder="搜索标题" style="width: 300px;" @keyup.enter="handleSearch"></el-input>
      <el-button @click="handleSearch" type="primary" icon="el-icon-search" style="margin-left: 10px;">搜索</el-button>
    </div>

    <!-- 表单区域 -->
    <div class="button-group">
      <FilenameOption v-model="filename" /> <!-- 文件名选项组件 -->
      <AutoWidthOption v-model="autoWidth" /> <!-- 自动宽度选项组件 -->
      <BookTypeOption v-model="bookType" /> <!-- 书籍类型选项组件 -->
      <el-button :loading="downloadLoading" type="primary" icon="el-icon-document" @click="handleDownload">
        导出
      </el-button>
      <el-button @click="handleCreate" type="primary" icon="el-icon-plus" style="margin-left: 10px;">
        创建
      </el-button>
    </div>

    <!-- 表格区域 -->
    <el-table
      ref="multipleTable"
      v-loading="listLoading"
      :data="filteredList"
      element-loading-text="Loading..."
      border
      fit
      highlight-current-row
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" /> <!-- 多选框列 -->
      <el-table-column align="center" label="编号" width="95">
        <template slot-scope="scope">
          {{ scope.$index }}
        </template>
      </el-table-column>
      <el-table-column label="标题">
        <template slot-scope="scope">
          {{ scope.row.title }}
        </template>
      </el-table-column>
      <el-table-column label="作者" width="110" align="center">
        <template slot-scope="scope">
          <el-tag>{{ scope.row.author }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="浏览量" width="115" align="center">
        <template slot-scope="scope">
          {{ scope.row.pageviews }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="日期" width="220">
        <template slot-scope="scope">
          <i class="el-icon-time" />
          <span>{{ scope.row.timestamp | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 创建/编辑表单 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
      <el-form :model="formData">
        <el-form-item label="标题">
          <el-input v-model="formData.title" />
        </el-form-item>
        <el-form-item label="作者">
          <el-input v-model="formData.author" />
        </el-form-item>
        <el-form-item label="浏览量">
          <el-input v-model="formData.pageviews" />
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker v-model="formData.timestamp" type="datetime" placeholder="选择日期时间" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">完成</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
// import { fetchList } from '@/api/article'
import { parseTime } from '@/utils'
import FilenameOption from './components/FilenameOption'
import AutoWidthOption from './components/AutoWidthOption'
import BookTypeOption from './components/BookTypeOption'

export default {
  name: 'ExportExcel',
  components: { FilenameOption, AutoWidthOption, BookTypeOption },
  data() {
    return {
      list: null, // 表格数据
      filteredList: [], // 过滤后的表格数据
      listLoading: true, // 加载状态
      downloadLoading: false, // 下载按钮加载状态
      filename: '', // 文件名
      autoWidth: true, // 是否自动宽度
      bookType: 'xlsx', // 导出文件类型
      multipleSelection: [], // 多选的数据
      dialogVisible: false, // 弹出框可见性
      dialogTitle: '', // 弹出框标题
      formData: { // 表单数据
        title: '',
        author: '',
        pageviews: 0,
        timestamp: ''
      },
      isEditing: false, // 是否为编辑状态
      editIndex: -1, // 正在编辑的数据索引
      searchKeyword: '' // 搜索关键字
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      this.listLoading = true
      fetchList().then(response => {
        this.list = response.data.items
        this.filteredList = this.list
        this.listLoading = false
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleDownload() {
      if (this.multipleSelection.length) {
        this.downloadLoading = true
        import('@/vendor/Export2Excel').then(excel => {
          const tHeader = ['编号', '标题', '作者', '浏览量', '日期'] // 表头
          const filterVal = ['id', 'title', 'author', 'pageviews', 'timestamp'] // 过滤字段
          const list = this.multipleSelection
          const data = this.formatJson(filterVal, list)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.filename,
            autoWidth: this.autoWidth,
            bookType: this.bookType
          })
          this.$refs.multipleTable.clearSelection()
          this.downloadLoading = false
        })
      } else {
        this.$message({
          message: '请选择至少一项',
          type: 'warning'
        })
      }
    },
    handleCreate() {
      this.dialogTitle = '创建新数据'
      this.formData = {
        title: '',
        author: '',
        pageviews: 0,
        timestamp: ''
      }
      this.isEditing = false
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.dialogTitle = '编辑数据'
      this.formData = { ...row }
      this.isEditing = true
      this.editIndex = this.list.indexOf(row)
      this.dialogVisible = true
    },
    handleDelete(row) {
      this.$confirm('确认删除该信息？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.list.indexOf(row)
        if (index !== -1) {
          this.list.splice(index, 1)
          this.filteredList = this.list.filter(item => item.title.includes(this.searchKeyword))
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    submitForm() {
      if (this.isEditing) {
        this.$set(this.list, this.editIndex, { ...this.formData })
        this.filteredList = this.list.filter(item => item.title.includes(this.searchKeyword))
      } else {
        this.list.push({ ...this.formData, id: this.list.length + 1 })
        this.filteredList = this.list.filter(item => item.title.includes(this.searchKeyword))
      }
      this.dialogVisible = false
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },
    handleSearch() {
      if (this.searchKeyword) {
        this.filteredList = this.list.filter(item => item.title.includes(this.searchKeyword))
      } else {
        this.filteredList = this.list
      }
    }
  }
}
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
}

.search-group {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.button-group {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.button-group .el-button {
  margin-left: 10px;
}

.radio-label {
  font-size: 14px;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 30px;
}
</style>