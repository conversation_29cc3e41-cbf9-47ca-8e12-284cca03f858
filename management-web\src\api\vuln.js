import request from '@/utils/request'

export function fetchVulnList(query) {
  return request({
    url: '/vue-element-admin/vuln/list',
    method: 'get',
    params: query
  })
}

export function fetchVulnDetail(id) {
  return request({
    url: `/vue-element-admin/vuln/detail/${id}`,
    method: 'get'
  })
}

// 漏洞贡献相关API
export function fetchVulnContributionList(query) {
  return request({
    url: '/vue-element-admin/vuln/contribution/list',
    method: 'get',
    params: query
  })
}

export function createVulnContribution(data) {
  return request({
    url: '/vue-element-admin/vuln/contribution/create',
    method: 'post',
    data
  })
}

export function updateVulnContribution(id, data) {
  return request({
    url: `/vue-element-admin/vuln/contribution/update/${id}`,
    method: 'put',
    data
  })
}

export function deleteVulnContribution(id) {
  return request({
    url: `/vue-element-admin/vuln/contribution/delete/${id}`,
    method: 'delete'
  })
}

export function submitVulnContribution(id) {
  return request({
    url: `/vue-element-admin/vuln/contribution/submit/${id}`,
    method: 'post'
  })
}

export function withdrawVulnContribution(id) {
  return request({
    url: `/vue-element-admin/vuln/contribution/withdraw/${id}`,
    method: 'post'
  })
}

// 漏洞资源相关API
export function fetchVulnResourceList(query) {
  return request({
    url: '/vue-element-admin/vuln/resource/list',
    method: 'get',
    params: query
  })
}

export function createVulnResource(data) {
  return request({
    url: '/vue-element-admin/vuln/resource/create',
    method: 'post',
    data
  })
}

export function updateVulnResource(id, data) {
  return request({
    url: `/vue-element-admin/vuln/resource/update/${id}`,
    method: 'put',
    data
  })
}

export function deleteVulnResource(id) {
  return request({
    url: `/vue-element-admin/vuln/resource/delete/${id}`,
    method: 'delete'
  })
}

export function submitVulnResource(id) {
  return request({
    url: `/vue-element-admin/vuln/resource/submit/${id}`,
    method: 'post'
  })
}

export function withdrawVulnResource(id) {
  return request({
    url: `/vue-element-admin/vuln/resource/withdraw/${id}`,
    method: 'post'
  })
}

// 漏洞管理列表API - 获取真实数据库数据
export function fetchVulnerabilityList(query) {
  return request({
    url: '/vul/list',
    method: 'get',
    params: query
  })
}

export function fetchVulnerabilityDetail(id) {
  return request({
    url: `/vul/${id}`,
    method: 'get'
  })
}

// 审核员漏洞知识审核相关API
export function fetchVulnKnowledgeReviewList(query) {
  return request({
    url: '/vue-element-admin/vuln/knowledge/review/list',
    method: 'get',
    params: query
  })
}

export function getVulnKnowledgeReviewDetail(id) {
  return request({
    url: `/vue-element-admin/vuln/knowledge/review/detail/${id}`,
    method: 'get'
  })
}

export function approveVulnKnowledge(id, data) {
  return request({
    url: `/vue-element-admin/vuln/knowledge/review/approve/${id}`,
    method: 'post',
    data
  })
}

export function rejectVulnKnowledge(id, data) {
  return request({
    url: `/vue-element-admin/vuln/knowledge/review/reject/${id}`,
    method: 'post',
    data
  })
}