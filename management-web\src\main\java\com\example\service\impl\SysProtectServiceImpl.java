package com.example.service.impl;

import com.example.common.core.domain.AjaxResult;
import com.example.common.exception.ServiceException;
import com.example.service.ISysProtectService;
import com.example.utils.ExcelUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Service
public class SysProtectServiceImpl implements ISysProtectService {

    @Override
    public AjaxResult importData(MultipartFile file) throws Exception {
        if (file == null) {
            throw new ServiceException("请选择要导入的文件");
        }

        // 获取文件名
        String fileName = file.getOriginalFilename();
        if (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls")) {
            throw new ServiceException("请上传Excel文件");
        }

        // 读取Excel数据
        List<List<String>> dataList = ExcelUtil.readExcel(file.getInputStream());
        if (dataList == null || dataList.isEmpty()) {
            throw new ServiceException("Excel文件内容为空");
        }

        // 移除表头
        dataList.remove(0);

        // TODO: 处理导入的数据，保存到数据库
        // 这里需要根据实际业务逻辑实现数据保存

        return AjaxResult.success("导入成功");
    }

    @Override
    public void export(HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = "等保数据.xlsx";
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);

        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("等保数据");

        // 创建表头
        String[] headers = {"所属单位", "系统名称", "业务类型", "备案号", "定级级别", "测评时间", 
                          "测评结果", "计划测评时间", "测评单位", "系统状态"};
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // TODO: 从数据库获取数据并写入Excel
        // 这里需要根据实际业务逻辑实现数据查询和写入

        // 写入响应流
        workbook.write(response.getOutputStream());
        workbook.close();
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = "等保数据导入模板.xlsx";
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);

        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("等保数据");

        // 创建表头
        String[] headers = {"所属单位", "系统名称", "业务类型", "备案号", "定级级别", "测评时间", 
                          "测评结果", "计划测评时间", "测评单位", "系统状态"};
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // 添加数据验证（下拉列表）
        DataValidationHelper dvHelper = sheet.getDataValidationHelper();
        
        // 业务类型下拉列表
        String[] businessTypes = {"生产作业", "指挥调度", "内部办公", "公众服务", "其他"};
        DataValidationConstraint businessTypeConstraint = dvHelper.createExplicitListConstraint(businessTypes);
        CellRangeAddressList businessTypeRange = new CellRangeAddressList(1, 1000, 2, 2);
        DataValidation businessTypeValidation = dvHelper.createValidation(businessTypeConstraint, businessTypeRange);
        sheet.addValidationData(businessTypeValidation);

        // 定级级别下拉列表
        String[] levels = {"一级", "二级", "三级", "四级", "五级"};
        DataValidationConstraint levelConstraint = dvHelper.createExplicitListConstraint(levels);
        CellRangeAddressList levelRange = new CellRangeAddressList(1, 1000, 4, 4);
        DataValidation levelValidation = dvHelper.createValidation(levelConstraint, levelRange);
        sheet.addValidationData(levelValidation);

        // 测评结果下拉列表
        String[] results = {"符合", "基本符合", "不符合"};
        DataValidationConstraint resultConstraint = dvHelper.createExplicitListConstraint(results);
        CellRangeAddressList resultRange = new CellRangeAddressList(1, 1000, 6, 6);
        DataValidation resultValidation = dvHelper.createValidation(resultConstraint, resultRange);
        sheet.addValidationData(resultValidation);

        // 系统状态下拉列表
        String[] statuses = {"运行中", "已下线", "已注销"};
        DataValidationConstraint statusConstraint = dvHelper.createExplicitListConstraint(statuses);
        CellRangeAddressList statusRange = new CellRangeAddressList(1, 1000, 9, 9);
        DataValidation statusValidation = dvHelper.createValidation(statusConstraint, statusRange);
        sheet.addValidationData(statusValidation);

        // 写入响应流
        workbook.write(response.getOutputStream());
        workbook.close();
    }
} 