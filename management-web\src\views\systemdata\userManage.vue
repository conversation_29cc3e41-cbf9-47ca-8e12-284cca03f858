<template>
    <div class="app-container">
    <!-- 搜索栏 -->
     <el-card id="search">
     <el-row>
        <el-col :span="20">
            <el-input v-model="listQuery.username" placeholder="姓名" clearable="ture"></el-input>
            <el-select v-model="listQuery.role" placeholder="请选择权限" clearable="true">
        <el-option
          v-for="item in roleOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
            <el-button style="margin-left: 10px;"  @click="getUserList" type="primary" round icon="el-icon-search">搜索</el-button>
        </el-col>
        <el-col :span="4" align="right">
            <el-button icon="el-icon-plus" type="primary" @click="openEditUI(null)" circle></el-button>
        </el-col>
    </el-row>
    </el-card>


    <!-- 结果表格 -->
     <el-card id="result">
        <el-table
    :data="userList"
    stripe
    style="width: 100%">
    <el-table-column
      label="序号"
      width="80">
      <template slot-scope="scope">
      <!-- 索引 (pageNo-1)*pageSize+index+1-->
    {{ (listQuery.pageNo-1)*listQuery.pageSize+scope.$index+1 }}
    </template>"
    </el-table-column>
    <el-table-column
      prop="id"
      label="用户ID" width="80px">
    </el-table-column>
    <el-table-column
      prop="username"
      label="用户名"
      width="80px">
    </el-table-column>
    <el-table-column
      prop="email"
      label="邮箱" align="center">
    </el-table-column>
    <el-table-column
      prop="phone"
      label="电话" align="center">
    </el-table-column>
    <el-table-column
      prop="role"
      label="权限" width="80px">
    </el-table-column>
    <el-table-column
      prop="organization"
      label="单位" width="160px" align="center">
    </el-table-column>
    <el-table-column
      prop="reviewer"
      label="审查者" width="80px">
    </el-table-column>
    <el-table-column
      prop="status"
      label="状态" width="80px" align="center">
      <template slot-scope="scope">
        <el-tag v-if="scope.row.status==1">正常</el-tag>
        <el-tag type="danger" v-else>禁用</el-tag>
    </template>
    </el-table-column>
    <el-table-column
      label="操作" align="center">
      <template slot-scope="scope">
        <el-button @click="openEditUI(scope.row.id)" type="primary" icon="el-icon-edit" size="mini"></el-button>
        <el-button @click="deleteUser(scope.row)" type="danger" icon="el-icon-delete" size="mini"></el-button>
    </template>
    </el-table-column>
  </el-table>
    </el-card>

    <!-- 分页 -->
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="listQuery.pageNo"
      :page-sizes="[5, 10, 20, 40]"
      :page-size="listQuery.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total">
    </el-pagination>

<!-- 用户信息添加表单 -->
      <el-dialog @close="clearForm" :title="title" :visible.sync="dialogFormVisible">
    <el-form :model="userForm" ref="userFormRef" :rules="rules">
      <el-form-item label="用户名" prop="username" :label-width="formLabelWidth">
        <el-input v-model="userForm.username" autocomplete="off"></el-input>
      </el-form-item>
      <el-form-item v-if="userForm.id == null|| userForm.id == undefined" label="密码" prop="password" :label-width="formLabelWidth">
        <el-input type="password" v-model="userForm.password" autocomplete="off"></el-input>
      </el-form-item>
      <el-form-item label="邮箱" prop="email" :label-width="formLabelWidth">
        <el-input v-model="userForm.email" autocomplete="off"></el-input>
      </el-form-item>
      <el-form-item label="电话" prop="phone" :label-width="formLabelWidth">
        <el-input v-model="userForm.phone" autocomplete="off"></el-input>
      </el-form-item>
      <el-form-item label="权限" prop="role" :label-width="formLabelWidth">
    <!-- 使用 el-select 来选择角色 -->
    <el-select v-model="userForm.role" placeholder="请选择权限">
      <el-option
        v-for="item in roleOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value">
      </el-option>
    </el-select>
  </el-form-item>


      <el-form-item label="单位" prop="organization" :label-width="formLabelWidth">
        <el-input v-model="userForm.organization" autocomplete="off"></el-input>
      </el-form-item>
      <el-form-item label="审查者" prop="reviewer" :label-width="formLabelWidth">
        <el-input v-model="userForm.reviewer" autocomplete="off"></el-input>
      </el-form-item>
      <el-form-item label="状态" :label-width="formLabelWidth">
        <el-switch
          v-model="userForm.status"
          :active-value="1"
          :inactive-value="0"
        >
        </el-switch>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogFormVisible = false">取 消</el-button>
      <el-button type="primary" @click="saveUser">确 定</el-button>
    </div>
  </el-dialog>
    </div>
</template>

<script>
import userApi from '@/api/userManage'
export default {
    name: 'userManage',
    data(){
      var checkEmail = (rule, value, callback) => {
        var reg= /^[a-zA-Z0-9]+([-_.][a-zA-Z0-9]+)*@[a-zA-Z0-9]+([-_.][a-zA-Z0-9]+)*\.[a-z]{2,}$/
        if (!reg.test(value)) {
          return callback(new Error('邮箱格式不符'));
        }
        callback();
      };
        return{
            userForm:{},
            formLabelWidth: '130px',
            dialogFormVisible: false,
            totle: "",
            total:0,
            listQuery:{
                pageNo:1,
                pageSize:10
            },
            userList:[],
            rules:{
              username:[
                {required:true,message:"请输入用户名",trigger:"blur"},
              ],
              password:[
                {required:true,message:"请输入密码",trigger:"blur"},
                { min: 6, max: 16, message: '长度在6到16个字符', trigger: 'blur' }
              ],
              email:[
                {validator: checkEmail, trigger: 'blur'}
            ],
              role:[
                {required:true,message:"请输入权限",trigger:"blur"}
              ],
              organization:[
                {required:true,message:"请输入单位",trigger:"blur"}
              ],
              reviewer:[
                {required:true,message:"请输入审查者",trigger:"blur"}
              ]
            },
            roleOptions: [
        { value: '用户', label: '用户' },
        { value: '管理员', label: '管理员' }
      ],
        }
    },
    methods:{
        saveUser(){
          //触发表单验证
          this.$refs.userFormRef.validate((valid) => {
          if (valid) {
            //提交表单给后端
            userApi.saveUser(this.userForm).then(response => {
              //成功提示
              this.$message({
                message: response.message,
                type: 'success'
              });
              //关闭对话框
              this.dialogFormVisible = false;
              //刷新列表
              this.getUserList();
            });
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      deleteUser(user){
        this.$confirm(`您确认删除用户 ${user.username} 吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          userApi.deleteUserByID(user.id).then(response => {
            this.$message({
            type: 'success',
            message: response.message
          });
            //刷新列表
            this.getUserList();
          });
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
      },
        clearForm(){//清除表单数据
            this.userForm = {};
            this.$refs.userFormRef.clearValidate();//清除之前的验证信息
        },
        openEditUI(id){
          if(id == null){
            this.title = "添加用户";
          }else{
            //根据id获取用户信息
            userApi.getUserByID(id).then(response => {
              this.userForm = response.data;
              this.title = "修改用户";
            });
          }
            this.dialogFormVisible = true;
        },
        handleSizeChange(pageSize){
            this.listQuery.pageSize = pageSize;
            this.getUserList();
        },
        handleCurrentChange(pageNo){
            this.listQuery.pageNo = pageNo;
            this.getUserList();

        },
        getUserList(){
            userApi.getUserList(this.listQuery).then(response => {
                this.userList = response.data.rows;
                this.total = response.data.total;
            });
        }

    },
    created(){
        this.getUserList();
    }
            
}

</script>

<style scoped>
#search .el-input{
    width: 200px;
    margin-right: 10px;
}
.el-dialog .el-input{
  width: 80%;
}
</style>