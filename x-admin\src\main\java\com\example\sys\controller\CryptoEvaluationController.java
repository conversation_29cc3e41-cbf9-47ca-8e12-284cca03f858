package com.example.sys.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.commom.vo.Result;
import com.example.sys.annotation.OperationLog;
import com.example.sys.entity.CryptoEvaluation;
import com.example.sys.service.ICryptoEvaluationService;
import com.example.sys.vo.CryptoEvaluationExcelVO;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.apache.pdfbox.pdmodel.PDDocument.load;

/**
 * <p>
 * 密码测评控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@RestController
@RequestMapping("/cryptoEvaluation")
public class CryptoEvaluationController {
    @Autowired
    private ICryptoEvaluationService cryptoEvaluationService;

    @GetMapping("/list")
    public Result<Map<String, Object>> getCryptoEvaluationList(
            @RequestParam(value = "systemOwnerOrg", required = false) String systemOwnerOrg,
            @RequestParam(value = "systemName", required = false) String systemName,
            @RequestParam(value = "businessType", required = false) String businessType,
            @RequestParam(value = "filingNumber", required = false) String filingNumber,
            @RequestParam(value = "classificationLevel", required = false) String classificationLevel,
            @RequestParam(value = "evaluationOrganization", required = false) String evaluationOrganization,
            @RequestParam(value = "evaluationStatus", required = false) String evaluationStatus,
            @RequestParam(value = "systemStatus", required = false) String systemStatus,
            @RequestParam(value = "category", required = false) String category,
            @RequestParam(value = "controlPoint", required = false) String controlPoint,
            @RequestParam(value = "evaluationItem", required = false) String evaluationItem,
            @RequestParam(value = "resultRecord", required = false) String resultRecord,
            @RequestParam(value = "riskLevel", required = false) String riskLevel,
            @RequestParam(value = "rectificationSuggestion", required = false) String rectificationSuggestion,
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "pageNo") Long pageNo,
            @RequestParam(value = "pageSize") Long pageSize
    ) {
        LambdaQueryWrapper<CryptoEvaluation> wrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        if (StringUtils.hasLength(systemOwnerOrg)) {
            wrapper.eq(StringUtils.hasLength(systemOwnerOrg), CryptoEvaluation::getSystemOwnerOrg, systemOwnerOrg);
        }
        if (StringUtils.hasLength(systemName)) {
            wrapper.eq(StringUtils.hasLength(systemName), CryptoEvaluation::getSystemName, systemName);
        }
        if (StringUtils.hasLength(businessType)) {
            wrapper.eq(StringUtils.hasLength(businessType), CryptoEvaluation::getBusinessType, businessType);
        }
        if (StringUtils.hasLength(filingNumber)) {
            wrapper.eq(StringUtils.hasLength(filingNumber), CryptoEvaluation::getFilingNumber, filingNumber);
        }
        if (StringUtils.hasLength(classificationLevel)) {
            wrapper.eq(StringUtils.hasLength(classificationLevel), CryptoEvaluation::getClassificationLevel, classificationLevel);
        }
        if (StringUtils.hasLength(evaluationOrganization)) {
            wrapper.eq(StringUtils.hasLength(evaluationOrganization), CryptoEvaluation::getEvaluationOrganization, evaluationOrganization);
        }
        if (StringUtils.hasLength(evaluationStatus)) {
            wrapper.eq(StringUtils.hasLength(evaluationStatus), CryptoEvaluation::getEvaluationStatus, evaluationStatus);
        }
        if (StringUtils.hasLength(systemStatus)) {
            wrapper.eq(StringUtils.hasLength(systemStatus), CryptoEvaluation::getSystemStatus, systemStatus);
        }
        if (StringUtils.hasLength(category)) {
            wrapper.eq(StringUtils.hasLength(category), CryptoEvaluation::getCategory, category);
        }
        if (StringUtils.hasLength(controlPoint)) {
            wrapper.eq(StringUtils.hasLength(controlPoint), CryptoEvaluation::getControlPoint, controlPoint);
        }
        if (StringUtils.hasLength(evaluationItem)) {
            wrapper.eq(StringUtils.hasLength(evaluationItem), CryptoEvaluation::getEvaluationItem, evaluationItem);
        }
        if (StringUtils.hasLength(resultRecord)) {
            wrapper.eq(StringUtils.hasLength(resultRecord), CryptoEvaluation::getResultRecord, resultRecord);
        }
        if (StringUtils.hasLength(riskLevel)) {
            wrapper.eq(StringUtils.hasLength(riskLevel), CryptoEvaluation::getRiskLevel, riskLevel);
        }
        if (StringUtils.hasLength(rectificationSuggestion)) {
            wrapper.eq(StringUtils.hasLength(rectificationSuggestion), CryptoEvaluation::getRectificationSuggestion, rectificationSuggestion);
        }

        // 处理日期范围查询
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if (StringUtils.hasLength(startTime) && StringUtils.hasLength(endTime)) {
            try {
                LocalDate start = LocalDate.parse(startTime, formatter);
                LocalDate end = LocalDate.parse(endTime, formatter);
                wrapper.between(CryptoEvaluation::getEvaluationTime, start, end);
            } catch (DateTimeParseException e) {
                // 日期格式错误时忽略该条件
            }
        } else if (StringUtils.hasLength(startTime)) {
            try {
                LocalDate start = LocalDate.parse(startTime, formatter);
                wrapper.ge(CryptoEvaluation::getEvaluationTime, start);
            } catch (DateTimeParseException e) {
                // 日期格式错误时忽略该条件
            }
        } else if (StringUtils.hasLength(endTime)) {
            try {
                LocalDate end = LocalDate.parse(endTime, formatter);
                wrapper.le(CryptoEvaluation::getEvaluationTime, end);
            } catch (DateTimeParseException e) {
                // 日期格式错误时忽略该条件
            }
        }

        Page<CryptoEvaluation> page = new Page<>(pageNo, pageSize);
        Page<CryptoEvaluation> cryptoEvaluationPage = cryptoEvaluationService.page(page, wrapper);

        Map<String, Object> data = new HashMap<>();
        data.put("cryptoEvaluation", cryptoEvaluationPage.getRecords());
        data.put("total", cryptoEvaluationPage.getTotal());
        data.put("pages", cryptoEvaluationPage.getPages());

        return Result.success(data);
    }

    // 新增密码测评数据
    @PostMapping("/add")
    @OperationLog(
            moduleName = "密码测评管理",
            operationType = "新增",
            desc = "'新增密码测评：' + #data.systemName",
            isSensitive = true
    )
    public Result<?> addCryptoEvaluation(@RequestBody CryptoEvaluation cryptoEvaluation) {
        cryptoEvaluationService.save(cryptoEvaluation);
        return Result.success("新增成功");
    }

    // 删除密码测评数据
    @DeleteMapping("/{id}")
    @OperationLog(
            moduleName = "密码测评管理",
            operationType = "删除",
            desc = "'删除密码测评：' + #data.systemName",
            dataId = "#id",
            isSensitive = true
    )
    public Result<?> delete(@PathVariable("id") Integer id) {
        CryptoEvaluation cryptoEvaluation = cryptoEvaluationService.getById(id);
        cryptoEvaluationService.removeById(id);
        return Result.success(cryptoEvaluation, "删除成功");
    }

    // 修改密码测评数据
    @PutMapping("/update")
    @OperationLog(
            moduleName = "密码测评管理",
            operationType = "修改",
            desc = "'修改密码测评：' + #data.systemName"
    )
    public Result<?> update(@RequestBody CryptoEvaluation cryptoEvaluation) {
        cryptoEvaluationService.updateById(cryptoEvaluation);
        return Result.success("修改成功");
    }

    // 查询单个密码测评数据
    @GetMapping("/{id}")
    @OperationLog(
            moduleName = "密码测评管理",
            operationType = "查询",
            desc = "'查询密码测评：' + #data.systemName"
    )
    public Result<CryptoEvaluation> getCryptoEvaluationById(@PathVariable("id") Integer id) {
        CryptoEvaluation cryptoEvaluation = cryptoEvaluationService.getById(id);
        return Result.success(cryptoEvaluation);
    }

    // 查询所有密码测评数据
    @GetMapping("/all")
    public Result<List<CryptoEvaluation>> getAllCryptoEvaluation() {
        List<CryptoEvaluation> list = cryptoEvaluationService.list();
        return Result.success(list);
    }

    @PostMapping("/importPdf")
    @OperationLog(
            moduleName = "密码测评管理",
            operationType = "导入",
            desc = "导入PDF密码测评报告"
    )
    @ResponseBody
    public Result<?> importPdf(@RequestPart("file") MultipartFile file) throws IOException {
        // 1. 读取PDF内容
        String pdfContent = readPdfContent(file.getInputStream());

        // 2. 解析PDF内容并提取关键信息
        CryptoEvaluation entity = parsePdfContent(pdfContent);

        // 3. 返回解析结果
        return Result.success(Arrays.asList(entity));
    }

    private String readPdfContent(InputStream inputStream) throws IOException {
        PDDocument document = load(inputStream);
        PDFTextStripper pdfStripper = new PDFTextStripper();
        String text = pdfStripper.getText(document);
        document.close();
        return text;
    }

    private CryptoEvaluation parsePdfContent(String pdfContent) {
        CryptoEvaluation entity = new CryptoEvaluation();

        // 提取基础信息
        extractField(pdfContent, "所属单位[:：]\\s*(.*?)\\s*\\n", entity::setSystemOwnerOrg);
        extractField(pdfContent, "系统名称[:：]\\s*(.*?)\\s*\\n", entity::setSystemName);
        extractField(pdfContent, "业务类型[:：]\\s*(.*?)\\s*\\n", entity::setBusinessType);
        extractField(pdfContent, "备案号[:：]\\s*(.*?)\\s*\\n", entity::setFilingNumber);
        extractField(pdfContent, "定级级别[:：]\\s*(.*?)\\s*\\n", entity::setClassificationLevel);
        extractField(pdfContent, "测评时间[:：]\\s*(\\d{4}-\\d{2}-\\d{2})\\s*\\n", val -> {
            try {
                entity.setEvaluationTime(LocalDate.parse(val, DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            } catch (DateTimeParseException e) {
                // 日期格式错误时忽略
            }
        });
        extractField(pdfContent, "测评单位[:：]\\s*(.*?)\\s*\\n", entity::setEvaluationOrganization);
        extractField(pdfContent, "测评状态[:：]\\s*(.*?)\\s*\\n", entity::setEvaluationStatus);
        extractField(pdfContent, "系统状态[:：]\\s*(.*?)\\s*\\n", entity::setSystemStatus);

        // 解析测评详情
        parseEvaluationDetails(pdfContent, entity);

        return entity;
    }

    private void extractField(String content, String regexPattern, Consumer<String> setter) {
        Pattern pattern = Pattern.compile(regexPattern);
        Matcher matcher = pattern.matcher(content);
        if (matcher.find()) {
            setter.accept(matcher.group(1).trim());
        }
    }

    private void parseEvaluationDetails(String content, CryptoEvaluation entity) {
        extractField(content, "分类[:：]\\s*(.*?)\\s*\\n", entity::setCategory);
        extractField(content, "控制点[:：]\\s*(.*?)\\s*\\n", entity::setControlPoint);
        extractField(content, "测评项[:：]\\s*(.*?)\\s*\\n", entity::setEvaluationItem);
        extractField(content, "结果记录[:：]\\s*(.*?)\\s*\\n", entity::setResultRecord);
        extractField(content, "风险等级[:：]\\s*(.*?)\\s*\\n", entity::setRiskLevel);
        extractField(content, "整改建议[:：]\\s*(.*?)\\s*\\n", entity::setRectificationSuggestion);

        // 提取分数
        extractField(content, "密码评分[:：]\\s*([\\d.]+)\\s*\\n", val -> {
            try {
                entity.setCryptoScore(new BigDecimal(val));
            } catch (NumberFormatException e) {
                // 数字格式错误时忽略
            }
        });
        extractField(content, "整改后预估分数[:：]\\s*([\\d.]+)\\s*\\n", val -> {
            try {
                entity.setEstimatedScoreAfterFix(new BigDecimal(val));
            } catch (NumberFormatException e) {
                // 数字格式错误时忽略
            }
        });
    }

    @PostMapping("/import")
    @OperationLog(
            moduleName = "密码测评管理",
            operationType = "导入",
            desc = "导入密码测评数据"
    )
    @ResponseBody
    public Result<?> importExcel(@RequestPart("file") MultipartFile file) throws IOException {
        // 1. 读取Excel数据到VO列表
        List<CryptoEvaluationExcelVO> voList = EasyExcel.read(file.getInputStream())
                .head(CryptoEvaluationExcelVO.class)
                .sheet()
                .doReadSync();

        // 2. VO转Entity并处理字段转换
        List<CryptoEvaluation> entityList = voList.stream().map(vo -> {
            CryptoEvaluation entity = new CryptoEvaluation();

            // 基础字段映射
            entity.setSystemOwnerOrg(vo.getSystemOwnerOrg());
            entity.setSystemName(vo.getSystemName());
            entity.setBusinessType(vo.getBusinessType());
            entity.setFilingNumber(vo.getFilingNumber());
            entity.setClassificationLevel(vo.getClassificationLevel());

            // 日期转换（String -> LocalDate）
            if (StringUtils.hasLength(vo.getEvaluationTime())) {
                try {
                    entity.setEvaluationTime(LocalDate.parse(vo.getEvaluationTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                } catch (DateTimeParseException e) {
                    // 日期格式错误时忽略
                }
            }

            entity.setEvaluationOrganization(vo.getEvaluationOrganization());
            entity.setEvaluationStatus(vo.getEvaluationStatus());
            entity.setSystemStatus(vo.getSystemStatus());
            entity.setCategory(vo.getCategory());
            entity.setControlPoint(vo.getControlPoint());
            entity.setEvaluationItem(vo.getEvaluationItem());
            entity.setResultRecord(vo.getResultRecord());
            entity.setRiskLevel(vo.getRiskLevel());
            entity.setRectificationSuggestion(vo.getRectificationSuggestion());

            // 数字转换（String -> BigDecimal）
            if (StringUtils.hasLength(vo.getCryptoScore())) {
                try {
                    entity.setCryptoScore(new BigDecimal(vo.getCryptoScore()));
                } catch (NumberFormatException e) {
                    // 数字格式错误时忽略
                }
            }
            if (StringUtils.hasLength(vo.getEstimatedScoreAfterFix())) {
                try {
                    entity.setEstimatedScoreAfterFix(new BigDecimal(vo.getEstimatedScoreAfterFix()));
                } catch (NumberFormatException e) {
                    // 数字格式错误时忽略
                }
            }

            return entity;
        }).collect(Collectors.toList());

        // 3. 批量保存数据
        // cryptoEvaluationService.saveBatch(entityList);

        return Result.success(entityList);
    }

    // 批量导出
    @GetMapping("/export")
    @OperationLog(
            moduleName = "密码测评管理",
            operationType = "导出",
            desc = "导出密码测评数据"
    )
    public void exportExcel(HttpServletResponse response,
                            @RequestParam(value = "systemOwnerOrg", required = false) String systemOwnerOrg,
                            @RequestParam(value = "systemName", required = false) String systemName,
                            @RequestParam(value = "businessType", required = false) String businessType,
                            @RequestParam(value = "filingNumber", required = false) String filingNumber,
                            @RequestParam(value = "classificationLevel", required = false) String classificationLevel,
                            @RequestParam(value = "evaluationOrganization", required = false) String evaluationOrganization,
                            @RequestParam(value = "evaluationStatus", required = false) String evaluationStatus,
                            @RequestParam(value = "systemStatus", required = false) String systemStatus,
                            @RequestParam(value = "category", required = false) String category,
                            @RequestParam(value = "controlPoint", required = false) String controlPoint,
                            @RequestParam(value = "evaluationItem", required = false) String evaluationItem,
                            @RequestParam(value = "resultRecord", required = false) String resultRecord,
                            @RequestParam(value = "riskLevel", required = false) String riskLevel
    ) throws IOException {
        // 设置响应格式和文件名
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("密码测评数据", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 构建查询条件
        LambdaQueryWrapper<CryptoEvaluation> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasLength(systemOwnerOrg)) {
            wrapper.eq(CryptoEvaluation::getSystemOwnerOrg, systemOwnerOrg);
        }
        if (StringUtils.hasLength(systemName)) {
            wrapper.eq(CryptoEvaluation::getSystemName, systemName);
        }
        if (StringUtils.hasLength(businessType)) {
            wrapper.eq(CryptoEvaluation::getBusinessType, businessType);
        }
        if (StringUtils.hasLength(filingNumber)) {
            wrapper.eq(CryptoEvaluation::getFilingNumber, filingNumber);
        }
        if (StringUtils.hasLength(classificationLevel)) {
            wrapper.eq(CryptoEvaluation::getClassificationLevel, classificationLevel);
        }
        if (StringUtils.hasLength(evaluationOrganization)) {
            wrapper.eq(CryptoEvaluation::getEvaluationOrganization, evaluationOrganization);
        }
        if (StringUtils.hasLength(evaluationStatus)) {
            wrapper.eq(CryptoEvaluation::getEvaluationStatus, evaluationStatus);
        }
        if (StringUtils.hasLength(systemStatus)) {
            wrapper.eq(CryptoEvaluation::getSystemStatus, systemStatus);
        }
        if (StringUtils.hasLength(category)) {
            wrapper.eq(CryptoEvaluation::getCategory, category);
        }
        if (StringUtils.hasLength(controlPoint)) {
            wrapper.eq(CryptoEvaluation::getControlPoint, controlPoint);
        }
        if (StringUtils.hasLength(evaluationItem)) {
            wrapper.eq(CryptoEvaluation::getEvaluationItem, evaluationItem);
        }
        if (StringUtils.hasLength(resultRecord)) {
            wrapper.eq(CryptoEvaluation::getResultRecord, resultRecord);
        }
        if (StringUtils.hasLength(riskLevel)) {
            wrapper.eq(CryptoEvaluation::getRiskLevel, riskLevel);
        }

        // 获取数据并转换为VO
        List<CryptoEvaluationExcelVO> voList = cryptoEvaluationService.list(wrapper)
                .stream()
                .map(CryptoEvaluationExcelVO::new)
                .collect(Collectors.toList());

        // 导出数据
        EasyExcel.write(response.getOutputStream(), CryptoEvaluationExcelVO.class)
                .sheet("密码测评数据")
                .doWrite(voList);
    }

    @GetMapping("/statistics")
    public Result<Map<String, Object>> getStatistics(
            @RequestParam(value = "systemOwnerOrg", required = false) String systemOwnerOrg,
            @RequestParam(value = "systemName", required = false) String systemName,
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime
    ) {
        Map<String, Object> statistics = cryptoEvaluationService.getStatistics(
                systemOwnerOrg, systemName, startTime, endTime
        );
        return Result.success(statistics);
    }
}
