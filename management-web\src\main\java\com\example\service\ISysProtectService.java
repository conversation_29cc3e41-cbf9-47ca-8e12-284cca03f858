package com.example.service;

import com.example.common.core.domain.AjaxResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public interface ISysProtectService {
    
    /**
     * 导入等保数据
     *
     * @param file 导入文件
     * @return 导入结果
     */
    AjaxResult importData(MultipartFile file) throws Exception;

    /**
     * 导出等保数据
     *
     * @param response HTTP响应对象
     */
    void export(HttpServletResponse response) throws IOException;

    /**
     * 下载导入模板
     *
     * @param response HTTP响应对象
     */
    void downloadTemplate(HttpServletResponse response) throws IOException;
} 