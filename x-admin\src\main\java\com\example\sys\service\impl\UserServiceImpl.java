package com.example.sys.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.example.commom.vo.Result;
import com.example.sys.entity.User;
import com.example.sys.mapper.UserMapper;
import com.example.sys.service.IUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate; // 明确泛型类型
    @Autowired
    private PasswordEncoder passwordEncoder;

    // 统一 Redis 键名前缀
    private static final String USER_TOKEN_PREFIX = "user:";
    private static final String USER_ID_BY_TOKEN_PREFIX = "userIdByToken:";

    @Override
    public Map<String, Object> login(User user) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getUsername, user.getUsername());
        User loginUser = this.baseMapper.selectOne(wrapper);

        if (loginUser != null && passwordEncoder.matches(user.getPassword(), loginUser.getPassword())) {
            String token = UUID.randomUUID().toString();
            String userKey = USER_TOKEN_PREFIX + token;
            String userIdKey = USER_ID_BY_TOKEN_PREFIX + token;

            // 存储用户对象到 Redis（包含 realname）
            redisTemplate.opsForValue().set(userKey, loginUser, 30, TimeUnit.MINUTES);
            // 存储用户ID到 Redis（用于缓存失效时查询）
            redisTemplate.opsForValue().set(userIdKey, loginUser.getId().toString(), 30, TimeUnit.MINUTES);

            Map<String, Object> data = new HashMap<>();
            data.put("token", token);
            return data;
        }
        return null;
    }

    @Override
    public Map<String, Object> getUserInfo(String token) {
        // 使用统一的键名前缀
        String userKey = USER_TOKEN_PREFIX + token;
        Object userObj = redisTemplate.opsForValue().get(userKey);

        if (userObj != null) {
            User user = (User) userObj; // 直接转换为 User 对象
            return convertUserToMap(user);
        } else {
            // 从 Redis 获取用户ID（缓存失效时）
            String userIdKey = USER_ID_BY_TOKEN_PREFIX + token;
            String userIdStr = (String) redisTemplate.opsForValue().get(userIdKey);
            if (userIdStr != null) {
                Long userId = Long.parseLong(userIdStr);
                User userFromDB = this.baseMapper.selectById(userId);
                if (userFromDB != null) {
                    // 更新 Redis 缓存
                    redisTemplate.opsForValue().set(userKey, userFromDB, 30, TimeUnit.MINUTES);
                    redisTemplate.opsForValue().set(userIdKey, userIdStr, 30, TimeUnit.MINUTES);
                    return convertUserToMap(userFromDB);
                }
            }
        }
        return null;
    }

    @Override
    public void logout(String token) {
        String userKey = USER_TOKEN_PREFIX + token;
        String userIdKey = USER_ID_BY_TOKEN_PREFIX + token;
        // 使用集合批量删除
        redisTemplate.delete(Arrays.asList(userKey, userIdKey));
    }

    // 辅助方法：将 User 对象转换为 Map
    private Map<String, Object> convertUserToMap(User user) {
        Map<String, Object> data = new HashMap<>();
        data.put("name", user.getUsername());
        data.put("realname", user.getRealname()); // 确保包含 realname
        data.put("avatar", user.getAvatar());
        // 角色处理（根据实际需求）
        List<String> roles = new ArrayList<>();
        if (user.getRole() != null) {
            roles.add(user.getRole());
        }
        data.put("roles", roles);
        return data;
    }

    // 根据组织获取用户邮箱列表
    @Override
    public List<String> getEmailsByOrganization(String organization) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getOrganization, organization);
        return this.baseMapper.selectList(wrapper).stream()
                .map(User::getEmail)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getUserNamesByOrganization(String organization) {
        // 查询条件：组织匹配且 realName 不为空
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getOrganization, organization)
// 检查方法名是否正确，可能是 getRealname 而非 getRealName
.isNotNull(User::getRealname); // 确保真实姓名存在
        
        // 提取 realName 字段并返回列表
        return this.list(wrapper).stream()
// 根据提示，User 类可能没有 getRealName 方法，推测正确方法名为 getRealname
.map(User::getRealname)
                .collect(Collectors.toList());
    }
}