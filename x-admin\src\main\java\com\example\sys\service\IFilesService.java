package com.example.sys.service;

import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import org.springframework.http.ResponseEntity;
import org.springframework.core.io.Resource;

public interface IFilesService {
    String uploadFile(MultipartFile file);
    String deleteFile(Integer fileId);
    ResponseEntity<Resource> downloadFile(Integer fileId);
    String getFileNameByFileId(Integer fileId);
}    