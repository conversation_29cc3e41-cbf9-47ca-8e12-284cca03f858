import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  })
}

export default{
  getUserList(listQuery){
    return request({
      url:'/user/list',
      method:'get',
      params:{
        pageNo:listQuery.pageNo,
        pageSize:listQuery.pageSize,
        username:listQuery.username,
        email:listQuery.email,
        phone:listQuery.phone,
        status:listQuery.status,
        orgnazation:listQuery.orgnazation,
        role:listQuery.role,
        reviewer:listQuery.reviewer
      }
    })
  },

  addUser(user){
    return request({
      url:'/user/add',
      method:'post',
      data:user //传到后端的是JSON数据
    })
  },

  updataUser(user){
    return request({
      url:'/user/updata',
      method:'put',
      data:user //传到后端的是JSON数据
    })
  },

  saveUser(user){
    if(user.id == null && user.id == undefined){
      return this.addUser(user)
    }else{
      return this.updataUser(user)
    }

  },

  getUserByID(id){
    return request({
      url:`/user/${id}`,
      method:'get',
    })
  },

  deleteUserByID(id){
    return request({
      url:`/user/${id}`,
      method:'delete',
    })
  },

}
