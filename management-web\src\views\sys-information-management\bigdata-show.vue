<template>
    <div class="app-container">
      <!-- 顶部导航栏 -->
      <header>
        <h1>系统管理平台</h1>
      </header>
  
      <!-- 系统总览 -->
      <section class="overview">
        <div class="card">系统总数: {{ totalSystems }}</div>
        <div class="card">漏洞总数: {{ totalVulnerabilities }}</div>
        <div class="card">高危漏洞: {{ highRiskVulnerabilities }}</div>
        <div class="card">中危漏洞: {{ mediumRiskVulnerabilities }}</div>
        <div class="card">低危漏洞: {{ lowRiskVulnerabilities }}</div>
      </section>
  
      <!-- 系统硬件参数监控 -->
      <section class="hardware-monitoring">
        <h2>硬件参数监控</h2>
        <div class="monitor">
          <h3>CPU利用率</h3>
          <progress-bar :value="cpuUsage"></progress-bar>
        </div>
        <div class="monitor">
          <h3>内存利用率</h3>
          <progress-bar :value="memoryUsage"></progress-bar>
        </div>
        <div class="monitor">
          <h3>磁盘利用率</h3>
          <progress-bar :value="diskUsage"></progress-bar>
        </div>
      </section>
  
      <!-- 单位安全状态地图 -->
      <section class="map">
        <h2>单位安全状态地图</h2>
        <l-map :zoom="zoom" :center="mapCenter">
          <l-tile-layer :url="tileLayerUrl"></l-tile-layer>
          <l-marker v-for="unit in units" :key="unit.id" :lat-lng="[unit.lat, unit.lng]">
            <l-popup>
              <strong>{{ unit.name }}</strong><br>
              漏洞数量: {{ unit.vulnerabilities }}<br>
              <button @click="viewUnitDetails(unit)">查看详情</button>
            </l-popup>
          </l-marker>
        </l-map>
      </section>
    </div>
  </template>
  
  <script>
  import { LMap, LTileLayer, LMarker, LPopup } from 'vue2-leaflet';
  import 'leaflet/dist/leaflet.css';
  
  export default {
    name: 'SystemDashboard',
    components: {
      LMap,
      LTileLayer,
      LMarker,
      LPopup,
      'progress-bar': {
        props: ['value'],
        template: `<div class="progress-container"><progress :value="value" max="100">{{ value }}%</progress></div>`,
        style: `.progress-container { width: 100%; }`
      }
    },
    data() {
      return {
        totalSystems: 100,
        totalVulnerabilities: 200,
        highRiskVulnerabilities: 50,
        mediumRiskVulnerabilities: 100,
        lowRiskVulnerabilities: 50,
        cpuUsage: 60,
        memoryUsage: 70,
        diskUsage: 80,
        mapCenter: [35.8617, 104.1954], // 中国地图中心
        zoom: 4,
        tileLayerUrl: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
        units: [
          { id: 1, name: '单位1', lat: 39.9042, lng: 116.4074, vulnerabilities: 10 },
          { id: 2, name: '单位2', lat: 31.2304, lng: 121.4737, vulnerabilities: 20 },
          { id: 3, name: '单位3', lat: 30.5728, lng: 104.0668, vulnerabilities: 30 }
        ]
      };
    },
    methods: {
      viewUnitDetails(unit) {
        alert(`单位名称: ${unit.name}\n漏洞数量: ${unit.vulnerabilities}`);
      }
    }
  };
  </script>
  
  <style>
  #app {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
  }
  
  header {
    background-color: #333;
    color: white;
    padding: 10px;
    text-align: center;
  }
  
  .overview {
    display: flex;
    justify-content: space-around;
    padding: 20px;
  }
  
  .card {
    background-color: #f2f2f2;
    border-radius: 5px;
    padding: 10px;
    text-align: center;
    width: 150px;
  }
  
  .hardware-monitoring {
    padding: 20px;
  }
  
  .monitor {
    margin-bottom: 15px;
  }
  
  .map {
    padding: 20px;
  }
  
  l-map {
    height: 400px;
    width: 100%;
  }
  </style>
  