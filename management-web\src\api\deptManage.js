import request from '@/utils/request'

export default{
    getDeptList(listQuery){
        return request({
          url:'/dept/list',
          method:'get',
          params:{
            deptId:listQuery.deptId,
            pid:listQuery.pid,
            name:listQuery.name,
            phone:listQuery.phone,
            address:listQuery.address,
            status:listQuery.status
          }
        })
      },
    addDept(dept){
      return request({
        url:'/dept/add',
        method:'post',
        data:dept
      })
    },
    updataDept(dept){
      return request({
        url:'/dept/updata',
        method:'put',
        data:dept
      })
    },
    deleteDeptbyDeptID(deptId){
      return request({
        url:`/dept/${deptId}`,
        method:'delete',
      })
    },
    getDeptByID(id){
      return request({
        url:'/dept/getDeptByID',
      })
    },

    searchByDeptName(name){
      return request({
        url:'/dept/search',
        method:'get',
        params:{
          name:name
        }
      })
    }

    }