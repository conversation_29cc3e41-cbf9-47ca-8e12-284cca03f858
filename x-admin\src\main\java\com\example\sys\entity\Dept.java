package com.example.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 部门
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-26
 */
@TableName("x_dept")
public class Dept implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "dept_id", type = IdType.AUTO)
    private Integer deptId;

    /**
     * 上级部门
     */
    private Integer pid;

    /**
     * 名称
     */
    private String name;

    /**
     * 状态
     */
    private String status;

    /**
     * 联系电话
     */
    private Integer phone;

    /**
     * 地址
     */
    private String address;


    /**
     * 删除标签
     */
    private Integer deleted;

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }
    public Integer getPid() {
        return pid;
    }

    public void setPid(Integer pid) {
        this.pid = pid;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public Integer getPhone() {
        return phone;
    }

    public void setPhone(Integer phone) {
        this.phone = phone;
    }
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
    public Integer getDelete() {
        return deleted;
    }

    public void setDelete(Integer delete) {
        this.deleted = delete;
    }

    @Override
    public String toString() {
        return "Dept{" +
            "deptId=" + deptId +
            ", pid=" + pid +
            ", name=" + name +
            ", status=" + status +
            ", phone=" + phone +
            ", address=" + address +
            ", deleted=" + deleted +
        "}";
    }
}
