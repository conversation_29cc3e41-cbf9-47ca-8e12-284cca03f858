package com.example.sys.service.impl;

import com.example.sys.entity.SysProtect;
import com.example.sys.entity.Tasks;
import com.example.sys.service.ISysProtectService;
import com.example.sys.service.ITasksService;
import com.example.sys.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;

@Service
public class TaskAutoGenerateService {

    @Autowired
    private ISysProtectService sysProtectService;

    @Autowired
    private ITasksService tasksService;

    @Autowired
    private IUserService userService;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy/MM/dd");

    // 原有定时任务（每天凌晨 0 点执行）
    @Scheduled(cron = "0 0 0 * * ?")
    public void autoGenerateTasks() {
        executeTaskGeneration(); // 抽取公共方法，供定时任务和启动时调用
    }

    // 新增：启动时立即执行一次
    @PostConstruct
    public void initGenerateTasksOnStartup() {
        executeTaskGeneration();
    }

    // 公共任务生成逻辑（避免代码重复）
    private void executeTaskGeneration() {
        List<SysProtect> sysProtectList = sysProtectService.list();

        for (SysProtect sysProtect : sysProtectList) {
            String plannedEvaluationTimeStr = sysProtect.getPlannedEvaluationTime();
            if (!StringUtils.hasText(plannedEvaluationTimeStr)) {
                continue;
            }

            LocalDate plannedEvaluationTime;
            try {
                plannedEvaluationTime = LocalDate.parse(plannedEvaluationTimeStr, DATE_FORMATTER);
            } catch (Exception e) {
                continue; // 日志记录异常（实际开发中需添加日志）
            }

            LocalDate currentDate = LocalDate.now();
            long daysUntilPlanned = ChronoUnit.DAYS.between(currentDate, plannedEvaluationTime);

            if (daysUntilPlanned == 2) {
                Tasks task = buildTaskFromSysProtect(sysProtect);
                tasksService.save(task);
            }
        }
    }

    // 原有映射方法（保持不变）
    private Tasks buildTaskFromSysProtect(SysProtect sysProtect) {
        Tasks task = new Tasks();
//        task.setApplicationId(sysProtect.getApplicationId());
//        task.setSystemName(sysProtect.getSystemName());
//        task.setSystemShortName(sysProtect.getSystemShortName());
//        task.setSystemOwnerOrg(sysProtect.getSystemOwnerOrg());
//        task.setPlannedEvaluationTime(LocalDate.parse(sysProtect.getPlannedEvaluationTime(), DATE_FORMATTER));
        task.setTaskStatus("未开始");
        task.setCreateTime(LocalDateTime.now());
//        task.setTaskDescription(sysProtect.getEvaluationTime());
        // task.setTaskDescription("自动生成：计划测评时间前 2 天提醒");

        // 设置负责人
        String organization = sysProtect.getEvaluationOrganization();
        List<String> realname = userService.getUserNamesByOrganization(organization);
        if (!realname.isEmpty()) {
            task.setResponsiblePerson(realname.get(0));
        }
        if(sysProtect.getClassificationLevel().equals("二级")){
//            task.setImportance(2);
        }
        else if(sysProtect.getClassificationLevel().equals("三级")){
//            task.setImportance(1);
        } else{
//            task.setImportance(3);
        }
        return task;
    }
}
