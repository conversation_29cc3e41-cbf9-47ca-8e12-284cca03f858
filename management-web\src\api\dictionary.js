import request from '@/utils/request'

export default {
  // 数据字典接口
  getAllDictionary() {
    return request({
      url: '/dictionary/all',
      method: 'get'
    })
  },
  addDictionary(data) {
    return request({
      url: '/dictionary/add',
      method: 'post',
      data
    })
  },
  updateDictionary(data) {
    return request({
      url: '/dictionary/update',
      method: 'put',
      data
    })
  },
  deleteDictionary(id) {
    return request({
      url: `/dictionary/${id}`,
      method: 'delete'
    })
  }
}
