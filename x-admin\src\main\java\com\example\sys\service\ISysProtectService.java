package com.example.sys.service;

import com.example.sys.entity.SysProtect;

import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 系统保护服务接口
 */
public interface ISysProtectService extends IService<SysProtect> {

    /**
     * 自动更新所有系统的evaluationStatus状态
     */
    void autoUpdateEvaluationStatus();
    // 新增统计方法
    Map<String, Object> getStatistics(String systemOwnerOrg, String systemName, String startTime, String endTime);
}
