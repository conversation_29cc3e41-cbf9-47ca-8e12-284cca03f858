package com.example.sys.service;

import com.example.sys.service.IMailService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import java.util.List;
import java.util.Arrays;

@SpringBootTest // 加载完整 Spring 上下文（使用真实邮件发送器）
@TestPropertySource(properties = {
        // 163 邮箱 SMTP 配置（必须替换为你的真实信息）
        "spring.mail.username=<EMAIL>",  // 发件人邮箱（你的 163 邮箱）
        "spring.mail.password=JRWY9rS3K5PuuiTR",        // 替换为你生成的授权码（非登录密码）
        "spring.mail.host=smtp.163.com",             // 163 邮箱 SMTP 服务器地址
        "spring.mail.port=465",                      // SSL 端口（163 固定为 465）
        "spring.mail.properties.mail.smtp.ssl.enable=true" // 启用 SSL 加密
})
public class MailServiceTest {

    @Autowired
    private IMailService mailService; // 注入真实的邮件服务实现

    // 测试收件人（你希望发送到的 QQ 邮箱）
    private static final String TEST_RECIPIENT = "<EMAIL>";

    /**
     * 测试单封真实邮件发送（到 QQ 邮箱）
     */
    @Test
    public void testRealSendToQQMail() {
        // 邮件内容
        String subject = "测试邮件（来自 163 到 QQ）";
        String content = "这是通过集成测试发送的真实邮件，收件人：" + TEST_RECIPIENT;

        // 调用真实发送方法
        mailService.sendMail(TEST_RECIPIENT, subject, content);

        System.out.println("邮件已发送，请检查 QQ 邮箱（包括垃圾邮件箱）！");
    }

   /**
 * 测试批量真实邮件发送（包含多个 QQ 邮箱和企业邮箱）
 */
@Test
public void testRealSendBatchMail() {
    // 批量收件人列表（包含你的所有目标邮箱）
    List<String> toList = Arrays.asList(
        "<EMAIL>",    // 你的第一个 QQ 邮箱
        "<EMAIL>",    // 新增的第二个 QQ 邮箱
        "<EMAIL>"// 新增的企业邮箱
        // "<EMAIL>"   // 其他测试邮箱（可选）
    );

    // 邮件内容（明确说明收件人列表）
    String subject = "批量测试邮件（163→QQ+企业邮箱）";
    String content = "这是批量集成测试发送的真实邮件" ;

    // 调用批量发送方法
    mailService.sendBatchMail(toList, subject, content);

    System.out.println("批量邮件已发送，请检查以下邮箱（包括垃圾邮件箱）：" + toList);
}
}
