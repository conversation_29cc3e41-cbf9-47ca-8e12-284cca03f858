# Java自动安装脚本
Write-Host "=== Java环境自动配置脚本 ===" -ForegroundColor Green

# 检查是否以管理员权限运行
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "警告: 建议以管理员权限运行此脚本" -ForegroundColor Yellow
}

# 1. 检查现有Java安装
Write-Host "1. 检查现有Java安装..." -ForegroundColor Cyan
try {
    $javaVersion = java -version 2>&1 | Out-String
    Write-Host "发现已安装的Java:" -ForegroundColor Green
    Write-Host $javaVersion
    
    # 检查版本是否符合要求
    if ($javaVersion -match "1\.8|11\.|17\.") {
        Write-Host "Java版本符合要求，无需重新安装" -ForegroundColor Green
        exit 0
    }
} catch {
    Write-Host "未找到Java安装" -ForegroundColor Yellow
}

# 2. 尝试使用winget安装
Write-Host "2. 尝试使用winget安装Java 11..." -ForegroundColor Cyan
try {
    $wingetResult = winget install Microsoft.OpenJDK.11 --accept-package-agreements --accept-source-agreements
    if ($LASTEXITCODE -eq 0) {
        Write-Host "winget安装成功!" -ForegroundColor Green
        $installSuccess = $true
    }
} catch {
    Write-Host "winget安装失败，尝试其他方法..." -ForegroundColor Yellow
}

# 3. 如果winget失败，尝试下载安装包
if (-not $installSuccess) {
    Write-Host "3. 下载Java 11安装包..." -ForegroundColor Cyan
    $javaUrl = "https://github.com/adoptium/temurin11-binaries/releases/download/jdk-11.0.21%2B9/OpenJDK11U-jdk_x64_windows_hotspot_11.0.21_9.msi"
    $installerPath = "$env:TEMP\openjdk11.msi"
    
    try {
        Write-Host "正在下载Java安装包..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $javaUrl -OutFile $installerPath -UseBasicParsing
        
        Write-Host "正在安装Java..." -ForegroundColor Yellow
        Start-Process msiexec.exe -ArgumentList "/i", $installerPath, "/quiet", "ADDLOCAL=FeatureMain,FeatureEnvironment,FeatureJarFileRunWith,FeatureJavaHome" -Wait
        
        Write-Host "Java安装完成!" -ForegroundColor Green
        $installSuccess = $true
    } catch {
        Write-Host "下载安装失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 4. 验证安装
Write-Host "4. 验证Java安装..." -ForegroundColor Cyan
Start-Sleep -Seconds 3

# 刷新环境变量
$env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")

try {
    $javaVersion = java -version 2>&1 | Out-String
    Write-Host "Java安装验证成功:" -ForegroundColor Green
    Write-Host $javaVersion
    
    # 显示JAVA_HOME
    $javaHome = [Environment]::GetEnvironmentVariable("JAVA_HOME", "Machine")
    if ($javaHome) {
        Write-Host "JAVA_HOME: $javaHome" -ForegroundColor Green
    }
    
    Write-Host "Java环境配置完成!" -ForegroundColor Green
    Write-Host "现在可以启动Spring Boot项目了" -ForegroundColor Green
    
} catch {
    Write-Host "Java验证失败，可能需要重启命令行或重新登录" -ForegroundColor Red
    Write-Host "请手动验证: java -version" -ForegroundColor Yellow
}

Write-Host "=== 脚本执行完成 ===" -ForegroundColor Green
