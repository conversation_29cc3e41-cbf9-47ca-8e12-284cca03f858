// chartOptions.js
export const gaugeOption = (value, color) => ({
  series: [{
    type: 'gauge',
    radius: '100%',
    startAngle: 200,
    endAngle: -20,
    min: 0,
    max: 100,
    progress: {
      show: true,
      width: 12,
      roundCap: true,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          { offset: 0, color },
          { offset: 1, color: color + '80' }
        ])
      }
    },
    pointer: { show: false },
    axisLine: { show: false },
    axisTick: { show: false },
    splitLine: { show: false },
    axisLabel: { show: false },
    detail: { show: false }
  }]
})

export const vulnOption = {
  tooltip: { trigger: 'item' },
  legend: {
    orient: 'vertical',
    right: 20,
    top: 'center',
    textStyle: { color: '#fff' }
  },
  series: [{
    type: 'pie',
    radius: ['40%', '70%'],
    roseType: 'radius',
    itemStyle: { borderRadius: 8 },
    label: { color: '#fff' },
    data: [
      { value: 335, name: '高危漏洞', itemStyle: { color: '#ff6b6b' } },
      { value: 310, name: '中危漏洞', itemStyle: { color: '#faa307' } },
      { value: 274, name: '低危漏洞', itemStyle: { color: '#20c997' } },
      { value: 235, name: '信息漏洞', itemStyle: { color: '#4cc9f0' } }
    ]
  }]
}

export const unitOption = {
  tooltip: { trigger: 'axis' },
  grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
  xAxis: {
    type: 'value',
    axisLabel: { color: '#fff' },
    splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
  },
  yAxis: {
    type: 'category',
    data: ['单位A', '单位B', '单位C', '单位D', '单位E'],
    axisLabel: { color: '#fff' }
  },
  series: [{
    type: 'bar',
    data: [120, 200, 150, 80, 70],
    itemStyle: {
      color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
        { offset: 0, color: '#4f46e5' },
        { offset: 1, color: '#818cf8' }
      ]),
      borderRadius: [0, 8, 8, 0]
    },
    barWidth: 16
  }]
}

export const taskOption = {
  tooltip: { trigger: 'item' },
  series: [{
    type: 'pie',
    radius: ['65%', '85%'],
    color: ['#4cc9f0', '#7209b7', '#3a0ca3'],
    label: { color: '#fff' },
    data: [
      { value: 1048, name: '进行中' },
      { value: 735, name: '已完成' },
      { value: 580, name: '未开始' }
    ]
  }]
}

