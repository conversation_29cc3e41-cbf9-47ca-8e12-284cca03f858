package com.example.sys.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.example.sys.entity.CryptoEvaluation;
import lombok.Data;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Data
public class CryptoEvaluationExcelVO {
    @ExcelProperty("所属单位")
    private String systemOwnerOrg;

    @ExcelProperty("系统名称")
    private String systemName;

    @ExcelProperty("业务类型")
    private String businessType;

    @ExcelProperty("备案号")
    private String filingNumber;

    @ExcelProperty("定级级别")
    private String classificationLevel;

    @ExcelProperty("测评时间")
    private String evaluationTime;

    @ExcelProperty("测评单位")
    private String evaluationOrganization;

    @ExcelProperty("测评状态")
    private String evaluationStatus;

    @ExcelProperty("系统状态")
    private String systemStatus;

    @ExcelProperty("分类")
    private String category;

    @ExcelProperty("控制点")
    private String controlPoint;

    @ExcelProperty("测评项")
    private String evaluationItem;

    @ExcelProperty("结果记录")
    private String resultRecord;

    @ExcelProperty("风险等级")
    private String riskLevel;

    @ExcelProperty("整改建议")
    private String rectificationSuggestion;

    @ExcelProperty("密码评分")
    private String cryptoScore;

    @ExcelProperty("整改后预估分数")
    private String estimatedScoreAfterFix;

    public CryptoEvaluationExcelVO() {}

    public CryptoEvaluationExcelVO(CryptoEvaluation entity) {
        this.systemOwnerOrg = entity.getSystemOwnerOrg();
        this.systemName = entity.getSystemName();
        this.businessType = entity.getBusinessType();
        this.filingNumber = entity.getFilingNumber();
        this.classificationLevel = entity.getClassificationLevel();
        LocalDate evaluationTime = entity.getEvaluationTime();
        this.evaluationTime = evaluationTime != null ? evaluationTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : null;
        this.evaluationOrganization = entity.getEvaluationOrganization();
        this.evaluationStatus = entity.getEvaluationStatus();
        this.systemStatus = entity.getSystemStatus();
        this.category = entity.getCategory();
        this.controlPoint = entity.getControlPoint();
        this.evaluationItem = entity.getEvaluationItem();
        this.resultRecord = entity.getResultRecord();
        this.riskLevel = entity.getRiskLevel();
        this.rectificationSuggestion = entity.getRectificationSuggestion();
        this.cryptoScore = entity.getCryptoScore() != null ? entity.getCryptoScore().toString() : null;
        this.estimatedScoreAfterFix = entity.getEstimatedScoreAfterFix() != null ? entity.getEstimatedScoreAfterFix().toString() : null;
    }
}