<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 顶部菜单 -->
      <el-menu
          class="el-menu-horizontal"
          :default-active="activeMenu"
          mode="horizontal"
          @select="handleSelect"
      >
        <el-menu-item index="1">我的待办</el-menu-item>
        <el-menu-item index="2">已完成/已处理</el-menu-item>
      </el-menu>
      <div class="content">
        <div v-if="activeMenu === '1'">
          <!-- 搜索表单 -->
          <el-form :model="pendingQueryForm" :inline="true" class="search-form" size="small" label-width="85px">
            <el-row :gutter="24" align="middle">
              <el-col :span="6">
                <el-form-item label="单位名称">
                  <el-select v-model="pendingQueryForm.systemOwnerOrg" placeholder="请选择单位" clearable style="width: 100%">
                    <el-option label="中国核电" value="中国核电"></el-option>
                    <el-option label="秦山核电" value="秦山核电"></el-option>
                    <el-option label="江苏核电" value="江苏核电"></el-option>
                    <el-option label="福清核电" value="福清核电"></el-option>
                    <el-option label="海南核电" value="海南核电"></el-option>
                    <el-option label="三门核电" value="三门核电"></el-option>
                    <el-option label="霞浦核电" value="霞浦核电"></el-option>
                    <el-option label="漳州核电" value="漳州核电"></el-option>
                    <el-option label="中核武汉" value="中核武汉"></el-option>
                    <el-option label="中核能源" value="中核能源"></el-option>
                    <el-option label="运行研究院" value="运行研究院"></el-option>
                    <el-option label="研究运维" value="研究运维"></el-option>
                    <el-option label="辽宁核电" value="辽宁核电"></el-option>
                    <el-option label="中核山东" value="中核山东"></el-option>
                    <el-option label="中核储能" value="中核储能"></el-option>
                    <el-option label="中核苏能" value="中核苏能"></el-option>
                    <el-option label="中核海得" value="中核海得"></el-option>
                    <el-option label="河北核电" value="河北核电"></el-option>
                    <el-option label="庄河核电" value="庄河核电"></el-option>
                    <el-option label="中核光电" value="中核光电"></el-option>
                    <el-option label="桃花江核电" value="桃花江核电"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="系统名称">
                  <el-input v-model="pendingQueryForm.systemName" placeholder="请输入系统名称" clearable style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="任务类别">
                  <el-select v-model="pendingQueryForm.taskType" placeholder="请选择任务类别" clearable style="width: 100%">
                    <el-option label="等保测评" value="等保测评" />
                    <el-option label="密评" value="密评" />
                    <el-option label="安全性检测" value="安全性检测" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6" style="text-align: right; margin-top: 0;">
                <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
                <el-button icon="el-icon-refresh" @click="pendingHandleReset">重置</el-button>
              </el-col>
            </el-row>
          </el-form>

          <!-- 数据表格 -->
          <el-table
              v-loading="loading"
              :data="pendingFilteredData"
              border
              style="width: 100%; margin-top: 20px;"
              :header-cell-style="{ background: '#f5f7fa' }"
          >
            <el-table-column label="ID" width="80" align="center">
              <template #default="{ row, $index }">
                {{ $index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="systemOwnerOrg" label="单位名称" min-width="150"
                             align="center" />
            <el-table-column prop="systemName" label="系统名称"
                             min-width="180" align="center" />
            <el-table-column prop="taskType" label="任务类别" width="120"
                             align="center" />
            <el-table-column prop="taskStatus" label="状态" width="120"
                             align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.taskStatus)">
                  {{ row.taskStatus }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="截止时间" min-width="160"
                             align="center">
              <template #default="{ row }">
                {{ formatDateTime(row.endTime) }}
              </template>
            </el-table-column>
            <el-table-column label="创建时间" min-width="160"
                             align="center">
              <template #default="{ row }">
                {{ formatDateTime(row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180" align="center">
              <template #default="{ row }">
                <el-button size="mini" type="primary"
                           @click="handleUpload(row)">上传</el-button>
                <el-button size="mini" type="danger"
                           @click="handleOffline(row)">下线</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <el-pagination
              :current-page="queryParams.pageNo"
              :page-sizes="[5, 10, 20, 40]"
              :page-size="queryParams.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pendingFilteredData.length"
              @size-change="pendingHandleSizeChange"
              @current-change="pendingHandleCurrentChange"
          />
        </div>
        <div v-if="activeMenu === '2'">
          <!-- 搜索表单 -->
          <el-form :model="finishQueryForm" :inline="true" class="search-form" size="small" label-width="85px">
            <el-row :gutter="24" align="middle">
              <el-col :span="8">
                <el-form-item label="单位名称">
                  <el-select v-model="finishQueryForm.systemOwnerOrg" placeholder="请选择单位" clearable style="width: 100%">
                    <el-option label="中国核电" value="中国核电"></el-option>
                    <el-option label="秦山核电" value="秦山核电"></el-option>
                    <el-option label="江苏核电" value="江苏核电"></el-option>
                    <el-option label="福清核电" value="福清核电"></el-option>
                    <el-option label="海南核电" value="海南核电"></el-option>
                    <el-option label="三门核电" value="三门核电"></el-option>
                    <el-option label="霞浦核电" value="霞浦核电"></el-option>
                    <el-option label="漳州核电" value="漳州核电"></el-option>
                    <el-option label="中核武汉" value="中核武汉"></el-option>
                    <el-option label="中核能源" value="中核能源"></el-option>
                    <el-option label="运行研究院" value="运行研究院"></el-option>
                    <el-option label="研究运维" value="研究运维"></el-option>
                    <el-option label="辽宁核电" value="辽宁核电"></el-option>
                    <el-option label="中核山东" value="中核山东"></el-option>
                    <el-option label="中核储能" value="中核储能"></el-option>
                    <el-option label="中核苏能" value="中核苏能"></el-option>
                    <el-option label="中核海得" value="中核海得"></el-option>
                    <el-option label="河北核电" value="河北核电"></el-option>
                    <el-option label="庄河核电" value="庄河核电"></el-option>
                    <el-option label="中核光电" value="中核光电"></el-option>
                    <el-option label="桃花江核电" value="桃花江核电"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="完成日期">
                  <div class="date-picker-wrapper">
                    <el-date-picker
                        v-model="finishQueryForm.dueDate"
                        type="date"
                        placeholder="选择截止日期"
                        value-format="yyyy-MM-dd"
                        style="width: 100%"
                    />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8" style="text-align: right; margin-top: 0;">
                <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
                <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
              </el-col>
            </el-row>
          </el-form>

          <!-- 数据表格 -->
          <el-table
              v-loading="loading"
              :data="finishFilteredData"
              border
              style="width: 100%; margin-top: 20px;"
              :header-cell-style="{ background: '#f5f7fa' }"
          >
            <el-table-column label="taskId" width="80" align="center">
              <template #default="{ row, $index }">
                {{ $index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="systemOwnerOrg" label="单位名称" min-width="150"
                             align="center" />
            <el-table-column prop="systemName" label="系统名称"
                             min-width="180" align="center" />
            <el-table-column prop="taskType" label="任务类别" width="120"
                             align="center" />
            <el-table-column prop="responsiblePerson" label="任务状态"
                             width="120" align="center" />
            <el-table-column label="完成时间" min-width="160"
                             align="center">
              <template #default="{ row }">
                {{ formatDateTime(row.finishTime) }}
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <el-pagination
              :current-page="queryParams.pageNo"
              :page-sizes="[5, 10, 20, 40]"
              :page-size="queryParams.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="finishFilteredData.length"
              @size-change="finishHandleSizeChange"
              @current-change="finishHandleCurrentChange"
          />
        </div>
      </div>
    </el-card>


    <!-- 统一的上传对话框 -->
    <el-dialog
        :title="'上传文件'"
        :visible.sync="uploadDialogVisible"
        width="500px"
        :close-on-click-modal="false"
    >
      <!-- 上传类型选择 -->
      <div class="upload-type-selector">
        <el-radio-group v-model="currentUploadType" size="medium">
          <el-radio label="issuesList">问题清单</el-radio>
          <el-radio label="evaluationReport">测评报告</el-radio>
        </el-radio-group>
      </div>

      <!-- 问题清单上传区域 -->
      <div v-if="currentUploadType === 'issuesList'" class="upload-section">
        <el-alert
            title="请上传Excel格式的问题清单文件"
            type="info"
            :closable="false"
            class="upload-alert"
        />
        <el-upload
            class="upload-demo"
            drag
            :action="getUploadAction()"
            :headers="uploadHeaders"
            :on-success="handleIssuesImportSuccess"
            :on-error="handleImportError"
            :before-upload="beforeImportUpload"
            accept=".xlsx,.xls"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">支持格式：.xlsx, .xls | 文件大小不超过2MB</div>
        </el-upload>
      </div>

      <!-- 测评报告上传区域 -->
      <div v-if="currentUploadType === 'evaluationReport'" class="upload-section">
        <el-alert
            title="请上传PDF格式的测评报告"
            type="info"
            :closable="false"
            class="upload-alert"
        />
        <el-upload
            class="upload-demo"
            drag
            :action="getUploadAction()"
            :headers="uploadHeaders"
            :on-success="handleReportImportSuccess"
            :on-error="handleImportError"
            :before-upload="beforePdfUpload"
            accept=".pdf"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">支持格式：.pdf | 文件大小不超过5MB</div>
        </el-upload>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="uploadDialogVisible = false">取消</el-button>
      </div>
    </el-dialog>
    <!-- 问题清单导入识别表单对话框 -->
    <el-dialog
        :title="'问题清单导入'"
        :visible.sync="issuesListFormDialogVisible"
        width="600px"
    >
      <el-form ref="issuesListForm" :model="issuesListFormData" label-width="100px" :rules="issuesListRules">
        <el-form-item label="单位名称" prop="systemOwnerOrg">
          <el-input v-model="issuesListFormData.systemOwnerOrg" disabled />
        </el-form-item>
        <el-form-item label="系统名称" prop="systemName">
          <el-input v-model="issuesListFormData.systemName" disabled />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-input v-model="issuesListFormData.category" />
        </el-form-item>
        <el-form-item label="控制点" prop="controlPoint">
          <el-input v-model="issuesListFormData.controlPoint" />
        </el-form-item>
        <el-form-item label="测评项" prop="evaluationItem">
          <el-input v-model="issuesListFormData.evaluationItem" />
        </el-form-item>
        <el-form-item label="符合情况" prop="complianceStatus">
          <el-input v-model="issuesListFormData.complianceStatus" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="issuesListFormDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitIssuesListForm">确认</el-button>
      </div>
    </el-dialog>

    <!-- 测评报告导入识别表单对话框 -->
    <el-dialog
        :title="'测评报告导入'"
        :visible.sync="evaluationListFormDialogVisible"
        width="600px"
    >
      <el-form ref="evaluationListForm" :model="evaluationListFormData" label-width="100px" :rules="evaluationListRules">
        <el-form-item label="所属单位" prop="systemOwnerOrg">
          <el-input v-model="evaluationListFormData.systemOwnerOrg" />
        </el-form-item>
        <el-form-item label="成员单位" prop="memberUnit">
          <el-input v-model="evaluationListFormData.memberUnit"/>
        </el-form-item>
        <el-form-item label="系统名称" prop="systemName">
          <el-input v-model="evaluationListFormData.systemName"/>
        </el-form-item>
        <el-form-item label="系统简称" prop="systemShortName">
          <el-input v-model="evaluationListFormData.systemShortName"/>
        </el-form-item>
        <el-form-item label="网络归属" prop="networkBelonging">
          <el-input v-model="evaluationListFormData.networkBelonging"/>
        </el-form-item>
        <el-form-item label="业务类型" prop="businessType">
          <el-select v-model="evaluationListFormData.businessType" placeholder="请选择">
            <el-option
                v-for="item in businessTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备案号" prop="filingNumber">
          <el-input v-model="evaluationListFormData.filingNumber"/>
        </el-form-item>
        <el-form-item label="定级级别" prop="classificationLevel">
          <el-select v-model="evaluationListFormData.classificationLevel">
            <el-option label="一级" value="一级" />
            <el-option label="二级" value="二级" />
            <el-option label="三级" value="三级" />
            <el-option label="四级" value="四级" />
            <el-option label="五级" value="五级" />
          </el-select>
        </el-form-item>
        <el-form-item label="测评时间" prop="evaluationTime">
          <el-date-picker
              v-model="evaluationListFormData.evaluationTime"
              type="date"
              value-format="yyyy-MM-dd"
              style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="测评结果" prop="evaluationResult">
          <el-select v-model="evaluationListFormData.evaluationResult">
            <el-option label="符合" value="符合" />
            <el-option label="基本符合" value="基本符合" />
            <el-option label="不符合" value="不符合" />
          </el-select>
        </el-form-item>
        <el-form-item label="测评单位" prop="evaluationOrganization">
          <el-input v-model="evaluationListFormData.evaluationOrganization"/>
        </el-form-item>
        <el-form-item label="测评状态" prop="evaluationStatus">
          <el-select v-model="evaluationListFormData.evaluationStatus" style="width: 100%">
            <el-option label="已测评" value="已测评" />
            <el-option label="测评中" value="测评中" />
          </el-select>
        </el-form-item>
        <el-form-item label="系统状态" prop="systemStatus">
          <el-select v-model="evaluationListFormData.systemStatus" style="width: 100%">
            <el-option label="运行中" value="运行中" />
            <el-option label="已下线" value="已下线" />
            <el-option label="已注销" value="已注销" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-input v-model="evaluationListFormData.category" />
        </el-form-item>
        <el-form-item label="控制点" prop="controlPoint">
          <el-input v-model="evaluationListFormData.controlPoint" />
        </el-form-item>
        <el-form-item label="测评项" prop="evaluationItem">
          <el-input v-model="evaluationListFormData.evaluationItem" />
        </el-form-item>
        <el-form-item label="符合情况" prop="complianceStatus">
          <el-input v-model="evaluationListFormData.complianceStatus" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="evaluationListFormDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitEvaluationListForm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import tasksManage, { getTasksList, saveComplexSysProtect } from '@/api/tasksManage'
import moment from 'moment'
import { getToken } from '@/utils/auth'
import * as securityAssessment from '@/api/securityAssessment'

export default {
  name: 'TaskManagement',
  data() {
    return {
      activeMenu: '1',
      loading: false,
      uploadDialogVisible: false,
      currentTask: null,
      issuesListFormDialogVisible: false,
      evaluationListFormDialogVisible: false,
      issuesListFormData: {
        systemOwnerOrg: '',
        systemName: '',
        category: '',
        controlPoint: '',
        evaluationItem: '',
        resultRecord: '',
        complianceStatus: ''
      },
      evaluationListFormData: {
        systemOwnerOrg: '',
        memberUnit: '',
        systemName: '',
        systemShortName: '',
        networkBelonging: '',
        businessType: '',
        filingNumber: '',
        classificationLevel: '',
        evaluationTime: '',
        evaluationResult: '',
        evaluationOrganization: '',
        evaluationStatus: '',
        systemStatus: '',
        category: '',
        controlPoint: '',
        evaluationItem: '',
        complianceStatus: ''
      },
      issuesListRules: {
        category: [{ required: true, trigger: 'blur' }],
        controlPoint: [{ required: true, trigger: 'blur' }],
        evaluationItem: [{ required: true, trigger: 'blur' }],
        resultRecord: [{ required: true, trigger: 'blur' }],
        complianceStatus: [{ required: true, trigger: 'blur' }]
      },
      evaluationListRules: {
        systemOwnerOrg: [{ required: true, trigger: 'blur' }],
        memberUnit: [{ required: true, trigger: 'blur' }],
        systemName: [{ required: true, trigger: 'blur' }],
        systemShortName: [{ required: true, trigger: 'blur' }],
        networkBelonging: [{ required: true, trigger: 'blur' }],
        businessType: [{ required: true, trigger: 'blur' }],
        filingNumber: [{ required: true, trigger: 'blur' }],
        classificationLevel: [{ required: true, trigger: 'blur' }],
        evaluationTime: [{ required: true, trigger: 'blur' }],
        evaluationResult: [{ required: true, trigger: 'blur' }],
        evaluationOrganization: [{ required: true, trigger: 'blur' }],
        evaluationStatus: [{ required: true, trigger: 'blur' }],
        systemStatus: [{ required: true, trigger: 'blur' }],
        category: [{ required: true, trigger: 'blur' }],
        controlPoint: [{ required: true, trigger: 'blur' }],
        evaluationItem: [{ required: true, trigger: 'blur' }],
        complianceStatus: [{ required: true, trigger: 'blur' }]
      },

      businessTypeOptions: [
        { label: '生产作业', value: '生产作业' },
        { label: '指挥调度', value: '指挥调度' },
        { label: '内部办公', value: '内部办公' },
        { label: '公众服务', value: '公众服务' },
        { label: '其他', value: '其他' }
      ],
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      },
      queryParams: {
        pageNo: 1,
        pageSize: 10
      },
      // 待办表单
      pendingQueryForm: {
        systemOwnerOrg: '',
        systemName: '',
        taskType: ''
      },
      // 已完成表单
      finishQueryForm: {
        systemOwnerOrg: '',
        systemName: '',
        taskType: '',
        taskStatus: '',
        responsiblePerson:''

      },
      pendingFilteredData: [],
      finishFilteredData: [],
      unitOptions: [
        { label: '单位A', value: '单位A' },
        { label: '单位B', value: '单位B' },
        { label: '单位C', value: '单位C' }
      ],
      // 上传相关的新增数据
      currentUploadType: 'issuesList', // 当前选中的上传类型
      reportUploadType: 'excel'      // 测评报告子类型
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表数据
    getList() {
      this.loading = true
      getTasksList(this.queryParams)
          .then(response => {
            console.log('API响应数据:', response)
            if (response?.data?.tasks) {
              // 根据任务状态筛选数据
              this.pendingFilteredData = response.data.tasks.filter(task =>
                  task.taskStatus === '待处理' || task.taskStatus === '进行中')
              this.finishFilteredData = response.data.tasks.filter(task =>
                  task.taskStatus === '已完成')
            } else {
              this.pendingFilteredData = []
              this.finishFilteredData = []
            }
          })
          .catch(error => {
            console.error('获取数据失败:', error)
            this.$message.error('获取数据失败')
          })
          .finally(() => {
            this.loading = false
          })
    },

    // 处理查询
    handleQuery() {
      this.queryParams.pageNo = 1
      // 合并查询表单数据到查询参数
      this.queryParams = {
        ...this.queryParams,
        ...(this.activeMenu === '1' ? this.pendingQueryForm : this.finishQueryForm)
      }
      this.getList()
    },

    // 处理重置
    handleReset() {
      if (this.activeMenu === '1') {
        this.pendingQueryForm = {
          unitName: '',
          systemName: '',
          taskType: '',
          taskStatus: ''
        }
      } else {
        // 已完成表单重置
        this.finishQueryForm = {
          taskName: '',
          responsiblePerson: '',
          finishTime: ''
        }
      }
      this.queryParams = {
        pageNo: 1,
        pageSize: this.queryParams.pageSize
      }
      this.getList()
    },

    // 待办重置
    pendingHandleReset() {
      this.pendingQueryForm = {
        systemOwnerOrg: '',
        systemName: '',
        taskType: '',
        taskStatus: '',
        responsiblePerson:''
      }
      this.queryParams = {
        pageNo: 1,
        pageSize: this.queryParams.pageSize
      }
      this.getList()
      this.$message.success('已重置查询条件');
    },

    // 已完成分页
    finishHandleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },

    finishHandleCurrentChange(val) {
      this.queryParams.pageNo = val
      this.getList()
    },

    // 菜单切换
    handleSelect(key) {
      this.activeMenu = key
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      return moment(dateTime).format('YYYY-MM-DD HH:mm:ss')
    },

    // 打开上传对话框
    handleUpload(row) {
      this.currentTask = row
      this.uploadDialogVisible = true
      // 重置上传类型
      this.currentUploadType = 'issuesList'
      this.reportUploadType = 'excel'
    },

    // 获取上传地址
    getUploadAction() {
      if (this.currentUploadType === 'issuesList') {
        return 'http://localhost:9999/tasks/importIssues'
      } else {
        return 'http://localhost:9999/tasks/importReport'
      }
    },
    // 问题清单导入成功处理
    handleIssuesImportSuccess(response, file) {
      if (response.data && response.data.length > 0) {
        const importedData = response.data[0] // 取第一条数据
        console.log(importedData)
        this.issuesListFormData = {
          ...this.issuesListFormData,
          // 优先用 currentTask 的值
          systemOwnerOrg: this.currentTask?.systemOwnerOrg || importedData.systemOwnerOrg || '',
          systemName: this.currentTask?.systemName || importedData.systemName || '',
          // 可选字段
          memberUnit: importedData.memberUnit || '',
          systemShortName: importedData.systemShortName || '',
          networkBelonging: importedData.networkBelonging || '',
          category: importedData.category || '',
          controlPoint: importedData.controlPoint || '',
          evaluationItem: importedData.evaluationItem || '',
          resultRecord: importedData.resultRecord || '',
          complianceStatus: importedData.complianceStatus || ''
        }
        console.log(response)
        // 自动打开问题清单导入识别表单对话框
        this.issuesListFormDialogVisible = true
        this.$message.success('导入成功，数据已填充')
      } else {
        this.$message.error('导入失败，文件内容为空或格式不正确')
      }
      this.uploadDialogVisible = false
      this.importFile = null // 清空已上传文件
    },

    // 测评报告导入成功处理
    handleReportImportSuccess(response, file) {
      if (response.data && response.data.length > 0) {
        const importedData = response.data[0] // 取第一条数据
        this.evaluationListFormData = {
          ...this.evaluationListFormData,
          systemOwnerOrg: importedData.systemOwnerOrg || '',
          memberUnit: importedData.memberUnit || '',
          systemName: importedData.systemName || '',
          systemShortName: importedData.systemShortName || '',
          networkBelonging: importedData.networkBelonging || '',
          businessType: importedData.businessType || '',
          filingNumber: importedData.filingNumber || '',
          classificationLevel: importedData.classificationLevel || '',
          evaluationTime: importedData.evaluationTime || '',
          evaluationResult: importedData.evaluationResult || '',
          evaluationOrganization: importedData.evaluationOrganization || '',
          evaluationStatus: importedData.evaluationStatus || '',
          systemStatus: importedData.systemStatus || '',
          category: importedData.category || '',
          controlPoint: importedData.controlPoint || '',
          evaluationItem: importedData.evaluationItem || '',
          complianceStatus: importedData.complianceStatus || ''
        }
        console.log(response)
        // 自动打开问题清单导入识别表单对话框
        this.evaluationListFormDialogVisible = true
        this.$message.success('导入成功，数据已填充')
      } else {
        this.$message.error('导入失败，文件内容为空或格式不正确')
      }
      this.uploadDialogVisible = false
      this.importFile = null // 清空已上传文件
    },

    // 提交问题清单表单
    submitIssuesListForm() {
      this.$refs.issuesListForm.validate(valid => {
        if (!valid) return
        try {
          this.markTaskAsCompleted(this.currentTask.taskId)
          securityAssessment.addSysProtect(this.issuesListFormData)
          console.log('问题清单', this.issuesListFormData)
          this.$message.success('问题清单导入成功')
          this.issuesListFormDialogVisible = false
          this.getList()
        } catch (error) {
          this.$message.error('操作失败')
        }
      })
    },

    // 提交测评报告表单
    submitEvaluationListForm() {
      this.$refs.evaluationListForm.validate(valid => {
        if (!valid) return

        try {
          this.markTaskAsCompleted(this.currentTask.taskId)
          securityAssessment.addSysProtect(this.evaluationListFormData)
          this.$message.success('测评报告导入成功')
          this.evaluationListFormDialogVisible = false
          this.getList()
        } catch (error) {
          this.$message.error('操作失败')
        }
      })
    },
    // 标记任务为已完成
    markTaskAsCompleted(taskId) {
      saveComplexSysProtect(taskId)
          .then(() => {
            this.$message.success('任务已完成')
            this.getList() // 刷新列表
          })
          .catch(error => {
            console.error('更新任务状态失败:', error)
            this.$message.error('更新任务状态失败')
          })
    },

    // 下线任务
    handleOffline(row) {
      this.$confirm('确认下线该设备？设备下线后将不再推送任务', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
          .then(() => {
            // 先删除任务
            return tasksManage.deleteTask(row.taskId)
          })
          .then(() => {
            this.$message.success('设备已下线')
            // 更新系统状态为已下线
            const updateData = {
              systemOwnerOrg: row.systemOwnerOrg,
              systemName: row.systemName,
              systemStatus: '已下线'
            }
            // 使用现有的updateSysProtect方法
            return securityAssessment.offline(updateData)
          })
          .then(() => {
            console.log('等保系统状态已更新为已下线')
            this.getList() // 刷新列表
          })
          .catch(error => {
            console.error('操作失败:', error)
            this.$message.error('操作失败: ' + (error.message || '未知错误'))
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消下线'
            })
          })
    },
    // 上传前校验
    beforeImportUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          file.type === 'application/vnd.ms-excel'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isExcel) {
        this.$message.error('只能上传 Excel 文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('文件大小不能超过 2MB!')
        return false
      }
      return true
    },

    // PDF上传前校验
    beforePdfUpload(file) {
      const isPdf = file.type === 'application/pdf'
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isPdf) {
        this.$message.error('只能上传 PDF 文件!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('文件大小不能超过 5MB!')
        return false
      }
      return true
    },

    // 处理导入错误
    handleImportError(err, file) {
      console.error('导入失败:', err)
      this.$message.error('文件导入失败，请重试')
    },

    // 状态标签样式
    getStatusType(status) {
      const typeMap = {
        '待处理': 'warning',
        '进行中': 'primary',
        '已完成': 'success'
      }
      return typeMap[status] || 'info'
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f0f2f5;
}

.box-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .card-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }
}

.search-form {
  padding: 20px 0;

  .el-form-item {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    .el-form-item__label {
      color: #606266;
      font-weight: 500;
      padding-right: 12px;
      text-align: right;
    }
    .el-form-item__content {
      width: calc(100% - 85px);
    }
  }
  .el-input, .el-select, .el-date-picker, .el-date-editor {
    width: 100%;
    height: 36px !important;
    line-height: 36px !important;
    display: block !important;
    vertical-align: middle !important;
    box-sizing: border-box;
  }
  .el-input__inner {
    height: 36px !important;
    line-height: 36px !important;
    box-sizing: border-box;
    display: block !important;
    vertical-align: middle !important;
  }
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    height: 36px !important;
    line-height: 36px !important;
    box-sizing: border-box;
    display: block !important;
    vertical-align: middle !important;
  }
}
.date-picker-wrapper {
  height: 36px;
  display: flex;
  align-items: center;
}
.date-picker-wrapper .el-date-editor {
  width: 100% !important;
  height: 36px !important;
  line-height: 36px !important;
  display: flex !important;
  align-items: center !important;
}
.date-picker-wrapper .el-input__inner {
  height: 36px !important;
  line-height: 36px !important;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 20px;
}

.el-button {
  padding: 8px 15px;
  margin: 0 8px;
}

.el-table {
  margin-top: 20px;

  th {
    background-color: #f5f7fa !important;
    color: #606266;
    font-weight: 500;
  }
}

@media screen and (max-width: 1400px) {
  .el-col {
    width: 100% !important;
    margin-bottom: 10px;
  }
}

.pagination-container {
  padding: 15px;
  background: white;
  border-top: 1px solid #e8e8e8;
  text-align: right;
}

:deep(.el-pagination) {
  padding: 0;
  margin: 0;
  font-weight: normal;
}

.el-button + .el-link {
  margin-left: 10px;
}

/* 上传对话框样式 */
.upload-type-selector {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  padding: 10px 0;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.upload-section {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 15px;
}

.upload-alert {
  margin-bottom: 15px;
}

.report-type-selector {
  text-align: center;
  margin-bottom: 15px;
}

.upload-demo {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 20px;
  background-color: #fafafa;
  transition: border-color 0.3s;

  &:hover {
    border-color: #409EFF;
  }

  .el-upload {
    width: 100%;
  }

  .el-upload-dragger {
    width: 100%;
    padding: 30px;
    background-color: #fafafa;
  }

  .el-upload__tip {
    color: #909399;
    font-size: 12px;
    margin-top: 10px;
    text-align: center;
  }
}

.dialog-footer {
  text-align: center;
  padding-top: 15px;
  border-top: 1px solid #eee;
}
</style>
