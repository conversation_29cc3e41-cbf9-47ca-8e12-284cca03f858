<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header">
        <span class="card-title">所属单位安全性分析</span>
      </div>

      <!-- 输入表单 -->
      <el-form :model="inputForm" :inline="true" class="search-form" size="small" label-width="120px">
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item label="漏洞数量">
              <el-input v-model="inputForm.vulnerabilityCount" placeholder="请输入漏洞数量" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="历史记录">
              <el-input v-model="inputForm.historyRecord" placeholder="请输入历史记录" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="系统状态">
              <el-select v-model="inputForm.systemStatus" placeholder="请选择系统状态" clearable style="width: 100%">
                <el-option label="正常" value="normal"></el-option>
                <el-option label="异常" value="abnormal"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="风险评估参数">
              <el-input v-model="inputForm.riskModelParams" placeholder="请输入风险评估模型参数" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24" style="text-align: center; margin-top: 20px;">
            <el-button type="primary" icon="el-icon-search" @click="performAnalysis">开始分析</el-button>
            <el-button icon="el-icon-refresh" @click="resetForm">重置</el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 分析结果展示 -->
      <div v-if="analysisResult">
        <el-card style="margin-top: 20px;">
          <template #header>
            <span>单位安全报告</span>
          </template>
          <el-table :data="[analysisResult]" border style="width: 100%; margin-top: 20px;" :header-cell-style="{ background: '#f5f7fa' }">
            <el-table-column prop="riskLevel" label="风险等级" width="120" align="center">
              <template slot-scope="scope">
                <el-tag :type="getRiskLevelType(scope.row.riskLevel)">{{ scope.row.riskLevel }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="weaknesses" label="薄弱环节" min-width="200" align="center"></el-table-column>
          </el-table>
        </el-card>

        <el-card style="margin-top: 20px;">
          <template #header>
            <span>历史趋势图表</span>
          </template>
          <div id="historyTrendChart" style="width: 100%; height: 400px;"></div>
        </el-card>

        <el-card style="margin-top: 20px;">
          <template #header>
            <span>风险应对建议</span>
          </template>
          <p>{{ analysisResult.suggestions }}</p>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'UnitSecurityAnalysis',
  data() {
    return {
      inputForm: {
        vulnerabilityCount: '',
        historyRecord: '',
        systemStatus: '',
        riskModelParams: ''
      },
      analysisResult: null,
      // 模拟历史分析结果
      historicalResults: [
        { date: '2023-01', riskLevel: '低' },
        { date: '2023-02', riskLevel: '低' },
        { date: '2023-03', riskLevel: '中' },
        { date: '2023-04', riskLevel: '高' },
        { date: '2023-05', riskLevel: '中' },
        { date: '2023-06', riskLevel: '低' }
      ]
    };
  },
  methods: {
    performAnalysis() {
      // 安全等级评估
      let riskLevel = '低';
      if (parseInt(this.inputForm.vulnerabilityCount) > 10 || this.inputForm.systemStatus === 'abnormal') {
        riskLevel = '高';
      } else if (parseInt(this.inputForm.vulnerabilityCount) > 5) {
        riskLevel = '中';
      }

      // 风险趋势分析
      let trend = '稳定';
      const lastRiskLevel = this.historicalResults[this.historicalResults.length - 1].riskLevel;
      if (riskLevel === '高' && lastRiskLevel!== '高') {
        trend = '恶化';
      } else if (riskLevel === '低' && lastRiskLevel!== '低') {
        trend = '改善';
      }

      // 生成安全报告
      let weaknesses = '';
      if (this.inputForm.systemStatus === 'abnormal') {
        weaknesses = '系统存在异常，可能存在潜在安全风险';
      } else if (parseInt(this.inputForm.vulnerabilityCount) > 0) {
        weaknesses = '存在一定数量的漏洞，需要及时修复';
      }

      let suggestions = '';
      if (riskLevel === '高') {
        suggestions = '立即对系统进行全面检查和修复，加强安全防护措施';
      } else if (riskLevel === '中') {
        suggestions = '尽快修复已知漏洞，加强安全监控';
      } else {
        suggestions = '继续保持系统安全，定期进行安全检查';
      }

      this.analysisResult = {
        riskLevel,
        weaknesses,
        suggestions,
        trend
      };

      // 绘制历史趋势图表
      this.drawHistoryTrendChart();
    },
    resetForm() {
      this.inputForm = {
        vulnerabilityCount: '',
        historyRecord: '',
        systemStatus: '',
        riskModelParams: ''
      };
      this.analysisResult = null;
      const chartDom = document.getElementById('historyTrendChart');
      if (chartDom) {
        const myChart = echarts.getInstanceByDom(chartDom);
        if (myChart) {
          myChart.clear();
        }
      }
    },
    getRiskLevelType(riskLevel) {
      return {
        '低': 'success',
        '中': 'warning',
        '高': 'danger'
      }[riskLevel];
    },
    drawHistoryTrendChart() {
      const chartDom = document.getElementById('historyTrendChart');
      const myChart = echarts.init(chartDom);
      const option = {
        xAxis: {
          type: 'category',
          data: this.historicalResults.map(item => item.date)
        },
        yAxis: {
          type: 'category',
          data: ['低', '中', '高']
        },
        series: [
          {
            data: this.historicalResults.map(item => [item.date, item.riskLevel]),
            type: 'line',
            symbolSize: 10
          }
        ]
      };
      myChart.setOption(option);
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f0f2f5;
}

.box-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .card-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }
}

.search-form {
  padding: 20px 0;

  .el-form-item {
    margin-bottom: 20px;
    width: 100%;

    .el-form-item__label {
      color: #606266;
      font-weight: 500;
      padding-right: 12px;
      text-align: right;
    }

    .el-form-item__content {
      width: calc(100% - 120px);
    }
  }

  .el-input,
  .el-select {
    width: 100%;
  }
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 20px;
}

.el-button {
  padding: 8px 15px;
  margin: 0 8px;
}

.el-table {
  margin-top: 20px;

  th {
    background-color: #f5f7fa !important;
    color: #606266;
    font-weight: 500;
  }
}

@media screen and (max-width: 1400px) {
  .el-col {
    width: 100% !important;
    margin-bottom: 10px;
  }
}

.pagination-container {
  padding: 15px;
  background: white;
  border-top: 1px solid #e8e8e8;
  text-align: right;
}

:deep(.el-pagination) {
  padding: 0;
  margin: 0;
  font-weight: normal;
}
</style>
