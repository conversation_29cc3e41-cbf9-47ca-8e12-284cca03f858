<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header">
        <span class="card-title">等保管理</span>
      </div>

      <!-- 搜索表单 -->
      <el-form :model="queryForm" :inline="true" class="search-form" size="small" label-width="85px">
        <el-row :gutter="16" align="middle">
          <el-col :span="6">
            <el-form-item label="所属单位">
              <el-select v-model="queryForm.systemOwnerOrg" placeholder="请选择所属单位" clearable style="width: 100%">
                <el-option label="中国核电" value="中国核电"></el-option>
                <el-option label="秦山核电" value="秦山核电"></el-option>
                <el-option label="江苏核电" value="江苏核电"></el-option>
                <el-option label="福清核电" value="福清核电"></el-option>
                <el-option label="海南核电" value="海南核电"></el-option>
                <el-option label="三门核电" value="三门核电"></el-option>
                <el-option label="霞浦核电" value="霞浦核电"></el-option>
                <el-option label="漳州核电" value="漳州核电"></el-option>
                <el-option label="中核武汉" value="中核武汉"></el-option>
                <el-option label="中核能源" value="中核能源"></el-option>
                <el-option label="运行研究院" value="运行研究院"></el-option>
                <el-option label="研究运维" value="研究运维"></el-option>
                <el-option label="辽宁核电" value="辽宁核电"></el-option>
                <el-option label="中核山东" value="中核山东"></el-option>
                <el-option label="中核储能" value="中核储能"></el-option>
                <el-option label="中核苏能" value="中核苏能"></el-option>
                <el-option label="中核海得" value="中核海得"></el-option>
                <el-option label="河北核电" value="河北核电"></el-option>
                <el-option label="庄河核电" value="庄河核电"></el-option>
                <el-option label="中核光电" value="中核光电"></el-option>
                <el-option label="桃花江核电" value="桃花江核电"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="系统名称">
              <el-input v-model="queryForm.systemName" placeholder="请输入系统名称" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="业务类型">
              <el-select v-model="queryForm.businessType" placeholder="请选择业务类型" clearable style="width: 100%">
                <el-option label="生产作业" value="生产作业" />
                <el-option label="指挥调度" value="指挥调度" />
                <el-option label="内部办公" value="内部办公" />
                <el-option label="公众服务" value="公众服务" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="备案号">
              <el-input v-model="queryForm.filingNumber" placeholder="请输入备案号" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16" align="middle">
          <el-col :span="6">
            <el-form-item label="定级级别">
              <el-select v-model="queryForm.classificationLevel" placeholder="请选择定级级别" clearable style="width: 100%">
                <el-option label="一级" value="一级" />
                <el-option label="二级" value="二级" />
                <el-option label="三级" value="三级" />
                <el-option label="四级" value="四级" />
                <el-option label="五级" value="五级" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="测评时间">
              <el-date-picker
                  v-model="queryForm.evaluationTime"
                  type="date"
                  placeholder="选择测评时间"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="测评结果">
              <el-select v-model="queryForm.evaluationResult" placeholder="请选择测评结果" clearable style="width: 100%">
                <el-option label="符合" value="符合" />
                <el-option label="基本符合" value="基本符合" />
                <el-option label="不符合" value="不符合" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="测评单位">
              <el-input v-model="queryForm.evaluationOrganization" placeholder="请输入测评单位" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: center; margin-top: 10px;">
            <el-button type="success" icon="el-icon-plus" @click="handleAdd">新增</el-button>
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button type="warning" icon="el-icon-download" @click="handleExport">导出</el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 数据表格 -->
      <el-table
          v-loading="loading"
          :data="tableData"
          border
          style="width: 100%; margin-top: 20px;"
          :header-cell-style="{ background: '#f5f7fa' }"
      >
        <el-table-column prop="systemOwnerOrg" label="所属单位" min-width="120" align="center" />
        <el-table-column prop="systemName" label="系统名称" min-width="120" align="center" />
        <el-table-column prop="businessType" label="业务类型" min-width="120" align="center" />
        <el-table-column prop="filingNumber" label="备案号" min-width="120" align="center" />
        <el-table-column prop="classificationLevel" label="定级级别" width="100" align="center" />
        <el-table-column prop="evaluationTime" label="测评时间" min-width="120" align="center" />
        <el-table-column prop="evaluationResult" label="测评结果" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getResultType(scope.row.evaluationResult)">
              {{ scope.row.evaluationResult }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="evaluationOrganization" label="测评单位" min-width="120" align="center" />
        <el-table-column prop="systemStatus" label="系统状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getSystemStatusType(scope.row.systemStatus)">
              {{ scope.row.systemStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="380" align="center">
          <template slot-scope="scope">
            <el-link type="primary" @click="handleDetail(scope.row)">详情</el-link>
            <el-divider direction="vertical" />
            <el-link type="warning" @click="handleEdit(scope.row)">编辑</el-link>
            <el-divider direction="vertical" />
            <el-link type="danger" @click="handleDelete(scope.row)">删除</el-link>
            <el-divider direction="vertical" />
            <el-link type="warning" @click="handleIssuesListImport(scope.row)">问题清单导入</el-link>
            <el-divider direction="vertical" />
            <el-link type="warning" @click="handleEvaluationReportImport(scope.row)">测评报告导入</el-link>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 分页 -->
    <el-pagination
        :current-page="queryParams.pageNo"
        :page-sizes="[5, 10, 20, 40]"
        :page-size="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
    />

    <!-- 导入对话框 -->
    <el-dialog
        title="导入等保数据"
        :visible.sync="importDialogVisible"
        width="400px"
        :close-on-click-modal="false"
    >
      <el-upload
          class="upload-demo"
          drag
          action="http://localhost:9999/sysProtect/import"
          :headers="uploadHeaders"
          :on-success="handleImportSuccess"
          :on-error="handleImportError"
          :before-upload="beforeImportUpload"
          accept=".xlsx,.xls"
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip">只能上传 xlsx/xls 文件</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">取 消</el-button>
        <!--        <el-button type="primary" @click="downloadTemplate">下载模板</el-button>-->
      </div>
    </el-dialog>

    <!-- 问题清单导入对话框 -->
    <el-dialog
        title="问题清单导入"
        :visible.sync="issuesListImportDialogVisible"
        width="400px"
        :close-on-click-modal="false"
    >
      <el-upload
          class="upload-demo"
          drag
          action="/sysProtect/import"
          :headers="uploadHeaders"
          :on-success="handleImportSuccess"
          :on-error="handleImportError"
          :before-upload="beforeImportUpload"
          accept=".xlsx,.xls"
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip">只能上传 xlsx/pdf 文件</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="issuesListImportDialogVisible = false">取 消</el-button>
        <!--        <el-button type="primary" @click="downloadTemplate">下载模板</el-button>-->
      </div>
    </el-dialog>

    <!-- 测评报告导入对话框 -->
    <el-dialog
        title="测评报告导入"
        :visible.sync="evaluationReportImportDialogVisible"
        width="400px"
        :close-on-click-modal="false"
    >
      <el-upload
          class="upload-demo"
          drag
          action="http://localhost:9999/sysProtect/importPdf"
          :headers="uploadHeaders"
          :on-success="handleEvaluationListImportSuccess"
          :on-error="handleImportError"
          :before-upload="beforePdfUpload"
          accept=".xlsx,.pdf"
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip">只能上传 xlsx/pdf 文件</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="evaluationReportImportDialogVisible = false">取 消</el-button>
        <!--        <el-button type="primary" @click="downloadTemplate">下载模板</el-button>-->
      </div>
    </el-dialog>
    <!-- 新增/编辑对话框 -->
    <el-dialog
        :title="dialogType === 'add' ? '新增等保' : '编辑等保'"
        :visible.sync="dialogVisible"
        width="600px"
    >
      <el-form ref="form" :model="formData" label-width="100px" :rules="rules">
        <el-form-item label="所属单位" prop="systemOwnerOrg">
          <el-input v-model="formData.systemOwnerOrg" />
        </el-form-item>
        <el-form-item label="系统名称" prop="systemName">
          <el-input v-model="formData.systemName" />
        </el-form-item>
        <el-form-item label="业务类型" prop="businessType">
          <el-select v-model="formData.businessType" placeholder="请选择">
            <el-option
                v-for="item in businessTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备案号" prop="filingNumber">
          <el-input v-model="formData.filingNumber" />
        </el-form-item>
        <el-form-item label="定级级别" prop="classificationLevel">
          <el-select v-model="formData.classificationLevel">
            <el-option label="一级" value="一级" />
            <el-option label="二级" value="二级" />
            <el-option label="三级" value="三级" />
            <el-option label="四级" value="四级" />
            <el-option label="五级" value="五级" />
          </el-select>
        </el-form-item>
        <el-form-item label="测评时间" prop="evaluationTime">
          <el-date-picker
              v-model="formData.evaluationTime"
              type="date"
              value-format="yyyy-MM-dd"
              style="width: 93%"
          />
        </el-form-item>
        <el-form-item label="测评结果" prop="evaluationResult">
          <el-select v-model="formData.evaluationResult">
            <el-option label="符合" value="符合" />
            <el-option label="基本符合" value="基本符合" />
            <el-option label="不符合" value="不符合" />
          </el-select>
        </el-form-item>

        <el-form-item label="测评单位" prop="evaluationOrganization">
          <el-input v-model="formData.evaluationOrganization" />
        </el-form-item>
        <el-form-item label="测评状态" prop="evaluationStatus">
          <el-select v-model="formData.evaluationStatus" style="width: 100%">
            <el-option label="已测评" value="已测评" />
            <el-option label="测评中" value="测评中" />
          </el-select>
        </el-form-item>
        <el-form-item label="系统状态" prop="systemStatus">
          <el-select v-model="formData.systemStatus" style="width: 100%">
            <el-option label="运行中" value="运行中" />
            <el-option label="已下线" value="已下线" />
            <el-option label="已注销" value="已注销" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button type="warning" icon="el-icon-upload2" @click="handleImport">导入</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确认</el-button>
      </div>
    </el-dialog>
    <!-- 问题清单导入对话框 -->
    <el-dialog
        title="问题清单导入"
        :visible.sync="issuesListImportDialogVisible"
        width="400px"
        :close-on-click-modal="false"
    >
      <el-upload
          class="upload-demo"
          drag
          action="http://localhost:9999/sysProtect/import"
          :headers="uploadHeaders"
          :on-success="handleIssuesListImportSuccess"
          :on-error="handleImportError"
          :before-upload="beforeImportUpload"
          accept=".xlsx,.xls"
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip">只能上传 xlsx/xls 文件</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="issuesListImportDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 问题清单导入识别表单对话框 -->
    <el-dialog
        :title="'新增等保问题清单'"
        :visible.sync="issuesListFormDialogVisible"
        width="600px"
    >
      <el-form ref="issuesListForm" :model="issuesListFormData" label-width="100px" :rules="issuesListRules">
        <el-form-item label="所属单位" prop="systemOwnerOrg">
          <el-input v-model="issuesListFormData.systemOwnerOrg" disabled />
        </el-form-item>
        <el-form-item label="系统名称" prop="systemName">
          <el-input v-model="issuesListFormData.systemName" disabled />
        </el-form-item>
        <el-form-item label="业务类型" prop="businessType">
          <el-select v-model="issuesListFormData.businessType" placeholder="请选择" disabled>
            <el-option
                v-for="item in businessTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备案号" prop="filingNumber">
          <el-input v-model="issuesListFormData.filingNumber" disabled />
        </el-form-item>
        <el-form-item label="定级级别" prop="classificationLevel">
          <el-select v-model="issuesListFormData.classificationLevel" disabled>
            <el-option label="一级" value="一级" />
            <el-option label="二级" value="二级" />
            <el-option label="三级" value="三级" />
            <el-option label="四级" value="四级" />
            <el-option label="五级" value="五级" />
          </el-select>
        </el-form-item>
        <el-form-item label="测评时间" prop="evaluationTime">
          <el-date-picker
              v-model="issuesListFormData.evaluationTime"
              type="date"
              value-format="yyyy-MM-dd"
              style="width: 100%"
              disabled
          />
        </el-form-item>
        <el-form-item label="测评结果" prop="evaluationResult">
          <el-select v-model="issuesListFormData.evaluationResult" disabled>
            <el-option label="符合" value="符合" />
            <el-option label="基本符合" value="基本符合" />
            <el-option label="不符合" value="不符合" />
          </el-select>
        </el-form-item>
        <el-form-item label="测评单位" prop="evaluationOrganization">
          <el-input v-model="issuesListFormData.evaluationOrganization" disabled />
        </el-form-item>
        <el-form-item label="系统状态" prop="systemStatus">
          <el-select v-model="issuesListFormData.systemStatus" style="width: 100%" disabled>
            <el-option label="运行中" value="运行中" />
            <el-option label="已下线" value="已下线" />
            <el-option label="已注销" value="已注销" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-input v-model="issuesListFormData.category" />
        </el-form-item>
        <el-form-item label="控制点" prop="controlPoint">
          <el-input v-model="issuesListFormData.controlPoint" />
        </el-form-item>
        <el-form-item label="测评项" prop="evaluationItem">
          <el-input v-model="issuesListFormData.evaluationItem" />
        </el-form-item>
        <el-form-item label="结果记录" prop="resultRecord">
          <el-input v-model="issuesListFormData.resultRecord" />
        </el-form-item>
        <el-form-item label="符合情况" prop="complianceStatus">
          <el-input v-model="issuesListFormData.complianceStatus" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="issuesListFormDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitIssuesListForm">确认</el-button>
      </div>
    </el-dialog>

    <!-- 测评报告导入识别表单对话框 -->
    <el-dialog
        :title="'新增等保测评报告'"
        :visible.sync="evaluationListFormDialogVisible"
        width="600px"
    >
      <el-form ref="evaluationListForm" :model="evaluationListFormData" label-width="100px" :rules="evaluationListRules">
        <el-form-item label="所属单位" prop="systemOwnerOrg">
          <el-input v-model="evaluationListFormData.systemOwnerOrg" />
        </el-form-item>
        <el-form-item label="成员单位" prop="memberUnit">
          <el-input v-model="evaluationListFormData.memberUnit"/>
        </el-form-item>
        <el-form-item label="系统名称" prop="systemName">
          <el-input v-model="evaluationListFormData.systemName"/>
        </el-form-item>
        <el-form-item label="系统简称" prop="systemShortName">
          <el-input v-model="evaluationListFormData.systemShortName"/>
        </el-form-item>
        <el-form-item label="网络归属" prop="networkBelonging">
          <el-input v-model="evaluationListFormData.networkBelonging"/>
        </el-form-item>
        <el-form-item label="业务类型" prop="businessType">
          <el-select v-model="evaluationListFormData.businessType" placeholder="请选择">
            <el-option
                v-for="item in businessTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备案号" prop="filingNumber">
          <el-input v-model="evaluationListFormData.filingNumber"/>
        </el-form-item>
        <el-form-item label="定级级别" prop="classificationLevel">
          <el-select v-model="evaluationListFormData.classificationLevel">
            <el-option label="一级" value="一级" />
            <el-option label="二级" value="二级" />
            <el-option label="三级" value="三级" />
            <el-option label="四级" value="四级" />
            <el-option label="五级" value="五级" />
          </el-select>
        </el-form-item>
        <el-form-item label="测评时间" prop="evaluationTime">
          <el-date-picker
              v-model="evaluationListFormData.evaluationTime"
              type="date"
              value-format="yyyy-MM-dd"
              style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="测评结果" prop="evaluationResult">
          <el-select v-model="evaluationListFormData.evaluationResult">
            <el-option label="符合" value="符合" />
            <el-option label="基本符合" value="基本符合" />
            <el-option label="不符合" value="不符合" />
          </el-select>
        </el-form-item>
        <el-form-item label="测评单位" prop="evaluationOrganization">
          <el-input v-model="evaluationListFormData.evaluationOrganization"/>
        </el-form-item>
        <el-form-item label="测评状态" prop="evaluationStatus">
          <el-select v-model="evaluationListFormData.evaluationStatus" style="width: 100%">
            <el-option label="已测评" value="已测评" />
            <el-option label="测评中" value="测评中" />
          </el-select>
        </el-form-item>
        <el-form-item label="系统状态" prop="systemStatus">
          <el-select v-model="evaluationListFormData.systemStatus" style="width: 100%">
            <el-option label="运行中" value="运行中" />
            <el-option label="已下线" value="已下线" />
            <el-option label="已注销" value="已注销" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-input v-model="evaluationListFormData.category" />
        </el-form-item>
        <el-form-item label="控制点" prop="controlPoint">
          <el-input v-model="evaluationListFormData.controlPoint" />
        </el-form-item>
        <el-form-item label="测评项" prop="evaluationItem">
          <el-input v-model="evaluationListFormData.evaluationItem" />
        </el-form-item>
        <el-form-item label="符合情况" prop="complianceStatus">
          <el-input v-model="evaluationListFormData.complianceStatus" />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="evaluationListFormDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitEvaluationListForm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as securityAssessment from '@/api/securityAssessment'
import { getToken } from '@/utils/auth'



export default {
  name: 'SecurityAssessmentDetail',
  data() {
    return {
      exportLoading: false,
      loading: false,
      total: 0,
      dialogVisible: false,
      dialogType: 'add',
      queryParams: {
        pageNo: 1,
        pageSize: 5
      },
      queryForm: {
        systemOwnerOrg: '',
        memberUnit: '',
        systemName: '',
        systemShortName: '',
        networkBelonging: '',
        businessType: '',
        filingNumber: '',
        classificationLevel: '',
        evaluationTime: '',
        evaluationResult: '',
        evaluationOrganization: '',
        evaluationStatus: '',
        systemStatus: '',
        category: '',
        controlPoint: '',
        evaluationItem: '',
        resultRecord: '',
        complianceStatus: ''
      },
      formData: {
        applicationId: null,
        systemOwnerOrg: '',
        systemName: '',
        businessType: '',
        filingNumber: '',
        classificationLevel: '',
        evaluationResult: '',
        evaluationTime: '',
        systemStatus: '',
        evaluationOrganization: ''
      },
      rules: {
        systemName: [{ required: true, trigger: 'blur' }],
        systemOwnerOrg: [{ required: true, trigger: 'blur' }],
        businessType: [{ required: true, trigger: 'blur' }],
        filingNumber: [{ required: true, trigger: 'blur' }],
        evaluationResult: [{ required: true, trigger: 'blur' }],
        systemStatus: [{ required: true, trigger: 'blur' }],
        evaluationTime: [{ required: true, trigger: 'blur' }],
        evaluationOrganization: [{ required: true, trigger: 'blur' }],
        evaluationStatus: [{ required: true, trigger: 'blur' }],
        classificationLevel: [{ required: true, trigger: 'blur' }]
      },
      issuesListImportDialogVisible: false,
      issuesListFormDialogVisible: false,
      issuesListFormData: {
        systemOwnerOrg: '',
        systemName: '',
        businessType: '',
        filingNumber: '',
        classificationLevel: '',
        evaluationTime: '',
        evaluationResult: '',
        evaluationOrganization: '',
        systemStatus: '',
        category: '',
        controlPoint: '',
        evaluationItem: '',
        resultRecord: '',
        complianceStatus: ''
      },
      issuesListRules: {
        category: [{ required: true, trigger: 'blur' }],
        controlPoint: [{ required: true, trigger: 'blur' }],
        evaluationItem: [{ required: true, trigger: 'blur' }],
        resultRecord: [{ required: true, trigger: 'blur' }],
        complianceStatus: [{ required: true, trigger: 'blur' }]
      },
      evaluationListFormDialogVisible: false,
      evaluationListFormData: {
        systemOwnerOrg: '',
        memberUnit: '',
        systemName: '',
        systemShortName: '',
        networkBelonging: '',
        businessType: '',
        filingNumber: '',
        classificationLevel: '',
        evaluationTime: '',
        evaluationResult: '',
        evaluationOrganization: '',
        evaluationStatus: '',
        systemStatus: '',
        category: '',
        controlPoint: '',
        evaluationItem: '',
        complianceStatus: ''
      },
      evaluationListRules: {
        systemOwnerOrg: [{ required: true, trigger: 'blur' }],
        memberUnit: [{ required: true, trigger: 'blur' }],
        systemName: [{ required: true, trigger: 'blur' }],
        systemShortName: [{ required: true, trigger: 'blur' }],
        networkBelonging: [{ required: true, trigger: 'blur' }],
        businessType: [{ required: true, trigger: 'blur' }],
        filingNumber: [{ required: true, trigger: 'blur' }],
        classificationLevel: [{ required: true, trigger: 'blur' }],
        evaluationTime: [{ required: true, trigger: 'blur' }],
        evaluationResult: [{ required: true, trigger: 'blur' }],
        evaluationOrganization: [{ required: true, trigger: 'blur' }],
        evaluationStatus: [{ required: true, trigger: 'blur' }],
        systemStatus: [{ required: true, trigger: 'blur' }],
        category: [{ required: true, trigger: 'blur' }],
        controlPoint: [{ required: true, trigger: 'blur' }],
        evaluationItem: [{ required: true, trigger: 'blur' }],
        complianceStatus: [{ required: true, trigger: 'blur' }]
      },
      businessTypeOptions: [
        { label: '生产作业', value: '生产作业' },
        { label: '指挥调度', value: '指挥调度' },
        { label: '内部办公', value: '内部办公' },
        { label: '公众服务', value: '公众服务' },
        { label: '其他', value: '其他' }
      ],
      tableData: [],
      importDialogVisible: false,
      evaluationReportImportDialogVisible: false,
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {

    getList() {
      this.loading = true
      securityAssessment.getSysProtectList(this.queryParams)
          .then(response => {
            if (response?.data) {
              this.tableData = response.data.sysProtect || []
              this.total = response.data.total || 0

              // 如果有查询条件，同步到queryForm
              if (response.data.queryParams) {
                this.queryForm = {
                  ...this.queryForm,
                  ...response.data.queryParams
                }
              }
            }
          })
          .catch(() => {
            this.$message.error('获取数据失败')
          })
          .finally(() => {
            this.loading = false
          })
    },

    handleQuery() {
      this.queryParams.pageNo = 1
      this.queryParams = { ...this.queryParams, ...this.queryForm }
      this.getList()
    },

    handleReset() {
      this.queryForm = {
        systemOwnerOrg: '',
        memberUnit: '',
        systemName: '',
        systemShortName: '',
        networkBelonging: '',
        businessType: '',
        filingNumber: '',
        classificationLevel: '',
        evaluationTime: '',
        evaluationResult: '',
        plannedEvaluationTime: '',
        evaluationOrganization: '',
        evaluationStatus: '',
        systemStatus: '',
        category: '',
        controlPoint: '',
        evaluationItem: '',
        resultRecord: '',
        complianceStatus: ''
      }
      this.queryParams = { pageNo: 1, pageSize: this.queryParams.pageSize }
      this.getList()
    },

    handleEdit(row) {
      this.dialogType = 'edit'
      this.formData = { ...row }
      this.dialogVisible = true
    },

    handleDelete(row) {
      this.$confirm('确认删除该记录？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
          .then(() => {
            securityAssessment.deleteSysProtect(row.applicationId)
            this.$message.success('删除成功')
            this.getList()
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            })
          })
    },

    submitForm() {
      this.$refs.form.validate(async valid => {
        if (!valid) return

        try {
          if (this.dialogType === 'add') {
            securityAssessment.addSysProtect(this.formData)
            this.$message.success('新增成功')
          } else {
            securityAssessment.updateSysProtect(this.formData)
            this.$message.success('修改成功')
          }
          this.dialogVisible = false
          this.getList()
        } catch (error) {
          this.$message.error('操作失败')
        }
      })
    },

    // 查看详情
    handleDetail(row) {
      // 跳转到详情页面并传递筛选条件
      this.$router.push({
        path: 'detail',
        query: {
          systemOwnerOrg: row.systemOwnerOrg,
          systemName: row.systemName,
          businessType: row.businessType,
          filingNumber: row.filingNumber,
          classificationLevel: row.classificationLevel,
          evaluationResult: row.evaluationResult,
          evaluationOrganization: row.evaluationOrganization,
          systemStatus: row.systemStatus
        }
      })
    },

    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },

    handleCurrentChange(val) {
      this.queryParams.pageNo = val
      this.getList()
    },

    getResultType(result) {
      const typeMap = {
        '符合': 'success',
        '基本符合': 'warning',
        '不符合': 'danger'
      }
      return typeMap[result] || 'info'
    },

    getSystemStatusType(status) {
      const typeMap = {
        '运行中': 'success',
        '已下线': 'info',
        '已注销': 'danger'
      }
      return typeMap[status] || 'info'
    },
    // 导入
    handleImport() {
      this.importDialogVisible = true
    },
    // 问题清单导入
    handleIssuesListImport(row) {
      this.issuesListImportDialogVisible = true
      // 复制行数据但排除 applicationId
      const { applicationId, ...rowWithoutId } = row;
      this.issuesListFormData = {
        ...rowWithoutId,
        category: '',
        controlPoint: '',
        evaluationItem: '',
        resultRecord: '',
        complianceStatus: ''
      }
    },
    // 测评报告导入
    handleEvaluationReportImport(row) {
      this.evaluationReportImportDialogVisible = true
      // 复制行数据但排除 applicationId
      const { applicationId, ...rowWithoutId } = row;
      this.evaluationListFormData = {
        ...rowWithoutId,
        category: '',
        controlPoint: '',
        evaluationItem: '',
        resultRecord: '',
        complianceStatus: ''
      }
    },
    // 新增导入成功处理逻辑
    handleImportSuccess(response, file) {
      if (response.data && response.data.length > 0) {
        const importedData = response.data[0] // 取第一条数据
        console.log(importedData)
        this.formData = {
          ...this.formData,
          systemOwnerOrg: importedData.systemOwnerOrg || '',
          systemName: importedData.systemName || '',
          businessType: importedData.businessType || '',
          filingNumber: importedData.filingNumber || '',
          classificationLevel: importedData.classificationLevel || '',
          evaluationResult: importedData.evaluationResult || '',
          evaluationTime: importedData.evaluationTime || '',
          plannedEvaluationTime: importedData.plannedEvaluationTime || '',
          evaluationOrganization: importedData.evaluationOrganization || '',
          evaluationStatus: importedData.evaluationStatus || '',
          systemStatus: importedData.systemStatus || '',
          // 可选字段
          memberUnit: importedData.memberUnit || '',
          systemShortName: importedData.systemShortName || '',
          networkBelonging: importedData.networkBelonging || '',
          category: importedData.category || '',
          controlPoint: importedData.controlPoint || '',
          evaluationItem: importedData.evaluationItem || '',
          resultRecord: importedData.resultRecord || '',
          complianceStatus: importedData.complianceStatus || ''
        }
        console.log(response)
        // 自动打开新增对话框
        this.dialogType = 'add'
        this.dialogVisible = true
        this.$message.success('导入成功，数据已填充')
      } else {
        this.$message.error('导入失败，文件内容为空或格式不正确')
      }
      this.importDialogVisible = false
      this.importFile = null // 清空已上传文件
    },

    // 修改上传配置，确保只上传一个文件
    beforeImportUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          file.type === 'application/vnd.ms-excel'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isExcel) {
        this.$message.error('只能上传 Excel 文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('文件大小不能超过 2MB!')
        return false
      }

      // 限制只能上传一个文件
      if (this.importFile) {
        this.$message.error('只能上传一个文件!')
        return false
      }
      this.importFile = file
      return true
    },

    // 修改上传配置，确保只上传一个文件
    beforePdfUpload(file) {
      const isPdf = file.type === 'application/pdf'
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isPdf) {
        this.$message.error('只能上传 PDF 文件!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('文件大小不能超过 2MB!')
        return false
      }

      // 限制只能上传一个文件
      if (this.importFile) {
        this.$message.error('只能上传一个文件!')
        return false
      }
      this.importFile = file
      return true
    },
    // 新增数据时清空导入的文件
    handleAdd() {
      this.dialogType = 'add'
      this.formData = this.$options.data().formData
      this.importFile = null
      this.dialogVisible = true
    },


    // 问题清单导入成功处理逻辑
    handleIssuesListImportSuccess(response, file) {
      if (response.data && response.data.length > 0) {
        const importedData = response.data[0] // 取第一条数据
        this.issuesListFormData = {
          ...this.issuesListFormData,
          category: importedData.category || '',
          controlPoint: importedData.controlPoint || '',
          evaluationItem: importedData.evaluationItem || '',
          resultRecord: importedData.resultRecord || '',
          complianceStatus: importedData.complianceStatus || ''
        }
        console.log(response)
        // 自动打开问题清单导入识别表单对话框
        this.issuesListFormDialogVisible = true
        this.$message.success('导入成功，数据已填充')
      } else {
        this.$message.error('导入失败，文件内容为空或格式不正确')
      }
      this.issuesListImportDialogVisible = false
      this.importFile = null // 清空已上传文件
    },

    // 提交问题清单表单
    submitIssuesListForm() {
      this.$refs.issuesListForm.validate(async valid => {
        if (!valid) return

        try {
          securityAssessment.addSysProtect(this.issuesListFormData)
          console.log('问题清单', this.issuesListFormData)
          this.$message.success('问题清单导入成功')
          this.issuesListFormDialogVisible = false
          this.getList()
        } catch (error) {
          this.$message.error('操作失败')
        }
      })
    },
    // 测评报告导入成功处理逻辑
    handleEvaluationListImportSuccess(response, file) {
      if (response.data && response.data.length > 0) {
        const importedData = response.data[0] // 取第一条数据
        this.evaluationListFormData = {
          ...this.evaluationListFormData,
          systemOwnerOrg: importedData.systemOwnerOrg || '',
          memberUnit: importedData.memberUnit || '',
          systemName: importedData.systemName || '',
          systemShortName: importedData.systemShortName || '',
          networkBelonging: importedData.networkBelonging || '',
          businessType: importedData.businessType || '',
          filingNumber: importedData.filingNumber || '',
          classificationLevel: importedData.classificationLevel || '',
          evaluationTime: importedData.evaluationTime || '',
          evaluationResult: importedData.evaluationResult || '',
          evaluationOrganization: importedData.evaluationOrganization || '',
          evaluationStatus: importedData.evaluationStatus || '',
          systemStatus: importedData.systemStatus || '',
          category: importedData.category || '',
          controlPoint: importedData.controlPoint || '',
          evaluationItem: importedData.evaluationItem || '',
          complianceStatus: importedData.complianceStatus || ''
        }
        console.log(response)
        // 自动打开问题清单导入识别表单对话框
        this.evaluationListFormDialogVisible = true
        this.$message.success('导入成功，数据已填充')
      } else {
        this.$message.error('导入失败，文件内容为空或格式不正确')
      }
      this.evaluationReportImportDialogVisible = false
      this.importFile = null // 清空已上传文件
    },
    // 提交测评报告表单
    submitEvaluationListForm() {
      this.$refs.evaluationListForm.validate(async valid => {
        if (!valid) return

        try {
          securityAssessment.addSysProtect(this.evaluationListFormData)
          this.$message.success('测评报告导入成功')
          this.evaluationListFormDialogVisible = false
          this.getList()
        } catch (error) {
          this.$message.error('操作失败')
        }
      })
    },

    handleExport() {
      this.exportLoading = true
      this.$message.info('正在准备导出数据，请稍候...')

      try {
        const params = new URLSearchParams()
        // 只添加非空且有值的查询条件
        Object.entries(this.queryForm).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value)
          }
        })

        // 移除分页参数，导出全部数据
        params.append('pageNo', 1)
        params.append('pageSize', this.total)

        const exportUrl = `${process.env.VUE_APP_BASE_API}/sysProtect/export?${params.toString()}`

        // 创建隐藏的iframe实现下载
        const iframe = document.createElement('iframe')
        iframe.style.display = 'none'
        iframe.src = exportUrl
        document.body.appendChild(iframe)

        // 设置超时检查
        const timeout = setTimeout(() => {
          this.$message.warning('导出时间较长，请耐心等待...')
        }, 5000)

        // 监听iframe加载完成
        iframe.onload = () => {
          clearTimeout(timeout)
          this.$message.success('全部数据导出成功，文件已开始下载')
          document.body.removeChild(iframe)
          this.exportLoading = false
        }

        iframe.onerror = () => {
          clearTimeout(timeout)
          this.$message.error('导出失败，请检查网络或联系管理员')
          document.body.removeChild(iframe)
          this.exportLoading = false
        }
      } catch (error) {
        this.exportLoading = false
        if (error.response) {
          // 处理HTTP错误
          const errorMsg = error.response.data?.message || '导出服务异常'
          this.$message.error(`导出失败: ${errorMsg}`)
        } else {
          // 处理其他错误
          this.$message.error(`导出失败: ${error.message || '未知错误'}`)
        }
      }
    }
  }

}

</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f0f2f5;
}

.box-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .card-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }
}

.search-form {
  padding: 10px 0 0 0;
  .el-form-item {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
  }
  .el-input, .el-select, .el-date-picker, .el-date-editor {
    width: 100%;
    height: 36px !important;
    line-height: 36px !important;
    display: block !important;
    vertical-align: middle !important;
    box-sizing: border-box;
  }
  .el-input__inner {
    height: 36px !important;
    line-height: 36px !important;
    box-sizing: border-box;
    display: block !important;
    vertical-align: middle !important;
  }
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    height: 36px !important;
    line-height: 36px !important;
    box-sizing: border-box;
    display: block !important;
    vertical-align: middle !important;
  }
}

.pagination-container {
  padding: 15px;
  background: white;
  border-top: 1px solid #e8e8e8;
  text-align: right;
}

:deep(.el-pagination) {
  padding: 0;
  margin: 0;
  font-weight: normal;
}

.upload-demo {
  text-align: center;

  .el-upload {
    width: 100%;
  }

  .el-upload-dragger {
    width: 100%;
  }

  .el-upload__tip {
    color: #909399;
    font-size: 12px;
    margin-top: 10px;
  }
}
</style>
