import request from '@/utils/request'

export function getCryptoEvaluationList(query) {
  return request({
    url: '/cryptoEvaluation/list',
    method: 'get',
    params: query
  })
}

export function getStatistics(query) {
  return request({
    url: '/cryptoEvaluation/statistics',
    method: 'get',
    params: query
  })
}

export function getCryptoEvaluationById(id) {
  return request({
    url: `/cryptoEvaluation/${id}`,
    method: 'get'
  })
}
// 新增
export function addCryptoEvaluation(data) {
  return request({
    url: '/cryptoEvaluation/add',
    method: 'post',
    data
  })
}

// 修改
export function updateCryptoEvaluation(data) {
  return request({
    url: '/cryptoEvaluation/update',
    method: 'put',
    data
  })
}

// 删除
export function deleteCryptoEvaluation(id) {
  return request({
    url: `/cryptoEvaluation/${id}`,
    method: 'delete'
  })
}

export function exportExcel(query) {
  return request({
    url: '/cryptoEvaluation/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  }).then(response => {
    return response
  }).catch(error => {
    console.error('错误详情：', error.response || error.message)
    throw error
  })
}

// 获取密评详情
export function getCryptoEvaluationDetail(id) {
  return request({
    url: `/cryptoEvaluation/detail/${id}`,
    method: 'get'
  })
}

