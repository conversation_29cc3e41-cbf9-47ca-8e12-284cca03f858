<template>
    <el-card>
      <h2>单位安全状态地图</h2>
      <v-chart :options="mapOptions" style="width: 100%; height: 600px;"></v-chart>
    </el-card>
  </template>
  
  <script>
    // main.js 或其他初始化文件
// import * as echarts from 'echarts';

// // 注册地图数据
// const chinaMapJson = require('!!raw-loader!./public/china.json');
// echarts.registerMap('china', chinaMapJson);

// 使用 ECharts

  
  export default {
    name: 'ChinaMap',
    props: ['units'],
    computed: {
      mapOptions() {
        return {
          tooltip: {
            trigger: 'item',
            formatter: '{b}'
          },
          series: [
            {
              name: '单位分布',
              type: 'scatter',
              coordinateSystem: 'geo',
              data: this.units.map(unit => ({
                name: unit.name,
                value: [...unit.location, unit.vulnerabilities],
                itemStyle: {
                  color: this.getColor(unit.vulnerabilities)
                }
              })),
              symbolSize: 20,
              label: {
                show: false
              },
              emphasis: {
                label: {
                  show: true
                }
              }
            },
            {
              type: 'map',
              map: 'china',
              roam: true,
              itemStyle: {
                emphasis: { label: { show: true } }
              }
            }
          ],
          geo: {
            map: 'china',
            roam: true,
            label: {
              emphasis: {
                show: true
              }
            },
            itemStyle: {
              normal: {
                borderColor: '#404a59',
                areaColor: '#323c48'
              },
              emphasis: {
                areaColor: '#2a333d'
              }
            }
          }
        };
      }
    },
    methods: {
      getColor(vulnerabilities) {
        if (vulnerabilities >= 3) return 'red';
        if (vulnerabilities === 2) return 'orange';
        return 'green';
      }
    }
  };
  </script>
  
  <style scoped>
  .el-card {
    height: 100%;
  }
  </style>
  