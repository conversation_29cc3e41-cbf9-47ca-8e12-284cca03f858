<template>
  <div class="app-container">
    <div class="custom-tree-container">
      <div class="block">
        <p>组织管理</p>
        <el-input
          placeholder="搜索节点"
          v-model="searchQuery"
          @input="handleSearch"
          style="margin-bottom: 10px;"
        ></el-input>
        <el-button type="primary" size="small" @click="addRootNode">
          新公司
        </el-button>
        <el-tree
          ref="tree"
          :data="filteredData"
          node-key="id"
          default-expand-all
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span>{{ node.label }}</span>
            <span>
              <el-button
                v-if="!node.children || node.children.length === 0"
                type="text"
                size="mini"
                @click.stop="append(data)"
              >
                <i class="el-icon-plus"></i>
              </el-button>
              <el-button
                type="text"
                size="mini"
                @click.stop="remove(node, data)"
              >
                <i class="el-icon-minus"></i>
              </el-button>
            </span>
          </span>
        </el-tree>
      </div>
    </div>

    <div class="form-container">
      <h4>组织详情</h4>
      <el-form :model="selectedNode" label-width="120px">
        <el-form-item label="邮编">
          <el-input v-model="selectedNode.postalCode"></el-input>
        </el-form-item>
        <el-form-item label="名称">
          <el-input v-model="selectedNode.label"></el-input>
        </el-form-item>
        <el-form-item label="地址">
          <el-input v-model="selectedNode.address"></el-input>
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input v-model="selectedNode.phoneNumber"></el-input>
        </el-form-item>
        <el-form-item label="上一级名称">
          <el-input v-model="parentNodeLabel" disabled></el-input>
        </el-form-item>
        <el-form-item label="下一级名称">
          <el-input v-model="childNodeLabels" disabled></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="updateNode">保存</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>


<script>
let id = 1000;

export default {
  data() {
    return {
      data: this.initializeData(),
      searchQuery: '',
      filteredData: [], // 新增
      selectedNode: {
        id: '',
        label: '',
        postalCode: '',
        address: '',
        phoneNumber: ''
      },
      parentNodeLabel: '',
      childNodeLabels: ''
    };
  },

  created() {
    this.filteredData = this.data; // 初始化时显示所有数据
  },

  methods: {
    initializeData() {
      return [
        {
          id: 1,
          label: '总公司 1',
          postalCode: '100000',
          address: '北京市',
          phoneNumber: '12345678901',
          children: [
            {
              id: 4,
              label: '一级子公司 1',
              postalCode: '100001',
              address: '北京市海淀区',
              phoneNumber: '12345678902',
              children: [
                {
                  id: 9,
                  label: '二级子公司 1',
                  postalCode: '100002',
                  address: '北京市海淀区中关村',
                  phoneNumber: '12345678903'
                },
                {
                  id: 10,
                  label: '二级子公司 2',
                  postalCode: '100003',
                  address: '北京市海淀区五道口',
                  phoneNumber: '12345678904'
                }
              ]
            }
          ]
        },
        {
          id: 2,
          label: '总公司 2',
          postalCode: '100004',
          address: '上海市',
          phoneNumber: '12345678905',
          children: [
            {
              id: 5,
              label: '一级子公司 2-1',
              postalCode: '100005',
              address: '上海市浦东新区',
              phoneNumber: '12345678906'
            },
            {
              id: 6,
              label: '一级子公司 2-2',
              postalCode: '100006',
              address: '上海市徐汇区',
              phoneNumber: '12345678907'
            }
          ]
        },
        {
          id: 3,
          label: '总公司 3',
          postalCode: '100007',
          address: '广州市',
          phoneNumber: '12345678908',
          children: [
            {
              id: 7,
              label: '一级子公司 3-1',
              postalCode: '100008',
              address: '广州市天河区',
              phoneNumber: '12345678909'
            },
            {
              id: 8,
              label: '一级子公司 3-2',
              postalCode: '100009',
              address: '广州市海珠区',
              phoneNumber: '12345678910'
            }
          ]
        }
      ];
    },

    handleSearch() {
      this.filteredData = this.search(this.data, this.searchQuery);
    },

    search(data, query) {
      let result = [];

      data.forEach(item => {
        if (item.label.includes(query)) {
          result.push(item);
        } else if (item.children) {
          let filteredChildren = this.search(item.children, query);
          if (filteredChildren.length) {
            result.push({
              ...item,
              children: filteredChildren
            });
          }
        }
      });

      return result;
    },

    addRootNode() {
      const newRootNode = {
        id: id++,
        label: '新公司',
        postalCode: '100010',
        address: '地址',
        phoneNumber: '12345678911',
        children: []
      };
      this.data.push(newRootNode);
      this.handleSearch(); // 添加新节点后重新过滤
    },

    append(data) {
      const newChild = {
        id: id++,
        label: 'testtest',
        postalCode: '100010',
        address: '新地址',
        phoneNumber: '12345678911',
        children: []
      };
      if (!data.children) {
        this.$set(data, 'children', []);
      }
      data.children.push(newChild);
      this.handleSearch(); // 添加子节点后重新过滤
    },

    remove(node, data) {
      const parent = node.parent;
      const children = parent.data.children || parent.data;
      const index = children.findIndex(d => d.id === data.id);
      children.splice(index, 1);
      this.handleSearch(); // 删除节点后重新过滤
    },

    handleNodeClick(node) {
      // 获取当前选中节点的相关信息
      this.selectedNode = {
        id: node.id,
        label: node.label,
        postalCode: node.postalCode,
        address: node.address,
        phoneNumber: node.phoneNumber
      };

      // 使用 $refs.tree.getNode(node.id) 获取完整的节点信息
      const fullNode = this.$refs.tree.getNode(node.id);

      // 获取父节点信息
      const parentNode = fullNode.parent ? fullNode.parent.data : null;
      this.parentNodeLabel = parentNode ? parentNode.label : '无';

      // 获取子节点信息
      this.childNodeLabels = node.children
        ? node.children.map(child => child.label).join(', ')
        : '无';
    },

    updateNode() {
      const updateNodeInData = (data) => {
        for (let i = 0; i < data.length; i++) {
          if (data[i].id === this.selectedNode.id) {
            data[i].label = this.selectedNode.label;
            data[i].postalCode = this.selectedNode.postalCode;
            data[i].address = this.selectedNode.address;
            data[i].phoneNumber = this.selectedNode.phoneNumber;
            break;
          }
          if (data[i].children) {
            updateNodeInData(data[i].children);
          }
        }
      };

      updateNodeInData(this.data);

      // 更新父节点名称
      const parentNode = this.$refs.tree.getNode(this.selectedNode.id).parent;
      this.parentNodeLabel = parentNode ? parentNode.data.label : '无';

      // 更新子节点名称
      this.childNodeLabels = this.selectedNode.children
        ? this.selectedNode.children.map(child => child.label).join(', ')
        : '无';

      // 特别处理根节点
      if (!this.selectedNode.parent) {
        this.parentNodeLabel = '无';
      }

      // 刷新树形结构以显示最新数据
      // Element UI 的 el-tree 没有提供 updateNode 方法，这里模拟刷新
      this.$nextTick(() => {
        this.$refs.tree.filter('', true); // 这里使用 filter 方法来模拟刷新
      });

      this.handleSearch(); // 更新节点后重新过滤
    }
  }
};
</script>


<style scoped>
.app-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.form-container {
  width: 40%;
  padding: 10px;
}

.custom-tree-container {
  width: 50%;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  padding-right: 8px;
}
</style>