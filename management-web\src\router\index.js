import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
 roles: ['admin','editor']    control the page roles (you can set multiple roles)
 title: 'title'               the name show in sidebar and breadcrumb (recommend set)
 icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
 breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
 activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
 }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },

  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },

  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [{
      path: 'dashboard',
      name: 'Dashboard',
      component: () => import('@/views/dashboard/index'),
      meta: { title: '首页', icon: 'dashboard',affix: true }
    }]
  },

  {
    path: '/systemdata',
    component: Layout,
    redirect: '/systemdata/usermanagement',
    name: 'systemdata',
    meta: {
      title: '系统数据管理',
      icon: 'table'
    },
    children: [
      {
        path: 'orgmanagement',
        component: () => import('@/views/systemdata/deptManagement'),
        name: 'OrgManagement',
        meta: { title: '组织管理' }
      },
      {
        path: 'usermanage',
        component: () => import('@/views/systemdata/userManage'),
        name: 'UserManage',
        meta: { title: '用户管理' }
      },
      {
        path: 'sysdeploymam',
        component: () => import('@/views/systemdata/sysdeploymam'),
        name: 'SysDeploymam',
        meta: { title: '系统配置管理' }
      },
      {
        path: 'datadicitionary',
        component: () => import('@/views/systemdata/datadicitionary'),
        name: 'DataDicitionary',
        meta: { title: '数据字典' }
      }
    ]
  },
  {
    path: '/excel',
    component: Layout,
    redirect: '/excel/export-excel',
    name: 'Excel',
    meta: {
      title: '系统数据处理',
      icon: 'excel'
    },
    children: [
      {
        path: 'export-data',
        component: () => import('@/views/sysdataprocess/export-data'),
        name: 'ExportData',
        meta: { title: '数据导出' }
      },

      {
        path: 'upload-data',
        component: () => import('@/views/sysdataprocess/upload-data'),
        name: 'UploadData',
        meta: { title: '数据导入' }
      }
    ]
  },
  {
    path: '/taskmanagement',
    component: Layout,
    // 移除 name 属性
    meta: { title: '系统任务管理', icon: 'el-icon-s-help' },
    children: [
      {
        path: '',
        name: 'TaskManagement', // 名称移到默认子路由
        component: () => import('@/views/taskmanagement/manage'),
        meta: { title: '系统任务管理', icon: 'el-icon-s-help' }
      }
    ]
  },
  {
    path: '/sys-information-management',
    component: Layout,
    redirect: '/sys-information-management/bigdata-show',
    name: 'sys-information-management',
    meta: {
      title: '系统信息管理',
      icon: 'excel'
    },
    children: [
      {
        path: 'bigdata-show',
        component: () => import('@/views/sys-information-management/bigdata-show'),
        name: 'bigdata-show',
        meta: { title: '大数据展示', icon: 'pdf' }
      },
      {
        path: '/child-system',
        component: () => import('@/views/sys-information-management/child-system'),
        name: 'chile-system',
        meta: { title: '子系统管理', icon: 'nested' }
      },
    ]
  },
  {
    path: '/security-assessment',
    component: Layout,
    redirect: '/security-assessment/management',
    name: 'SecurityAssessment',
    meta: {
      title: '等保测评管理',
      icon: 'el-icon-s-check'
    },
    children: [
      {
        path: 'detail',
        component: () => import('@/views/security-assessment/detail'),
        name: 'SecurityAssessmentDetail',
        meta: { title: '等保详情' }
      },
      {
        path: 'management',
        component: () => import('@/views/security-assessment/management'),
        name: 'SecurityAssessmentManagement',
        meta: { title: '等保管理' }
      },
      {
        path: 'ledger',
        component: () => import('@/views/security-assessment/ledger'),
        name: 'SecurityAssessmentLedger',
        meta: { title: '等保概览' }
      },
      {
        path: 'table',
        component: () => import('@/views/security-assessment/table'),
        name: 'SecurityAssessmentTable',
        meta: { title: 'test' }
      }
    ]
  },
  // 新增密评信息管理路由，与等保测评管理并列
  {
    path: '/crypto_evaluation',
    component: Layout,
    redirect: '/crypto_evaluation/ledger',
    name: 'CryptoEvaluation',
    meta: { title: '密评信息管理', icon: 'el-icon-key' },
    children: [
      {
        path: 'ledger',
        component: () => import('@/views/crypto_evaluation/ledger.vue'),
        name: 'CryptoEvaluationLedger',
        meta: { title: '密评概览' }
      },
      {
        path: 'detail',
        component: () => import('@/views/crypto_evaluation/detail.vue'),
        name: 'CryptoEvaluationDetail',
        meta: { title: '密评详情' }
      },
      {
        path: 'management',
        component: () => import('@/views/crypto_evaluation/management.vue'),
        name: 'CryptoEvaluationManagement',
        meta: { title: '密评管理' }
      }
    ]
  },
  // 新增系统漏洞管理路由，与密评信息管理并列
  {
    path: '/vulnerability-management',
    component: Layout,
    redirect: '/vulnerability-management/list',
    name: 'VulnerabilityManagement',
    meta: { title: '系统漏洞管理', icon: 'el-icon-warning' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/vulnerability-management/vulnList'),
        name: 'VulnerabilityList',
        meta: { title: '漏洞列表' }
      },
      {
        path: 'overview',
        component: () => import('@/views/vulnerability-management/vulnledger'),
        name: 'VulnerabilityOverview',
        meta: { title: '漏洞概览' }
      },
      {
        path: 'detail/:id',
        component: () => import('@/views/vulnerability-management/vulndetail'),
        name: 'VulnerabilityDetail',
        meta: { title: '漏洞详情' }
      }
    ]
  },
  {
    path: '/sys-security-assessment',
    component: Layout,
    redirect: '/sys-security-assessment/subsystem-analysis',
    name: 'sys-security-assessment',
    meta: {
      title: '系统安全性评估',
      icon: 'el-icon-s-opportunity'
    },
    children: [
      {
        path: 'subsystem-analysis',
        component: () => import('@/views/sys-security-assessment/subsystem-analysis'),
        name: 'SubsystemAnalysis',
        meta: { title: '所属单位安全性分析' }
      },
      {
        path: 'security-analysis',
        component: () => import('@/views/sys-security-assessment/security-analysis'),
        name: 'SecurityAnalysis',
        meta: { title: '安全分析' }
      }
    ]
  },
  // 添加日志操作路由
  {
    path: '/log-operation',
    component: Layout,
    children: [
      {
        path: 'index',
        name: 'LogOperation',
        component: () => import('@/views/log-operation/index'),
        meta: { title: '日志操作', icon: 'el-icon-document' }
      }
    ]
  },
  {
    path: '/example',
    component: Layout,
    redirect: '/example/table',
    name: 'Example',
    meta: { title: 'Example', icon: 'el-icon-s-help' },
    children: [
      {
        path: 'table',
        name: 'Table',
        component: () => import('@/views/table/index'),
        meta: { title: 'Table', icon: 'table' }
      },
      {
        path: 'tree',
        name: 'Tree',
        component: () => import('@/views/tree/index'),
        meta: { title: 'Tree', icon: 'tree' }
      }
    ]
  },

  {
    path: '/form',
    component: Layout,
    children: [
      {
        path: 'index',
        name: 'Form',
        component: () => import('@/views/form/index'),
        meta: { title: 'Form', icon: 'form' }
      }
    ]
  },

  {
    path: '/nested',
    component: Layout,
    redirect: '/nested/menu1',
    name: 'Nested',
    meta: {
      title: 'Nested',
      icon: 'nested'
    },
    children: [
      {
        path: 'menu1',
        component: () => import('@/views/nested/menu1/index'), // Parent router-view
        name: 'Menu1',
        meta: { title: 'Menu1' },
        children: [
          {
            path: 'menu1-1',
            component: () => import('@/views/nested/menu1/menu1-1'),
            name: 'Menu1-1',
            meta: { title: 'Menu1-1' }
          },
          {
            path: 'menu1-2',
            component: () => import('@/views/nested/menu1/menu1-2'),
            name: 'Menu1-2',
            meta: { title: 'Menu1-2' },
            children: [
              {
                path: 'menu1-2-1',
                component: () => import('@/views/nested/menu1/menu1-2/menu1-2-1'),
                name: 'Menu1-2-1',
                meta: { title: 'Menu1-2-1' }
              },
              {
                path: 'menu1-2-2',
                component: () => import('@/views/nested/menu1/menu1-2/menu1-2-2'),
                name: 'Menu1-2-2',
                meta: { title: 'Menu1-2-2' }
              }
            ]
          },
          {
            path: 'menu1-3',
            component: () => import('@/views/nested/menu1/menu1-3'),
            name: 'Menu1-3',
            meta: { title: 'Menu1-3' }
          }
        ]
      },
      {
        path: 'menu2',
        component: () => import('@/views/nested/menu2/index'),
        name: 'Menu2',
        meta: { title: 'menu2' }
      }
    ]
  },

  {
    path: 'external-link',
    component: Layout,
    children: [
      {
        path: 'https://panjiachen.github.io/vue-element-admin-site/#/',
        meta: { title: 'External Link', icon: 'link' }
      }
    ]
  },

  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
