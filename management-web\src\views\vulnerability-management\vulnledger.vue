<template>
  <div class="bigscreen-container fullscreen-root-fix">
    <div class="canvas" style="opacity: .2">
      <!-- 背景粒子效果（保留容器占位） -->
    </div>
    <div class="loading" v-show="false">
      <div class="loadbox"> 页面加载中...</div>
    </div>
    <div class="head">
      <h1>{{ pageTitle }}</h1>
      <div class="weather">
        <span>{{ nowTime }}</span>
      </div>
    </div>
    <div class="mainbox">
      <ul class="clearfix">
        <li>
          <div class="boxall" style="height: 3.2rem">
            <div class="alltitle">{{ sections.echart1.title }}</div>
            <div class="allnav" ref="echart1"></div>
            <div class="boxfoot"></div>
          </div>
          <div class="boxall" style="height: 3.2rem">
            <div class="alltitle">{{ sections.echart2.title }}</div>
            <div class="allnav" ref="echart2"></div>
            <div class="boxfoot"></div>
          </div>
          <div class="boxall" style="height: 3.2rem">
            <div style="height:100%; width: 100%; display: flex">
              <div class="sy" ref="fb1"></div>
              <div class="sy" ref="fb2"></div>
              <div class="sy" ref="fb3"></div>
            </div>
            <div class="boxfoot"></div>
          </div>
        </li>
        <li>
          <div class="bar">
            <div class="barbox">
              <ul class="clearfix">
                <li class="pulll_left counter">{{ counters.counter }}</li>
                <li class="pulll_left counter">{{ counters.counter2 }}</li>
              </ul>
            </div>
            <div class="barbox2">
              <ul class="clearfix">
                <li class="pulll_left">指标一</li>
                <li class="pulll_left">指标二</li>
              </ul>
            </div>
          </div>
          <div class="map">
            <div class="map1"><img :src="assets.lbx" /></div>
            <div class="map2"><img :src="assets.jt" /></div>
            <div class="map3"><img :src="assets.map" /></div>
            <div class="map4" ref="map"></div>
          </div>
        </li>
        <li>
          <div class="boxall" style="height:3.4rem">
            <div class="alltitle">{{ sections.echart4.title }}</div>
            <div class="allnav" ref="echart4"></div>
            <div class="boxfoot"></div>
          </div>
          <div class="boxall" style="height: 3.2rem">
            <div class="alltitle">{{ sections.echart5.title }}</div>
            <div class="allnav" ref="echart5"></div>
            <div class="boxfoot"></div>
          </div>
          <div class="boxall" style="height: 3rem">
            <div class="alltitle">{{ sections.echart6.title }}</div>
            <div class="allnav" ref="echart6"></div>
            <div class="boxfoot"></div>
          </div>
        </li>
      </ul>
    </div>
    <div class="back"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'VulnLedgerBigScreen',
  data() {
    return {
      pageTitle: '漏洞概览',
      nowTime: '',
      counters: { counter: 128, counter2: 56 },
      assets: {
        lbx: require('@/../src/assets/big_screen/lbx.png'),
        jt: require('@/../src/assets/big_screen/jt.png'),
        map: require('@/../src/assets/big_screen/map.png')
      },
      sections: {
        echart1: { title: '图表一' },
        echart2: { title: '图表二' },
        echart4: { title: '趋势图' },
        echart5: { title: '图表三' },
        echart6: { title: '占比图' }
      },
      charts: {},
      originalFontSize: '' // 存储原始的font-size
    }
  },
  mounted() {
    // 保存原始的font-size
    this.originalFontSize = document.documentElement.style.fontSize || getComputedStyle(document.documentElement).fontSize
    this.updateNowTime()
    this._timer = setInterval(this.updateNowTime, 1000)
    this.setRootRem()
    this.initCharts()
    window.addEventListener('resize', () => {
      this.setRootRem()
      this.handleResize()
    })
  },
  beforeDestroy() {
    clearInterval(this._timer)
    window.removeEventListener('resize', this.handleResize)
    Object.values(this.charts).forEach(c => c && c.dispose && c.dispose())
    // 恢复原始的font-size
    if (this.originalFontSize) {
      document.documentElement.style.fontSize = this.originalFontSize
    } else {
      // 如果没有保存原始值，则清除内联样式，让CSS默认值生效
      document.documentElement.style.fontSize = ''
    }
  },
  methods: {
    setRootRem() {
      const width = window.innerWidth || document.documentElement.clientWidth
      const base = Math.max(1280, Math.min(2560, width))
      document.documentElement.style.fontSize = (base / 20) + 'px'
    },
    updateNowTime() {
      const dt = new Date()
      const pad = n => String(n).padStart(2, '0')
      this.nowTime = `${dt.getFullYear()}年${pad(dt.getMonth()+1)}月${pad(dt.getDate())}日-${pad(dt.getHours())}时${pad(dt.getMinutes())}分${pad(dt.getSeconds())}秒`
    },
    initCharts() {
      // 示例数据
      const demoXAxis = ['一月','二月','三月','四月','五月','六月','七月']
      const demoBarA = [23, 45, 18, 39, 52, 27, 61]
      const demoBarB = [12, 28, 33, 21, 40, 30, 25]
      const demoPie1 = [
        { value: 35, name: '高危' },
        { value: 45, name: '中危' },
        { value: 20, name: '低危' }
      ]
      const demoPie2 = [
        { value: 30, name: '已修复' },
        { value: 50, name: '整改中' },
        { value: 20, name: '未处理' }
      ]
      const demoPie3 = [
        { value: 25, name: 'APP' },
        { value: 35, name: 'Web' },
        { value: 40, name: '中间件' }
      ]

      const optionBarBlue = {
        tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
        grid: { left: '0%', right: '0%', bottom: '4%', top: '10px', containLabel: true },
        xAxis: [{ type: 'category', data: demoXAxis, axisLine: { show: true }, axisTick: { show: false }, axisLabel: { show: true } }],
        yAxis: [{ type: 'value', axisLabel: { show: true }, axisTick: { show: false }, axisLine: { show: true }, splitLine: { show: true } }],
        series: [{ type: 'bar', data: demoBarA, barWidth: '35%' }]
      }
      const optionBarGreen = {
        tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
        grid: { left: '0%', right: '0%', bottom: '4%', top: '10px', containLabel: true },
        xAxis: [{ type: 'category', data: demoXAxis, axisLine: { show: true }, axisTick: { show: false }, axisLabel: { show: true } }],
        yAxis: [{ type: 'value', axisLabel: { show: true }, axisTick: { show: false }, axisLine: { show: true }, splitLine: { show: true } }],
        series: [{ type: 'bar', data: demoBarB, barWidth: '35%' }]
      }
      const optionPie = (data) => ({
        tooltip: { trigger: 'item' },
        legend: { top: '70%', itemWidth: 10, itemHeight: 10 },
        series: [{ type: 'pie', center: ['50%', '42%'], radius: ['40%', '60%'], label: { show: false }, labelLine: { show: false }, data }]
      })
      const optionLine = {
        tooltip: { trigger: 'axis' },
        legend: { top: '0%', data: ['发现数','修复数'] },
        grid: { left: '10', right: '10', bottom: '10', top: '30', containLabel: true },
        xAxis: [{ type: 'category', data: demoXAxis }],
        yAxis: [{ type: 'value' }],
        series: [
          { name: '发现数', type: 'line', smooth: true, data: demoBarA },
          { name: '修复数', type: 'line', smooth: true, data: demoBarB }
        ]
      }
      const optionRings = {
        tooltip: { show: true, formatter: '{a} : {c}' },
        legend: { bottom: '3%', data: ['覆盖率','完成率','通过率'] },
        series: [
          { name: '覆盖率', type: 'pie', clockWise: false, center: ['50%','42%'], radius: ['35%','45%'], hoverAnimation: false, data: [{ value: 70, name: '覆盖率' }, { value: 30, name: 'invisible' }] },
          { name: '完成率', type: 'pie', clockWise: false, center: ['50%','42%'], radius: ['48%','58%'], hoverAnimation: false, data: [{ value: 55, name: '完成率' }, { value: 45, name: 'invisible' }] },
          { name: '通过率', type: 'pie', clockWise: false, center: ['50%','42%'], radius: ['61%','71%'], hoverAnimation: false, data: [{ value: 40, name: '通过率' }, { value: 60, name: 'invisible' }] }
        ]
      }

      this.charts.e1 = echarts.init(this.$refs.echart1)
      this.charts.e2 = echarts.init(this.$refs.echart2)
      this.charts.fb1 = echarts.init(this.$refs.fb1)
      this.charts.fb2 = echarts.init(this.$refs.fb2)
      this.charts.fb3 = echarts.init(this.$refs.fb3)
      this.charts.map = echarts.init(this.$refs.map)
      this.charts.e4 = echarts.init(this.$refs.echart4)
      this.charts.e5 = echarts.init(this.$refs.echart5)
      this.charts.e6 = echarts.init(this.$refs.echart6)

      this.charts.e1.setOption(optionBarBlue)
      this.charts.e2.setOption(optionBarGreen)
      this.charts.fb1.setOption(optionPie(demoPie1))
      this.charts.fb2.setOption(optionPie(demoPie2))
      this.charts.fb3.setOption(optionPie(demoPie3))
      // 地图暂不加载 china 地图数据，保持空占位
      this.charts.map.setOption({ title: { text: '', show: false } })
      this.charts.e4.setOption(optionLine)
      this.charts.e5.setOption(optionBarBlue)
      this.charts.e6.setOption(optionRings)
    },
    handleResize() {
      Object.values(this.charts).forEach(c => c && c.resize && c.resize())
    }
  }
}
</script>

<style scoped lang="scss">
.fullscreen-root-fix {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}
.bigscreen-container {
  position: relative;
  height: 100vh;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: .1rem .1rem 0rem .1rem;
  background: #000d4a url(~@/../src/assets/big_screen/bg.jpg) center top;
  background-size: cover;
  color: #fff;
  font-size: .1rem;
}

.clearfix:after, .clearfix:before { display: table; content: " "; }
.clearfix:after { clear: both; }
.pulll_left{float:left;}
.pulll_right{float:right;}

.canvas{position: absolute; width:100%; left: 0; top: 0; height: 99%; z-index: 1;}
.allnav{height: calc(100% - 30px);}
.loading{position:fixed; left:0; top:0; font-size:18px; z-index:100000000;width:100%; height:100%; background:#1a1a1c; text-align:center;}
.loadbox{position:absolute; width:160px;height:150px; color: #aaa; left:50%; top:50%; margin-top:-100px; margin-left:-75px;}
.loadbox img{ margin:10px auto; display:block; width:40px;}

.head{ height:1.05rem; background: url(~@/../src/assets/big_screen/head_bg.png) no-repeat center center; background-size: 100% 100%; position: relative; z-index: 100;}
.head h1{ color:#fff; text-align: center; font-size: .4rem; line-height:.8rem;}
.weather{ position:absolute; right:.3rem; top:0; line-height: .75rem;}

.mainbox{ flex: 1; overflow: auto; -webkit-overflow-scrolling: touch; }
.mainbox>ul>li{ float: left; padding: 0 .1rem}
.mainbox>ul>li{ width: 30%}
.mainbox>ul>li:nth-child(2){ width: 40%;padding: 0}

.boxall{ border: 1px solid rgba(25,186,139,.17); padding:0 .2rem .4rem .15rem;  background: rgba(255,255,255,.04) url(~@/../src/assets/big_screen/line.png); background-size: 100% auto; position: relative; margin-bottom: .15rem; z-index: 10;}
.alltitle{ font-size:.2rem; color:#fff; text-align: center; line-height: .5rem;}
.boxfoot{ position:absolute; bottom: 0; width: 100%; left: 0;}

.bar{background:rgba(101,132,226,.1); padding: .15rem;}
.barbox li,.barbox2 li{ width:50%; text-align: center; position: relative; z-index: 100;}
.barbox{  border: 1px solid rgba(25,186,139,.17); position: relative;}
.barbox li{ font-size: .7rem; color: #ffeb7b; padding: .05rem 0; font-weight: bold;}
.barbox2 li{ font-size: .19rem; color:rgba(255,255,255,.7); padding-top: .1rem;}

.map{  position:relative; height: 7.2rem; z-index: 9;}
.map4{ width: 200%; height:7rem;  position: relative; left: -50%; top: 4%; margin-top: .2rem; z-index: 5;}
.map1,.map2,.map3{ position:absolute; opacity: .5}
.map1{ width:6.43rem; z-index: 2; top:.45rem; left: .7rem;  animation: myfirst2 15s infinite linear; }
.map2{ width:5.66rem; top:.85rem; left:1.2rem; z-index: 3; opacity: 0.2; animation: myfirst 10s infinite linear; }
.map3{ width:5.18rem; top:1.07rem; left: 1.4rem; z-index: 1; }

@keyframes myfirst2 { from { transform: rotate(0deg); } to { transform: rotate(359deg); } }
@keyframes myfirst { from { transform: rotate(0deg); } to { transform: rotate(-359deg); } }

.sy{ float:left; width: 33%; height:95%; margin-top: .25rem;}
</style>
