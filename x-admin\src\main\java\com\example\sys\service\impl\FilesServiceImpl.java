package com.example.sys.service.impl;

import com.example.sys.entity.Files;
import com.example.sys.mapper.FilesMapper;
import com.example.sys.service.IFilesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.UUID;

@Service
public class FilesServiceImpl extends ServiceImpl<FilesMapper, Files> implements IFilesService {
    private static final Logger logger = LoggerFactory.getLogger(FilesServiceImpl.class);

    @Override
    public String uploadFile(MultipartFile file) {
        if (file.isEmpty()) {
            return "上传失败，请选择文件";
        }
        // 获取文件原始名
        String fileName = file.getOriginalFilename();
        // 检查文件是否已存在
        if (isFileExists(fileName)) {
            return "文件已存在，请勿重复上传";
        }
        String suffix = fileName.substring(fileName.lastIndexOf("."));
        String filePath = "D:/upload/";
        // 获取文件类型
        String fileType = getFileType(suffix);

        File dest = new File(filePath + fileName);
        if (!dest.getParentFile().exists()) {
            dest.getParentFile().mkdirs();
        }

        try {
            file.transferTo(dest);
            Files fileEntity = new Files();
            // 使用文件原始名
            fileEntity.setFileName(fileName);
            fileEntity.setFilePath(filePath);
            fileEntity.setUploadTime(LocalDateTime.now());
            // 设置文件类型
            fileEntity.setFileType(fileType);

            this.save(fileEntity);
            return fileEntity.getFileId().toString(); // 返回 fileId

            // return "上传成功: " + fileName;
        } catch (IOException e) {
            e.printStackTrace();
            return "上传失败";
        }
    }

    /**
     * 检查文件是否已存在
     * @param fileName 文件名
     * @return 文件是否已存在
     */
    private boolean isFileExists(String fileName) {
        Long count = this.lambdaQuery().eq(Files::getFileName, fileName).count();
        return count > 0;
    }

    /**
     * 根据文件后缀名获取文件类型
     * @param suffix 文件后缀名
     * @return 文件类型
     */
    private String getFileType(String suffix) {
        switch (suffix.toLowerCase()) {
            case ".jpg":
            case ".jpeg":
            case ".png":
            case ".gif":
                return "image";
            case ".doc":
            case ".docx":
            case ".pdf":
            case ".txt":
                return "document";
            case ".mp3":
            case ".wav":
                return "audio";
            case ".mp4":
            case ".avi":
                return "video";
            default:
                return "unknown";
        }
    }

    @Override
    public String deleteFile(Integer fileId) {
        Files fileEntity = this.getById(fileId);
        if (fileEntity == null) {
            return "文件不存在";
        }
        // 删除本地文件
        String fullPath = fileEntity.getFilePath() + fileEntity.getFileName();
        File file = new File(fullPath);
        if (file.exists()) {
            if (!file.delete()) {
                logger.error("本地文件删除失败，文件路径: {}", fullPath);
                return "本地文件删除失败";
            }
        }
        // 逻辑删除（使用 MyBatis-Plus 的逻辑删除功能）
        try {
            if (this.removeById(fileId)) {
                logger.info("文件记录逻辑删除成功，文件ID: {}", fileId);
                return "删除成功";
            } else {
                logger.error("数据库更新失败，文件ID: {}", fileId);
                return "数据库更新失败";
            }
        } catch (Exception e) {
            logger.error("数据库更新时发生异常，文件ID: {}", fileId, e);
            return "数据库更新时发生异常";
        }
    }

    @Override
    public ResponseEntity<Resource> downloadFile(Integer fileId) {
        Files fileEntity = this.getById(fileId);
        if (fileEntity == null) {
            return ResponseEntity.notFound().build();
        }
        String filePath = fileEntity.getFilePath() + fileEntity.getFileName();
        File file = new File(filePath);
        if (!file.exists()) {
            return ResponseEntity.notFound().build();
        }
        Resource resource = new FileSystemResource(file);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileEntity.getFileName() + "\"");
        return ResponseEntity.ok()
               .headers(headers)
               .body(resource);
    }

    @Override
    public String getFileNameByFileId(Integer fileId) {
        Files file = getById(fileId);
        return file != null? file.getFileName() : "未知文件";
    }
}