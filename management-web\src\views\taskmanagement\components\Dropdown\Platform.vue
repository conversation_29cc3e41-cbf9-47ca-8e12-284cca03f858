<template>
  <el-dropdown :hide-on-click="false" :show-timeout="100" trigger="click">
    <el-button plain>
      <!-- 平台({{ platforms.length }}) -->
       平台
      <i class="el-icon-caret-bottom el-icon--right" />
    </el-button>
    <el-dropdown-menu slot="dropdown" class="no-border">
      <el-checkbox-group v-model="platforms" style="padding: 5px 15px;">
        <el-checkbox v-for="item in platformsOptions" :key="item.key" :label="item.key">
          {{ item.name }}
        </el-checkbox>
      </el-checkbox-group>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
// 定义一个Vue组件，用于展示和操作平台选项
export default {
  // 定义组件的属性，其中value是必需的，用于同步平台数据
  props: {
    value: {
      required: true, // 必须提供
      default: () => [], // 默认值为空数组
      type: Array // 类型为数组
    }
  },
  // 定义组件的数据，包括平台选项的数组
  data() {
    return {
      platformsOptions: [
        { key: 'a-platform', name: 'a-平台' },
        { key: 'b-platform', name: 'b-平台' },
        { key: 'c-platform', name: 'c-平台' }
      ]
    }
  },
  // 定义计算属性platforms，用于双向绑定平台数据
  computed: {
    platforms: {
      // 获取platforms的值，等于传入的value
      get() {
        return this.value
      },
      // 设置platforms的值，通过$emit触发input事件，将新值传回父组件
      set(val) {
        this.$emit('input', val)
      }
    }
  }
}
</script>