package com.example.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.example.sys.entity.Dept;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 部门 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-26
 */
public interface IDeptService extends IService<Dept> {

//    List<Dept> list(LambdaQueryWrapper<Dept> wrapper);
}
