package com.example.sys.service.impl;

import com.example.sys.entity.SysProtect;
import com.example.sys.service.IReminderService;
import com.example.sys.service.IMailService;
import com.example.sys.service.ISysProtectService;
import com.example.sys.service.IUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.concurrent.ScheduledFuture;

@Service
public class ReminderServiceImpl implements IReminderService, SchedulingConfigurer {
    private static final Logger logger = LoggerFactory.getLogger(ReminderServiceImpl.class);

    @Resource
    private ISysProtectService sysProtectService;
    @Resource
    private IUserService userService;
    @Resource
    private IMailService mailService;
    @Resource
    private TaskScheduler taskScheduler;  // 自动注入任务调度器

    @Value("${reminder.send-on-start:true}")
    private boolean sendOnStart;
    @Value("${reminder.cron:0 0/2 * * * ?}")
    private String reminderCron;  // 可动态修改的cron表达式

    private ScheduledFuture<?> scheduledFuture;  // 持有当前定时任务的引用
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy/MM/dd");
    private static final String STATUS_NOT_STARTED = "待测评";
    private static final String STATUS_NOT_COMPLETED = "逾期";
    private static final String STATUS_COMPLETED = "已完成";

    @PostConstruct
    public void initSendReminder() {
        logger.info("当前定时任务cron表达式：{}", reminderCron);
        if (sendOnStart) {
            logger.info("项目启动，触发初始邮件提醒...");
            sendEvaluationReminders();
            logger.info("初始邮件提醒发送完成");
        }
    }

    // 实现SchedulingConfigurer接口，动态注册定时任务
    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.setTaskScheduler(taskScheduler);
        // 初始化定时任务
        scheduledFuture = taskRegistrar.getScheduler().schedule(
            this::sendEvaluationReminders,
            triggerContext -> new CronTrigger(reminderCron).nextExecutionTime(triggerContext)
        );
    }

    // 新增：允许外部动态修改cron表达式（如通过接口调用）
    public void updateCron(String newCron) {
        if (scheduledFuture != null) {
            scheduledFuture.cancel(true);  // 取消旧任务
        }
        this.reminderCron = newCron;
        // 重新注册新任务
        scheduledFuture = taskScheduler.schedule(
            this::sendEvaluationReminders,
            triggerContext -> new CronTrigger(reminderCron).nextExecutionTime(triggerContext)
        );
        logger.info("定时任务cron已更新为：{}", newCron);
    }

    @Override
    public void sendEvaluationReminders() {
        LocalDate currentDate = LocalDate.now();
        List<SysProtect> protectList = sysProtectService.list();

        for (SysProtect protect : protectList) {
            try {
                String evaluationStatus = protect.getEvaluationStatus();
                String plannedTimeStr = protect.getPlannedEvaluationTime();
                String evaluationOrganization = protect.getEvaluationOrganization();

                if (STATUS_COMPLETED.equals(evaluationStatus)) {
                    logger.debug("任务[{}]已完成，跳过提醒", protect.getSystemName());
                    continue;
                }
                if (plannedTimeStr == null) {
                    logger.warn("任务[{}]计划时间为空，跳过提醒", protect.getSystemName());
                    continue;
                }
                if (evaluationOrganization == null) {
                    logger.warn("任务[{}]责任部门为空，跳过提醒", protect.getSystemName());
                    continue;
                }

                String datePart = plannedTimeStr.length() >= 10
                    ? plannedTimeStr.substring(0, 10)
                    : plannedTimeStr;
                
                // 尝试多种日期格式
                LocalDate plannedDate;
                try {
                    plannedDate = LocalDate.parse(datePart, DATE_FORMATTER);
                } catch (DateTimeParseException e1) {
                    try {
                        // 尝试 yyyy/MM/dd 格式
                        plannedDate = LocalDate.parse(datePart, DateTimeFormatter.ofPattern("yyyy/MM/dd"));
                    } catch (DateTimeParseException e2) {
                        try {
                            // 尝试 yyyy.MM.dd 格式
                            plannedDate = LocalDate.parse(datePart, DateTimeFormatter.ofPattern("yyyy.MM.dd"));
                        } catch (DateTimeParseException e3) {
                            // 如果都失败，记录错误并跳过
                            logger.warn("任务[{}]计划时间格式无法解析，原始值：{}", protect.getSystemName(), plannedTimeStr);
                            continue;
                        }
                    }
                }

                long daysDiff = ChronoUnit.DAYS.between(currentDate, plannedDate);
                List<String> emails = userService.getEmailsByOrganization(evaluationOrganization);

                if (emails.isEmpty()) {
                    logger.debug("任务[{}]无责任用户邮箱，跳过提醒", protect.getSystemName());
                    continue;
                }

                String content = buildMailContent(protect);

                if (STATUS_NOT_STARTED.equals(evaluationStatus)) {
                    if (daysDiff == 2) {
                        sendReminder(emails, "【即将开始】测评任务提醒", content);
                    }
                } else if (STATUS_NOT_COMPLETED.equals(evaluationStatus)) {
                    if (daysDiff <= 0) {
                        sendReminder(emails, "【逾期】测评任务提醒", content);
                    }
                }
            } catch (DateTimeParseException e) {
                logger.error("任务[{}]计划时间格式错误（预期yyyy-MM-dd），原始值：{}",
                    protect.getSystemName(), protect.getPlannedEvaluationTime(), e);
            } catch (Exception e) {
                logger.error("处理任务[{}]时发生未知异常", protect.getSystemName(), e);
            }
        }
    }

    private String buildMailContent(SysProtect protect) {
        return "尊敬的用户：\n\n" +
                "以下测评任务状态需要您的关注：\n\n" +
                "系统名称：" + protect.getSystemName() + "\n" +
                "级别：" + protect.getClassificationLevel() + "\n" +
                "计划评测日期：" + (protect.getPlannedEvaluationTime().length() >= 10
                    ? protect.getPlannedEvaluationTime().substring(0, 10)
                    : protect.getPlannedEvaluationTime()) + "\n" +
                "当前状态：" + protect.getEvaluationStatus() + "\n" +
                "评测项目：" + protect.getEvaluationItem() + "\n\n" +
                "请及时处理以确保任务按时完成！";
    }

    private void sendReminder(List<String> emails, String subject, String content) {
        if (!emails.isEmpty()) {
            mailService.sendBatchMail(emails, subject, content);
            logger.info("发送提醒邮件，主题：{}，收件人：{}", subject, emails);
        }
    }
}



