<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header">
        <span class="card-title">密评详情</span>
      </div>

      <!-- 搜索表单 -->
      <el-form :model="queryForm" :inline="true" class="search-form" size="small" label-width="85px">
        <el-row :gutter="16" align="middle">
          <el-col :span="6">
            <el-form-item label="所属单位">
              <el-select v-model="queryForm.systemOwnerOrg" placeholder="请选择所属单位" clearable style="width: 100%">
                <el-option label="中国核电" value="中国核电"></el-option>
                <el-option label="秦山核电" value="秦山核电"></el-option>
                <el-option label="江苏核电" value="江苏核电"></el-option>
                <el-option label="福清核电" value="福清核电"></el-option>
                <el-option label="海南核电" value="海南核电"></el-option>
                <el-option label="三门核电" value="三门核电"></el-option>
                <el-option label="霞浦核电" value="霞浦核电"></el-option>
                <el-option label="漳州核电" value="漳州核电"></el-option>
                <el-option label="中核武汉" value="中核武汉"></el-option>
                <el-option label="中核能源" value="中核能源"></el-option>
                <el-option label="运行研究院" value="运行研究院"></el-option>
                <el-option label="研究运维" value="研究运维"></el-option>
                <el-option label="辽宁核电" value="辽宁核电"></el-option>
                <el-option label="中核山东" value="中核山东"></el-option>
                <el-option label="中核储能" value="中核储能"></el-option>
                <el-option label="中核苏能" value="中核苏能"></el-option>
                <el-option label="中核海得" value="中核海得"></el-option>
                <el-option label="河北核电" value="河北核电"></el-option>
                <el-option label="庄河核电" value="庄河核电"></el-option>
                <el-option label="中核光电" value="中核光电"></el-option>
                <el-option label="桃花江核电" value="桃花江核电"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="系统名称">
              <el-input v-model="queryForm.systemName" placeholder="请输入系统名称" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="备案号">
              <el-input v-model="queryForm.filingNumber" placeholder="请输入备案号" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="定级级别">
              <el-select v-model="queryForm.classificationLevel" placeholder="请选择定级级别" clearable style="width: 100%">
                <el-option label="一级" value="一级" />
                <el-option label="二级" value="二级" />
                <el-option label="三级" value="三级" />
                <el-option label="四级" value="四级" />
                <el-option label="五级" value="五级" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16" align="middle">
          <el-col :span="6">
            <el-form-item label="业务类型">
              <el-select v-model="queryForm.businessType" placeholder="请选择业务类型" clearable style="width: 100%">
                <el-option label="生产作业" value="生产作业" />
                <el-option label="指挥调度" value="指挥调度" />
                <el-option label="内部办公" value="内部办公" />
                <el-option label="公众服务" value="公众服务" />
                <el-option label="监控" value="监控" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="分类">
              <el-select v-model="queryForm.category" placeholder="请选择分类" clearable style="width: 100%">
                <el-option label="基础类" value="基础类" />
                <el-option label="应用类" value="应用类" />
                <el-option label="系统类" value="系统类" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="系统状态">
              <el-select v-model="queryForm.systemStatus" placeholder="请选择系统状态" clearable style="width: 100%">
                <el-option label="运行中" value="运行中" />
                <el-option label="建设中" value="建设中" />
                <el-option label="已停用" value="已停用" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="风险等级">
              <el-select v-model="queryForm.riskLevel" placeholder="请选择风险等级" clearable style="width: 100%">
                <el-option label="低" value="低" />
                <el-option label="中" value="中" />
                <el-option label="高" value="高" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16" align="middle">
          <el-col :span="6">
            <el-form-item label="测评时间">
              <el-date-picker
                  v-model="queryForm.evaluationTime"
                  type="date"
                  placeholder="选择测评时间"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="测评单位">
              <el-input v-model="queryForm.evaluationOrganization" placeholder="请输入测评单位" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="测评状态">
              <el-select v-model="queryForm.evaluationStatus" placeholder="请选择测评状态" clearable style="width: 100%">
                <el-option label="已完成" value="已完成" />
                <el-option label="进行中" value="进行中" />
                <el-option label="逾期" value="逾期" />
                <el-option label="待测评" value="待测评" />
                <el-option label="未知分级" value="未知分级" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: center; margin-top: 10px;">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 数据表格 -->
      <el-table
          v-loading="loading"
          :data="tableData"
          border
          style="width: 100%; margin-top: 20px;"
          :header-cell-style="{ background: '#f5f7fa' }"
      >
        <el-table-column prop="systemOwnerOrg" label="所属单位" min-width="120" align="center" />
        <el-table-column prop="systemName" label="系统名称" min-width="120" align="center" />
        <el-table-column prop="businessType" label="业务类型" min-width="120" align="center" />
        <el-table-column prop="filingNumber" label="备案号" min-width="120" align="center" />
        <el-table-column prop="classificationLevel" label="定级级别" min-width="100" align="center" />
        <el-table-column prop="category" label="分类" min-width="120" align="center" />
        <el-table-column prop="evaluationTime" label="测评时间" min-width="120" align="center" sortable />
        <el-table-column prop="evaluationOrganization" label="测评单位" min-width="120" align="center" />
        <el-table-column prop="evaluationStatus" label="测评状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getEvaluationStatusType(scope.row.evaluationStatus)">
              {{ scope.row.evaluationStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="evaluationResult" label="测评结果" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getEvaluationResultType(scope.row.evaluationResult)">
              {{ scope.row.evaluationResult }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="systemStatus" label="系统状态" min-width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getSystemStatusType(scope.row.systemStatus)">
              {{ scope.row.systemStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="controlPoint" label="控制点" min-width="120" align="center" />
        <el-table-column prop="evaluationItem" label="测评项" min-width="120" align="center" />
        <el-table-column prop="riskLevel" label="风险等级" min-width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getRiskLevelType(scope.row.riskLevel)">
              {{ scope.row.riskLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="cryptoScore" label="密评分数" min-width="100" align="center" />
      </el-table>
    </el-card>

    <!-- 分页 -->
    <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNo"
        :page-sizes="[5, 10, 20, 40]"
        :page-size="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
    </el-pagination>
  </div>
</template>

<script>
import { getCryptoEvaluationList } from '@/api/cryptoEvaluation'

export default {
  name: 'CryptoEvaluationDetail',
  data() {
    return {
      loading: false,
      total: 0,
      queryParams: {
        pageNo: 1,
        pageSize: 5
      },
      queryForm: {
        systemOwnerOrg: '',
        systemName: '',
        businessType: '',
        category: '',
        evaluationTime: '',
        evaluationOrganization: '',
        evaluationStatus: '',
        filingNumber: '',
        classificationLevel: '',
        systemStatus: '',
        riskLevel: ''
      },
      tableData: []
    }
  },
  methods: {
    handleQuery() {
      this.queryParams.pageNo = 1
      this.queryParams = {
        ...this.queryParams,
        ...this.queryForm
      }
      this.getList()
    },
    handleReset() {
      this.queryForm = {
        systemOwnerOrg: '',
        systemName: '',
        businessType: '',
        category: '',
        evaluationTime: '',
        evaluationOrganization: '',
        evaluationStatus: '',
        filingNumber: '',
        classificationLevel: '',
        systemStatus: '',
        riskLevel: ''
      }
      this.queryParams = {
        pageNo: 1,
        pageSize: this.queryParams.pageSize
      }
      this.getList()
    },
    getList() {
      this.loading = true
      console.log('发送请求参数:', this.queryParams)
      getCryptoEvaluationList(this.queryParams)
        .then(response => {
          console.log('API响应:', response)
          if (response.code === 200 || response.code === 20000) {
            this.tableData = response.data.cryptoEvaluation || []
            this.total = response.data.total || 0
            console.log('设置表格数据:', this.tableData)
            console.log('设置总数:', this.total)
            // 添加详细的数据结构调试
            if (this.tableData.length > 0) {
              console.log('第一条数据:', this.tableData[0])
              console.log('数据字段:', Object.keys(this.tableData[0]))
            }
          } else {
            this.$message.error(response.message || '获取数据失败')
          }
          this.loading = false
        })
        .catch(error => {
          console.error('获取数据失败:', error)
          this.$message.error('获取数据失败')
          this.loading = false
        })
    },
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.queryParams.pageNo = val
      this.getList()
    },
    getResultType(result) {
      const typeMap = {
        '通过': 'success',
        '整改中': 'warning',
        '不通过': 'danger'
      }
      return typeMap[result] || 'info'
    },
    getSystemStatusType(status) {
      const typeMap = {
        '运行中': 'success',
        '建设中': 'warning',
        '已停用': 'danger',
        '已下线': 'info'
      }
      return typeMap[status] || 'info'
    },
    getRiskLevelType(level) {
      const typeMap = {
        '低': 'success',
        '中': 'warning',
        '高': 'danger'
      }
      return typeMap[level] || 'info'
    },
    getEvaluationStatusType(status) {
      const typeMap = {
        '已完成': 'success',
        '进行中': 'warning',
        '逾期': 'danger',
        '待测评': 'info',
        '未知分级': 'info'
      }
      return typeMap[status] || 'info'
    },
    getEvaluationResultType(result) {
      const typeMap = {
        '符合': 'success',
        '不符合': 'danger'
      }
      return typeMap[result] || 'info'
    }
  },
  created() {
    this.getList()
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f0f2f5;
}

.box-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .card-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }
}

.search-form {
  padding: 10px 0 0 0;
  .el-form-item {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
  }
  .el-input, .el-select, .el-date-picker, .el-date-editor {
    width: 100%;
    height: 36px !important;
    line-height: 36px !important;
    display: block !important;
    vertical-align: middle !important;
    box-sizing: border-box;
  }
  .el-input__inner {
    height: 36px !important;
    line-height: 36px !important;
    box-sizing: border-box;
    display: block !important;
    vertical-align: middle !important;
  }
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    height: 36px !important;
    line-height: 36px !important;
    box-sizing: border-box;
    display: block !important;
    vertical-align: middle !important;
  }
}

.pagination-container {
  padding: 15px;
  background: white;
  border-top: 1px solid #e8e8e8;
  text-align: right;
}

:deep(.el-pagination) {
  padding: 0;
  margin: 0;
  font-weight: normal;
}
</style> 