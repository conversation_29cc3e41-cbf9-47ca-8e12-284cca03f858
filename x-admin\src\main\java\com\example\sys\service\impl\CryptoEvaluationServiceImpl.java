package com.example.sys.service.impl;

import com.example.sys.entity.CryptoEvaluation;
import com.example.sys.mapper.CryptoEvaluationMapper;
import com.example.sys.service.ICryptoEvaluationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;
import java.math.BigDecimal;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Service
public class CryptoEvaluationServiceImpl extends ServiceImpl<CryptoEvaluationMapper, CryptoEvaluation> implements ICryptoEvaluationService {

    @Override
    public Map<String, Object> getStatistics(String systemOwnerOrg, String systemName,
                                         String startTime, String endTime) {
        // 构建查询条件
        QueryWrapper<CryptoEvaluation> wrapper = new QueryWrapper<>();

        if (StringUtils.hasText(systemOwnerOrg)) {
            wrapper.eq("system_owner_org", systemOwnerOrg);
        }
        if (StringUtils.hasText(systemName)) {
            wrapper.eq("system_name", systemName);
        }

        // 处理日期条件（针对LocalDate类型的正确处理）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        try {
            if (StringUtils.hasText(startTime) && StringUtils.hasText(endTime)) {
                LocalDate start = LocalDate.parse(startTime, formatter);
                LocalDate end = LocalDate.parse(endTime, formatter);
                wrapper.between("evaluation_time", start, end);
            } else if (StringUtils.hasText(startTime)) {
                LocalDate start = LocalDate.parse(startTime, formatter);
                wrapper.ge("evaluation_time", start);
            } else if (StringUtils.hasText(endTime)) {
                LocalDate end = LocalDate.parse(endTime, formatter);
                wrapper.le("evaluation_time", end);
            }
        } catch (DateTimeParseException e) {
            // 日期格式错误时忽略日期条件
            log.warn("日期格式解析错误: " + e.getMessage());
        }

        // 查询数据
        List<CryptoEvaluation> dataList = this.list(wrapper);

        // 初始化结果集
        Map<String, Object> result = new LinkedHashMap<>();

        // 1. 控制点(controlPoint)计数统计
        Map<String, Long> controlPointCount = dataList.stream()
            .filter(item -> StringUtils.hasText(item.getControlPoint()))
            .collect(Collectors.groupingBy(
                CryptoEvaluation::getControlPoint,
                Collectors.counting()
            ));
        result.put("controlPointStatistics", controlPointCount);

        // 2. 测评时间(evaluationTime)年份计数统计
        // 修正：直接使用LocalDate的getYear()方法获取年份
        Map<String, Long> yearCount = dataList.stream()
            .filter(item -> item.getEvaluationTime() != null)
            .map(item -> String.valueOf(item.getEvaluationTime().getYear()))
            .collect(Collectors.groupingBy(
                year -> year,
                Collectors.counting()
            ));
        result.put("evaluationYearStatistics", yearCount);

        // 3. 业务类型(businessType)计数统计
        Map<String, Long> businessTypeCount = dataList.stream()
            .filter(item -> StringUtils.hasText(item.getBusinessType()))
            .collect(Collectors.groupingBy(
                CryptoEvaluation::getBusinessType,
                Collectors.counting()
            ));
        result.put("businessTypeStatistics", businessTypeCount);

        // 4. 定级级别(classificationLevel)计数统计
        Map<String, Long> classificationLevelCount = dataList.stream()
            .filter(item -> StringUtils.hasText(item.getClassificationLevel()))
            .collect(Collectors.groupingBy(
                CryptoEvaluation::getClassificationLevel,
                Collectors.counting()
            ));
        result.put("classificationLevelStatistics", classificationLevelCount);

        // 5. 按所属单位(systemOwnerOrg)分组的定级级别统计
        Map<String, Map<String, Long>> orgClassificationCount = dataList.stream()
            .filter(item -> StringUtils.hasText(item.getSystemOwnerOrg()) &&
                           StringUtils.hasText(item.getClassificationLevel()))
            .collect(Collectors.groupingBy(
                CryptoEvaluation::getSystemOwnerOrg,
                Collectors.groupingBy(
                    CryptoEvaluation::getClassificationLevel,
                    Collectors.counting()
                )
            ));
        result.put("orgClassificationStatistics", orgClassificationCount);

        // 6. 新增：风险等级(riskLevel)分布统计
        Map<String, Long> riskLevelCount = dataList.stream()
            .filter(item -> StringUtils.hasText(item.getRiskLevel()))
            .collect(Collectors.groupingBy(
                CryptoEvaluation::getRiskLevel,
                Collectors.counting()
            ));
        result.put("riskLevelStatistics", riskLevelCount);

        // 7. 新增：密码评分区间统计
        Map<String, Long> scoreRangeCount = dataList.stream()
            .filter(item -> item.getCryptoScore() != null)
            .map(item -> {
                BigDecimal score = item.getCryptoScore();
                if (score.compareTo(new BigDecimal("1")) <= 0) {
                    return "1分及以下";
                } else if (score.compareTo(new BigDecimal("1.5")) <= 0) {
                    return "1-1.5分";
                } else {
                    return "1.5-2分";
                }
            })
            //     if (score.compareTo(new BigDecimal("1")) < 0) {
            //         return "1分以下";
            //     } else {
            //         return "2分以上";
            //     }
            // })
            .collect(Collectors.groupingBy(
                range -> range,
                Collectors.counting()
            ));
        result.put("cryptoScoreRangeStatistics", scoreRangeCount);

        // 8. 新增：测评状态(evaluationStatus)统计
        Map<String, Long> evaluationStatusCount = dataList.stream()
            .filter(item -> StringUtils.hasText(item.getEvaluationStatus()))
            .collect(Collectors.groupingBy(
                CryptoEvaluation::getEvaluationStatus,
                Collectors.counting()
            ));
        result.put("evaluationStatusStatistics", evaluationStatusCount);
        //9.新增：测评结果(evaluationResult)统计
        Map<String, Long> evaluationResultCount = dataList.stream()
                .filter(item -> StringUtils.hasText(item.getEvaluationResult()))
                .collect(Collectors.groupingBy(
                        CryptoEvaluation::getEvaluationResult,
                        Collectors.counting()
                ));
        result.put("evaluationResultStatistics", evaluationResultCount);
        return result;
    }
}
