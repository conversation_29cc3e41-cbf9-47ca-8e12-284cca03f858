<template>
  <div class="dashboard">
    <!-- 顶部统计卡片 -->
    <div class="stats-row">
      <div class="stat-card">
        <div class="stat-icon-box" style="background: #e6f4ff;">
          <i class="el-icon-s-platform" style="color: #1890ff;"></i>
        </div>
        <div class="stat-content">
          <div class="stat-title">管理系统总数</div>
          <div class="stat-value">86</div>
          <div class="stat-tag success">+2 新增</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon-box" style="background: #fff0f0;">
          <i class="el-icon-warning" style="color: #ff4d4f;"></i>
        </div>
        <div class="stat-content">
          <div class="stat-title">漏洞总数</div>
          <div class="stat-value">1,422</div>
          <div class="stat-tag danger">12 未处理</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon-box" style="background: #f6ffed;">
          <i class="el-icon-alarm-clock" style="color: #52c41a;"></i>
        </div>
        <div class="stat-content">
          <div class="stat-title">进行中任务</div>
          <div class="stat-value">23</div>
          <div class="stat-tag warning">3 即将到期</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon-box" style="background: #f0f5ff;">
          <i class="el-icon-cpu" style="color: #2f54eb;"></i>
        </div>
        <div class="stat-content">
          <div class="stat-title">资源健康度</div>
          <div class="stat-value">92%</div>
          <div class="stat-tag normal">运行正常</div>
        </div>
      </div>
    </div>

    <!-- 资源监控仪表盘 -->
    <div class="chart-row">
      <div class="chart-card">
        <h3 class="chart-title">系统资源监控</h3>
        <div class="gauges-container">
          <div class="gauge-item">
            <div ref="cpuChart" class="gauge"></div>
            <div class="gauge-label">CPU 使用率</div>
            <div class="gauge-value">68%</div>
          </div>
          <div class="gauge-item">
            <div ref="memoryChart" class="gauge"></div>
            <div class="gauge-label">内存 使用率</div>
            <div class="gauge-value">82%</div>
          </div>
          <div class="gauge-item">
            <div ref="diskChart" class="gauge"></div>
            <div class="gauge-label">磁盘 使用率</div>
            <div class="gauge-value">45%</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 漏洞分布图表 -->
    <div class="chart-row">
      <div class="chart-card">
        <h3 class="chart-title">漏洞类型分布</h3>
        <div ref="vulnPieChart" class="chart-container"></div>
      </div>
      <div class="chart-card">
        <h3 class="chart-title">单位系统漏洞统计</h3>
        <div ref="vulnBarChart" class="chart-container"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  mounted() {
    this.initCharts()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initCharts() {
      // 初始化仪表盘
      this.initGauge('cpuChart', 68, '#1890ff')
      this.initGauge('memoryChart', 82, '#52c41a')
      this.initGauge('diskChart', 45, '#722ed1')

      // 漏洞饼图
      echarts.init(this.$refs.vulnPieChart).setOption({
        tooltip: { trigger: 'item' },
        legend: { top: 'bottom' },
        series: [{
          type: 'pie',
          radius: ['40%', '70%'],
          roseType: 'radius',
          itemStyle: { borderRadius: 8 },
          data: [
            { value: 335, name: '高危漏洞', itemStyle: { color: '#ff4d4f' }},
            { value: 310, name: '中危漏洞', itemStyle: { color: '#faad14' }},
            { value: 274, name: '低危漏洞', itemStyle: { color: '#13c2c2' }},
            { value: 235, name: '信息漏洞', itemStyle: { color: '#2f54eb' }}
          ]
        }]
      })

      // 漏洞柱状图
      echarts.init(this.$refs.vulnBarChart).setOption({
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['单位A', '单位B', '单位C', '单位D', '单位E'],
          axisLabel: { color: '#666' }
        },
        yAxis: { type: 'value' },
        series: [{
          type: 'bar',
          data: [120, 200, 150, 80, 70],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#1890ff' },
              { offset: 1, color: '#1890ff33' }
            ]),
            borderRadius: [4, 4, 0, 0]
          },
          barWidth: 36
        }]
      })
    },
    initGauge(ref, value, color) {
      const chart = echarts.init(this.$refs[ref])
      chart.setOption({
        series: [{
          type: 'gauge',
          radius: '90%',
          startAngle: 90,
          endAngle: -270,
          progress: {
            show: true,
            width: 16,
            roundCap: true,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: this.hexToRgba(color, 0.2) },
                { offset: 1, color: color }
              ])
            }
          },
          pointer: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false }
        }]
      })
    },
    hexToRgba(hex, opacity) {
      const r = parseInt(hex.slice(1, 3), 16)
      const g = parseInt(hex.slice(3, 5), 16)
      const b = parseInt(hex.slice(5, 7), 16)
      return `rgba(${r}, ${g}, ${b}, ${opacity})`
    },
    handleResize() {
      echarts.init(this.$refs.cpuChart).resize()
      echarts.init(this.$refs.memoryChart).resize()
      echarts.init(this.$refs.diskChart).resize()
      echarts.init(this.$refs.vulnPieChart).resize()
      echarts.init(this.$refs.vulnBarChart).resize()
    }
  }
}
</script>

<style scoped>
.dashboard {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-3px);
}

.stat-icon-box {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.stat-content {
  flex: 1;
}

.stat-title {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stat-tag {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.success {
  background: #f6ffed;
  color: #52c41a;
}

.danger {
  background: #fff1f0;
  color: #ff4d4f;
}

.warning {
  background: #fffbe6;
  color: #faad14;
}

.normal {
  background: #f0f5ff;
  color: #2f54eb;
}

.chart-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.chart-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.chart-title {
  margin: 0 0 24px 0;
  color: #333;
  font-size: 16px;
  font-weight: 500;
}

.gauges-container {
  display: flex;
  justify-content: space-around;
  padding: 20px 0;
}

.gauge-item {
  text-align: center;
  width: 180px;
}

.gauge {
  height: 180px;
  width: 100%;
}

.gauge-label {
  color: #666;
  margin: 12px 0 8px;
  font-size: 14px;
}

.gauge-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.chart-container {
  height: 400px;
}
</style>

