<template>
  <div class="app-container">

    <!-- 导航栏 -->
    <el-menu
      class="el-menu-vertical-demo"
      :default-active="activeMenu"
      @select="handleSelect"
    >
      <el-menu-item index="1">系统参数配置</el-menu-item>
      <el-menu-item index="2">日志管理</el-menu-item>
      <el-menu-item index="3">备份与恢复</el-menu-item>
      <el-menu-item index="4">系统更新</el-menu-item>
    </el-menu>

    <!-- 主内容区域 -->
    <div class="content">
      <!-- 系统参数配置 -->
      <div v-if="activeMenu === '1'">
        <h2>系统参数配置</h2>
        <el-form :model="form">
          <el-form-item label="参数1">
            <el-input v-model="form.param1"></el-input>
          </el-form-item>
          <el-form-item label="参数2">
            <el-input v-model="form.param2"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="saveSettings">保存配置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 日志管理 -->
      <div v-if="activeMenu === '2'">
        <h2>日志管理</h2>
        <el-input
          placeholder="搜索日志..."
          v-model="searchTerm"
          style="margin-bottom: 20px;"
        />
        <el-table :data="filteredLogs">
          <el-table-column prop="date" label="日期" width="180"></el-table-column>
          <el-table-column prop="level" label="级别" width="100"></el-table-column>
          <el-table-column prop="message" label="日志内容"></el-table-column>
        </el-table>
      </div>

      <!-- 备份与恢复 -->
      <div v-if="activeMenu === '3'">
        <h2>备份与恢复</h2>
        <el-button type="primary" @click="backupSystem">立即备份</el-button>
        <el-table :data="backupList" style="margin-top: 20px;">
          <el-table-column prop="date" label="备份日期" width="180"></el-table-column>
          <el-table-column prop="status" label="状态" width="100"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button @click="restoreBackup(scope.row)">恢复</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 系统更新 -->
      <div v-if="activeMenu === '4'">
        <h2>系统更新</h2>
        <el-button type="primary" @click="checkForUpdates">检查更新</el-button>
        <div v-if="updateAvailable" style="margin-top: 20px;">
          <p>发现新版本: {{ newVersion }}</p>
          <el-button type="success" @click="performUpdate">立即更新</el-button>
        </div>
        <div v-else style="margin-top: 20px;">
          <p>系统已经是最新版本。</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      activeMenu: '1', // 默认选中的菜单项
      form: {
        param1: '',
        param2: '',
      },
      searchTerm: '',
      logs: [
        { date: '2024-08-30', level: 'INFO', message: '系统启动成功' },
        { date: '2024-08-30', level: 'ERROR', message: '数据库连接失败' },
      ],
      backupList: [
        { date: '2024-08-30', status: '成功' },
        { date: '2024-08-25', status: '成功' },
      ],
      updateAvailable: false,
      newVersion: '1.2.3',
    };
  },
  computed: {
    filteredLogs() {
      return this.logs.filter(log =>
        log.message.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    },
  },
  methods: {
    handleSelect(key) {
      this.activeMenu = key;
    },
    saveSettings() {
      // 保存配置的逻辑
      console.log('保存配置:', this.form);
    },
    backupSystem() {
      // 执行备份操作的逻辑
      console.log('备份系统');
    },
    restoreBackup(backup) {
      // 执行恢复操作的逻辑
      console.log('恢复备份:', backup);
    },
    checkForUpdates() {
      // 检查更新的逻辑
      console.log('检查系统更新');
      this.updateAvailable = true; // 模拟有新版本
    },
    performUpdate() {
      // 执行更新的逻辑
      console.log('执行系统更新');
    },
  },
};
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: row;
}

.el-menu-vertical-demo {
  width: 200px;
}

.content {
  margin-left: 20px;
  flex-grow: 1;
  padding: 20px;
}
</style>