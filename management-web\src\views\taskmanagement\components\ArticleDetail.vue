<template>
  <div class="createPost-container">
    <!-- 表单用于创建或编辑文章 -->
    <el-form ref="postForm" :model="postForm" :rules="rules" class="form-container">

      <!-- 固定顶部导航栏 -->
      <sticky :z-index="10" :class-name="'sub-navbar '+postForm.status">
        <!-- 评论管理下拉菜单 -->
        <CommentDropdown v-model="postForm.comment_disabled" />
        <!-- 平台选择下拉菜单 -->
        <PlatformDropdown v-model="postForm.platforms" />
        <!-- 源链接选择下拉菜单 -->
        <SourceUrlDropdown v-model="postForm.source_uri" />
        <!-- 发布按钮 -->
        <el-button v-loading="loading" style="margin-left: 10px;" type="success" @click="submitForm">
          发布
        </el-button>
        <!-- 草稿按钮 -->
        <el-button v-loading="loading" type="warning" @click="draftForm">
          保存草稿
        </el-button>
      </sticky>

      <!-- 主要的内容区域用于创建文章 -->
      <div class="createPost-main-container">
        <el-row>
          <!-- 警告或通知 -->
          <Warning />

          <!-- 标题输入区 -->
          <el-col :span="24">
            <el-form-item style="margin-bottom: 40px;" prop="title">
              <MDinput v-model="postForm.title" :maxlength="100" name="name" required>
                标题
              </MDinput>
            </el-form-item>

            <!-- 文章信息区域 -->
            <div class="postInfo-container">
              <el-row>
                <!-- 使用远程用户搜索来实现发布者选择 -->
                <!-- <el-col :span="8">
                  <el-form-item label-width="60px" label="发布者:" class="postInfo-container-item">
                    <el-select v-model="postForm.author" :remote-method="getRemoteUserList" filterable default-first-option remote placeholder="Search user">
                      <el-option v-for="(item,index) in userListOptions" :key="item+index" :label="item" :value="item" />
                    </el-select>
                  </el-form-item>
                </el-col> -->
                <el-col :span="8">
                  <el-form-item label-width="60px" label="发布者:" class="postInfo-container-item">
                    <el-input v-model="postForm.author" placeholder="请输入发布者名称"></el-input>
                  </el-form-item>
                </el-col>

                <!-- 截止时间选择 -->
                <el-col :span="10">
                  <el-form-item label-width="120px" label="截止时间:" class="postInfo-container-item">
                    <el-date-picker v-model="displayTime" type="datetime" format="yyyy-MM-dd HH:mm:ss" placeholder="选择截止时间" />
                  </el-form-item>
                </el-col>

                <!-- 重要程度选择 -->
                <el-col :span="6">
                  <el-form-item label-width="90px" label="重要程度:" class="postInfo-container-item">
                    <el-rate
                      v-model="postForm.importance"
                      :max="3"
                      :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
                      :low-threshold="1"
                      :high-threshold="3"
                      style="display:inline-block"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>

        <!-- 摘要文本框 -->
        <el-form-item style="margin-bottom: 40px;" label-width="70px" label="摘要:">
          <el-input v-model="postForm.content_short" :rows="1" type="textarea" class="article-textarea" autosize placeholder="请输入摘要内容" />
          <!-- 字数统计 -->
          <span v-show="contentShortLength" class="word-counter">{{ contentShortLength }}words</span>
        </el-form-item>

        <!-- 内容编辑器 -->
        <el-form-item prop="content" style="margin-bottom: 30px;">
          <Tinymce ref="editor" v-model="postForm.content" :height="400" />
        </el-form-item>

        <!-- 特色图片上传器 -->
        <el-form-item prop="image_uri" style="margin-bottom: 30px;">
          <Upload v-model="postForm.image_uri" />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
import Tinymce from '@/components/Tinymce'
import Upload from '@/components/Upload/SingleImage3'
// 引入MDinput组件
import MDinput from '@/components/MDinput'
// 引入Sticky组件，用于实现粘性header效果
import Sticky from '@/components/Sticky'
// 引入URL校验方法
import { validURL } from '@/utils/validate'
// 引入获取文章详情的API方法
// import { fetchArticle } from '@/api/article'
// 引入远程搜索用户的API方法
// import { searchUser } from '@/api/remote-search'
// 引入Warning组件，用于显示警告信息
import Warning from './Warning'
// 引入三个下拉菜单组件，分别用于评论、平台和源URL的选择
import { CommentDropdown, PlatformDropdown, SourceUrlDropdown } from './Dropdown'

// 定义默认表单数据，用于文章编辑或创建
const defaultForm = {
  status: '草稿', // 文章状态，默认为草稿
  title: '', // 文章题目
  content: '', // 文章内容
  content_short: '', // 文章摘要
  source_uri: '', // 文章外链
  image_uri: '', // 文章图片链接
  display_time: undefined, // 前台展示时间
  id: undefined, // 文章ID
  platforms: ['a-platform'], // 发布平台
  // comment_disabled: false, // 是否禁用评论
  importance: 0 // 文章重要性
}

// 定义组件
export default {
  name: 'ArticleDetail',
  components: { Tinymce, MDinput, Upload, Sticky, Warning, CommentDropdown, PlatformDropdown, SourceUrlDropdown },
  props: {
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    // 定义表单校验规则
    const validateRequire = (rule, value, callback) => {
      if (value === '') {
        this.$message({
          message: rule.field + '为必传项',
          type: 'error'
        })
        callback(new Error(rule.field + '为必传项'))
      } else {
        callback()
      }
    }
    const validateSourceUri = (rule, value, callback) => {
      if (value) {
        if (validURL(value)) {
          callback()
        } else {
          this.$message({
            message: '外链url填写不正确',
            type: 'error'
          })
          callback(new Error('外链url填写不正确'))
        }
      } else {
        callback()
      }
    }
    // 返回组件数据
    return {
      postForm: Object.assign({}, defaultForm),
      loading: false,
      userListOptions: [],
      rules: {
        image_uri: [{ validator: validateRequire }], // 图片链接校验规则
        title: [{ validator: validateRequire }], // 标题校验规则
        content: [{ validator: validateRequire }], // 内容校验规则
        source_uri: [{ validator: validateSourceUri, trigger: 'blur' }] // 外链校验规则
      },
      tempRoute: {}
    }
  },
  computed: {
    contentShortLength() {
      return this.postForm.content_short.length
    },
    displayTime: {
      get() {
        return (+new Date(this.postForm.display_time))
      },
      set(val) {
        this.postForm.display_time = new Date(val)
      }
    }
  },
  created() {
    if (this.isEdit) {
      const id = this.$route.params && this.$route.params.id
      this.fetchData(id)
    }
    // 复制当前路由信息，用于在编辑页面切换标签时保持路由信息不变
    this.tempRoute = Object.assign({}, this.$route)
  },
  methods: {
    // 根据文章ID获取文章详情
    fetchData(id) {
      fetchArticle(id).then(response => {
        this.postForm = response.data
        this.postForm.title += `   Article Id:${this.postForm.id}`
        this.postForm.content_short += `   Article Id:${this.postForm.id}`
        this.setTagsViewTitle()
        this.setPageTitle()
      }).catch(err => {
        console.log(err)
      })
    },
    // 设置标签视图标题
    setTagsViewTitle() {
      const title = '编辑任务'
      const route = Object.assign({}, this.tempRoute, { title: `${title}-${this.postForm.id}` })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },
    // 设置页面标题
    setPageTitle() {
      const title = '编辑任务'
      document.title = `${title} - ${this.postForm.id}`
    },
    // 提交表单
    submitForm() {
      console.log(this.postForm)
      this.$refs.postForm.validate(valid => {
        if (valid) {
          this.loading = true
          this.$notify({
            title: '成功',
            message: '发布任务成功',
            type: 'success',
            duration: 2000
          })
          this.postForm.status = '发布成功'
          this.loading = false
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 保存为草稿
    draftForm() {
      if (this.postForm.content.length === 0 || this.postForm.title.length === 0) {
        this.$message({
          message: '请填写必要的标题和内容',
          type: 'warning'
        })
        return
      }
      this.$message({
        message: '保存成功',
        type: 'success',
        showClose: true,
        duration: 1000
      })
      this.postForm.status = '草稿'
    },
    // 远程搜索用户列表
    getRemoteUserList(query) {
      searchUser(query).then(response => {
        if (!response.data.items) return
        this.userListOptions = response.data.items.map(v => v.name)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  // 引入全局混入，以便在组件样式中使用
  @import "~@/styles/mixin.scss";

  // 定义创建帖子页面的主容器样式
  .createPost-container {
    position: relative;

    // 定义创建帖子页面的主要内容容器样式
    .createPost-main-container {
      padding: 40px 45px 20px 50px;

      // 定义帖子信息容器样式
      .postInfo-container {
        position: relative;
        // 使用混入实现兼容性清理浮动
        @include clearfix;
        margin-bottom: 10px;

        // 定义帖子信息项的样式，使用浮动布局
        .postInfo-container-item {
          float: left;
        }
      }
    }

    // 定义字数统计的样式
    .word-counter {
      width: 40px;
      position: absolute;
      right: 10px;
      top: 0px;
    }
  }

  // 深度选择器，定制Vue富文本编辑器的样式
  .article-textarea ::v-deep {
    textarea {
      // 为文本区域设定内边距和边框样式
      padding-right: 40px;
      resize: none;
      border: none;
      border-radius: 0px;
      border-bottom: 1px solid #bfcbd9;
    }
  }
</style>
