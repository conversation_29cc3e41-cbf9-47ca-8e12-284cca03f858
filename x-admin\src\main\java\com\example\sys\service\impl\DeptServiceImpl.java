package com.example.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.example.sys.entity.Dept;
import com.example.sys.mapper.DeptMapper;
import com.example.sys.service.IDeptService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 部门 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-26
 */
@Service
public class DeptServiceImpl extends ServiceImpl<DeptMapper, Dept> implements IDeptService {
//    @Override
//    public List<Dept> list(LambdaQueryWrapper<Dept> wrapper) {
//        return this.baseMapper.selectList(wrapper);
//    }

}
