package com.example.sys.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@TableName("x_tasks")
public class Tasks implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "task_id", type = IdType.AUTO)
    private Integer taskId;


    // 任务状态（待处理/已完成），数据库默认值'待处理'
    @TableField("task_status")
    private String taskStatus;

    // 任务类型
    @TableField("task_type ")
    private String taskType;


    // 任务创建时间（自动记录）
    @TableField("create_time")
    private LocalDateTime createTime;

    // 任务最后更新时间（通过触发器自动更新）
    @TableField("end_time")
    private LocalDateTime endTime;

    // 任务负责人
    @TableField("responsible_person")
    private String responsiblePerson;


    // 软删除标记（0=未删除，1=已删除）
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    //系统所属组织
    @TableField("system_owner_org")
    private String systemOwnerOrg;

    //系统名称
    @TableField("system_name")
    private String systemName;

    //系统状态
    @TableField("system_status")
    private String systemStatus;

    public Integer getTaskId() { return taskId; }

    public void setTaskId(Integer taskId) { this.taskId = taskId; }


    public String getTaskStatus() { return taskStatus; }

    public void setTaskStatus(String taskStatus) { this.taskStatus = taskStatus; }

    public String getTaskType() { return taskType; }

    public void setTaskType(String taskType) { this.taskType = taskType; }

    public LocalDateTime getCreateTime() { return createTime; }

    public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }

    public LocalDateTime getEndTime() { return endTime; }

    public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }

    public String getResponsiblePerson() { return responsiblePerson; }

    public void setResponsiblePerson(String responsiblePerson) { this.responsiblePerson = responsiblePerson; }

    public Integer getDeleted() { return deleted; }

    public void setDeleted(Integer deleted) { this.deleted = deleted; }

    public String getSystemOwnerOrg() { return systemOwnerOrg; }

    public void setSystemOwnerOrg(String systemOwnerOrg) { this.systemOwnerOrg = systemOwnerOrg; }

    public String getSystemName() { return systemName; }

    public void setSystemName(String systemName) { this.systemName = systemName; }

    public String getSystemStatus() { return systemStatus; }

    @Override
    public String toString() {
        return "Tasks{" +
                "taskId=" + taskId +
                ", taskStatus='" + taskStatus + '\'' +
                ", taskType='" + taskType + '\'' +
                ", createTime=" + createTime +
                ", endTime=" + endTime +
                ", responsiblePerson='" + responsiblePerson + '\'' +
                ", deleted=" + deleted +
                ", systemOwnerOrg='" + systemOwnerOrg + '\'' +
                ", systemName='" + systemName + '\'' +
                ", systemStatus='" + systemStatus + '\'' +
                '}';
    }
}


