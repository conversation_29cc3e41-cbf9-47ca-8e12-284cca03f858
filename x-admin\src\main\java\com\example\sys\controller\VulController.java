package com.example.sys.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.commom.vo.Result;
import com.example.sys.annotation.OperationLog;
import com.example.sys.entity.Vul;
import com.example.sys.service.IVulService;
import com.example.sys.vo.VulExcelVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 漏洞控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-06
 */
@RestController
@RequestMapping("/vul")
public class VulController {

    @Autowired
    private IVulService vulService;

    /**
     * 测试端点 - 简单查询所有漏洞
     */
    @GetMapping("/test")
    public Result<List<Vul>> testVulList() {
        try {
            List<Vul> list = vulService.list();
            return Result.success(list);
        } catch (Exception e) {
            return Result.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询漏洞列表
     */
    @GetMapping("/list")
    public Result<Map<String, Object>> getVulList(
            @RequestParam(value = "systemOwnerOrg", required = false) String systemOwnerOrg,
            @RequestParam(value = "systemName", required = false) String systemName,
            @RequestParam(value = "businessType", required = false) String businessType,
            @RequestParam(value = "vulId", required = false) String vulId,
            @RequestParam(value = "vulName", required = false) String vulName,
            @RequestParam(value = "vulType", required = false) String vulType,
            @RequestParam(value = "vulLevel", required = false) String vulLevel,
            @RequestParam(value = "vulStatus", required = false) String vulStatus,
            @RequestParam(value = "vulDescription", required = false) String vulDescription,
            @RequestParam(value = "disclosureDate", required = false) String disclosureDate,
            @RequestParam(value = "patchDate", required = false) String patchDate,
            @RequestParam(value = "patchStatus", required = false) String patchStatus,
            @RequestParam(value = "patchDescription", required = false) String patchDescription,
            @RequestParam(value = "patchLink", required = false) String patchLink,
            @RequestParam(value = "cveNumber", required = false) String cveNumber,
            @RequestParam(value = "cnvdNumber", required = false) String cnvdNumber,
            @RequestParam(value = "cnnvdNumber", required = false) String cnnvdNumber,
            @RequestParam(value = "cvssScore", required = false) String cvssScore,
            @RequestParam(value = "impactScope", required = false) String impactScope,
            @RequestParam(value = "solutionSuggestions", required = false) String solutionSuggestions,
            @RequestParam(value = "referenceLink", required = false) String referenceLink,
            @RequestParam(value = "startDate", required = false) String startDate,
            @RequestParam(value = "endDate", required = false) String endDate,
            @RequestParam(value = "pageNo") Long pageNo,
            @RequestParam(value = "pageSize") Long pageSize
    ) {
        QueryWrapper<Vul> wrapper = new QueryWrapper<>();

        // 构建查询条件
        if (StringUtils.hasLength(systemOwnerOrg)) {
            wrapper.like("system_owner_org", systemOwnerOrg);
        }
        if (StringUtils.hasLength(systemName)) {
            wrapper.like("system_name", systemName);
        }
        if (StringUtils.hasLength(businessType)) {
            wrapper.eq("business_type", businessType);
        }
        if (StringUtils.hasLength(vulId)) {
            wrapper.like("vul_id", vulId);
        }
        if (StringUtils.hasLength(vulName)) {
            wrapper.like("vul_name", vulName);
        }
        if (StringUtils.hasLength(vulType)) {
            wrapper.eq("vul_type", vulType);
        }
        if (StringUtils.hasLength(vulLevel)) {
            wrapper.eq("vul_level", vulLevel);
        }
        if (StringUtils.hasLength(vulStatus)) {
            wrapper.eq("vul_status", vulStatus);
        }

        if (StringUtils.hasLength(vulDescription)) {
            wrapper.like("vul_description", vulDescription);
        }

        if (StringUtils.hasLength(cveNumber)) {
            wrapper.like("cve_number", cveNumber);
        }
        if (StringUtils.hasLength(cnvdNumber)) {
            wrapper.like("cnvd_number", cnvdNumber);
        }
        if (StringUtils.hasLength(cnnvdNumber)) {
            wrapper.like("cnnvd_number", cnnvdNumber);
        }
        if (StringUtils.hasLength(cvssScore)) {
            wrapper.eq("cvss_score", cvssScore);
        }
        if (StringUtils.hasLength(impactScope)) {
            wrapper.like("impact_scope", impactScope);
        }
        if (StringUtils.hasLength(solutionSuggestions)) {
            wrapper.like("solution_suggestions", solutionSuggestions);
        }
        if (StringUtils.hasLength(referenceLink)) {
            wrapper.like("reference_link", referenceLink);
        }
        // 处理日期范围查询（披露日期）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if (StringUtils.hasLength(startDate) && StringUtils.hasLength(endDate)) {
            try {
                LocalDate start = LocalDate.parse(startDate, formatter);
                LocalDate end = LocalDate.parse(endDate, formatter);
                wrapper.between("disclosure_date", start, end);
            } catch (DateTimeParseException e) {
                // 日期格式错误时忽略该条件
            }
        } else if (StringUtils.hasLength(startDate)) {
            try {
                LocalDate start = LocalDate.parse(startDate, formatter);
                wrapper.ge("disclosure_date", start);
            } catch (DateTimeParseException e) {
                // 日期格式错误时忽略该条件
            }
        } else if (StringUtils.hasLength(endDate)) {
            try {
                LocalDate end = LocalDate.parse(endDate, formatter);
                wrapper.le("disclosure_date", end);
            } catch (DateTimeParseException e) {
                // 日期格式错误时忽略该条件
            }
        }

        // 添加排序：按披露日期降序排列（时间从近到远）
        wrapper.orderByDesc("disclosure_date");
        
        try {
            Page<Vul> page = new Page<>(pageNo, pageSize);
            Page<Vul> vulPage = vulService.page(page, wrapper);

            Map<String, Object> data = new HashMap<>();
            data.put("vulList", vulPage.getRecords());
            data.put("total", vulPage.getTotal());
            data.put("pages", vulPage.getPages());

            return Result.success(data);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 新增漏洞
     */
    @PostMapping("/add")
    @OperationLog(
            moduleName = "漏洞管理",
            operationType = "新增",
            desc = "'新增漏洞：' + #data.vulName",
            isSensitive = true
    )
    public Result<?> addVul(@RequestBody Vul vul) {
        vulService.save(vul);
        return Result.success("新增成功");
    }

    /**
     * 删除漏洞
     */
    @DeleteMapping("/{id}")
    @OperationLog(
            moduleName = "漏洞管理",
            operationType = "删除",
            desc = "'删除漏洞：' + #data.vulName",
            dataId = "#id",
            isSensitive = true
    )
    public Result<?> delete(@PathVariable("id") Integer id) {
        Vul vul = vulService.getById(id);
        vulService.removeById(id);
        return Result.success(vul, "删除成功");
    }

    /**
     * 修改漏洞
     */
    @PutMapping("/update")
    @OperationLog(
            moduleName = "漏洞管理",
            operationType = "修改",
            desc = "'修改漏洞：' + #data.vulName"
    )
    public Result<?> update(@RequestBody Vul vul) {
        vulService.updateById(vul);
        return Result.success("修改成功");
    }

    /**
     * 查询单个漏洞
     */
    @GetMapping("/{id}")
    @OperationLog(
            moduleName = "漏洞管理",
            operationType = "查询",
            desc = "'查询漏洞：' + #data.vulName"
    )
    public Result<Vul> getVulById(@PathVariable("id") Integer id) {
        Vul vul = vulService.getById(id);
        return Result.success(vul);
    }

    /**
     * 查询所有漏洞
     */
    @GetMapping("/all")
    public Result<List<Vul>> getAllVul() {
        List<Vul> list = vulService.list();
        return Result.success(list);
    }

    /**
     * 导入漏洞Excel数据
     */
    @PostMapping("/import")
    @OperationLog(
            moduleName = "漏洞管理",
            operationType = "导入",
            desc = "导入漏洞数据"
    )
    @ResponseBody
    public Result<?> importExcel(@RequestPart("file") MultipartFile file) throws IOException {
        // 1. 读取Excel数据到VO列表
        List<VulExcelVO> voList = EasyExcel.read(file.getInputStream())
                .head(VulExcelVO.class)
                .sheet()
                .doReadSync();

        // 2. VO转Entity并处理字段转换
        List<Vul> entityList = voList.stream().map(vo -> {
            Vul entity = new Vul();

            // 基础字段映射
            entity.setSystemOwnerOrg(vo.getSystemOwnerOrg());
            entity.setSystemName(vo.getSystemName());
            entity.setBusinessType(vo.getBusinessType());
            entity.setVulId(vo.getVulId());
            entity.setVulName(vo.getVulName());
            entity.setVulType(vo.getVulType());
            entity.setVulLevel(vo.getVulLevel());
            entity.setVulStatus(vo.getVulStatus());
            entity.setVulDescription(vo.getVulDescription());
            entity.setCveNumber(vo.getCveNumber());
            entity.setCnvdNumber(vo.getCnvdNumber());
            entity.setCnnvdNumber(vo.getCnnvdNumber());
            entity.setImpactScope(vo.getImpactScope());
            entity.setSolutionSuggestions(vo.getSolutionSuggestions());
            entity.setReferenceLink(vo.getReferenceLink());

            // 日期转换（String -> LocalDate）
            if (StringUtils.hasLength(vo.getDisclosureDate())) {
                try {
                    entity.setDisclosureDate(LocalDate.parse(vo.getDisclosureDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                } catch (DateTimeParseException e) {
                    // 日期格式错误时忽略
                }
            }

            return entity;
        }).collect(Collectors.toList());

        // 3. 批量保存数据
        vulService.saveBatch(entityList);

        return Result.success(entityList);
    }

    /**
     * 导出漏洞数据
     */
    @GetMapping("/export")
    @OperationLog(
            moduleName = "漏洞管理",
            operationType = "导出",
            desc = "导出漏洞数据"
    )
    public void exportExcel(HttpServletResponse response,
                            @RequestParam(value = "systemOwnerOrg", required = false) String systemOwnerOrg,
                            @RequestParam(value = "systemName", required = false) String systemName,
                            @RequestParam(value = "businessType", required = false) String businessType,
                            @RequestParam(value = "vulId", required = false) String vulId,
                            @RequestParam(value = "vulName", required = false) String vulName,
                            @RequestParam(value = "vulType", required = false) String vulType,
                            @RequestParam(value = "vulLevel", required = false) String vulLevel,
                            @RequestParam(value = "vulStatus", required = false) String vulStatus,
                            @RequestParam(value = "vulDescription", required = false) String vulDescription,
                            @RequestParam(value = "disclosureDate", required = false) String disclosureDate,
                            @RequestParam(value = "patchDate", required = false) String patchDate,
                            @RequestParam(value = "patchStatus", required = false) String patchStatus,
                            @RequestParam(value = "patchDescription", required = false) String patchDescription,
                            @RequestParam(value = "cveNumber", required = false) String cveNumber,
                            @RequestParam(value = "cnvdNumber", required = false) String cnvdNumber,
                            @RequestParam(value = "cnnvdNumber", required = false) String cnnvdNumber,
                            @RequestParam(value = "cvssScore", required = false) String cvssScore,
                            @RequestParam(value = "patchSituation", required = false) String patchSituation,
                            @RequestParam(value = "impactScope", required = false) String impactScope,
                            @RequestParam(value = "solutionSuggestions", required = false) String solutionSuggestions,
                            @RequestParam(value = "referenceLink", required = false) String referenceLink
    ) throws IOException {
        // 设置响应格式和文件名
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("漏洞数据", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        // 构建查询条件
        QueryWrapper<Vul> wrapper = new QueryWrapper<>();
        if (StringUtils.hasLength(systemOwnerOrg)) {
            wrapper.eq("system_owner_org", systemOwnerOrg);
        }
        if (StringUtils.hasLength(systemName)) {
            wrapper.eq("system_name", systemName);
        }
        if (StringUtils.hasLength(businessType)) {
            wrapper.eq("business_type", businessType);
        }
        if (StringUtils.hasLength(vulId)) {
            wrapper.eq("vul_id", vulId);
        }
        if (StringUtils.hasLength(vulName)) {
            wrapper.eq("vul_name", vulName);
        }
        if (StringUtils.hasLength(vulType)) {
            wrapper.eq("vul_type", vulType);
        }
        if (StringUtils.hasLength(vulLevel)) {
            wrapper.eq("vul_level", vulLevel);
        }
        if (StringUtils.hasLength(vulStatus)) {
            wrapper.eq("vul_status", vulStatus);
        }
        if (StringUtils.hasLength(vulDescription)) {
            wrapper.eq("vul_description", vulDescription);
        }
        if (StringUtils.hasLength(cveNumber)) {
            wrapper.eq("cve_number", cveNumber);
        }
        if (StringUtils.hasLength(cnvdNumber)) {
            wrapper.eq("cnvd_number", cnvdNumber);
        }
        if (StringUtils.hasLength(cnnvdNumber)) {
            wrapper.eq("cnnvd_number", cnnvdNumber);
        }
        if (StringUtils.hasLength(cvssScore)) {
            wrapper.eq("cvss_score", cvssScore);
        }
        if (StringUtils.hasLength(patchSituation)) {
            wrapper.eq("patch_situation", patchSituation);
        }
        if (StringUtils.hasLength(impactScope)) {
            wrapper.eq("impact_scope", impactScope);
        }
        if (StringUtils.hasLength(solutionSuggestions)) {
            wrapper.eq("solution_suggestions", solutionSuggestions);
        }
        if (StringUtils.hasLength(referenceLink)) {
            wrapper.eq("reference_link", referenceLink);
        }
        // 获取数据并转换为VO
        List<VulExcelVO> voList = vulService.list(wrapper)
                .stream()
                .map(VulExcelVO::new)
                .collect(Collectors.toList());

        // 导出数据
        EasyExcel.write(response.getOutputStream(), VulExcelVO.class)
                .sheet("漏洞数据")
                .doWrite(voList);
    }

    /**
     * 漏洞统计分析
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getStatistics(
            @RequestParam(value = "systemOwnerOrg", required = false) String systemOwnerOrg,
            @RequestParam(value = "systemName", required = false) String systemName,
            @RequestParam(value = "startDate", required = false) String startDate,
            @RequestParam(value = "endDate", required = false) String endDate
    ) {
        Map<String, Object> statistics = vulService.getStatistics(
                systemOwnerOrg, systemName, startDate, endDate
        );
        return Result.success(statistics);
    }
}