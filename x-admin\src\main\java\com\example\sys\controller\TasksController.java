package com.example.sys.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.commom.vo.Result;
import com.example.sys.annotation.OperationLog;
import com.example.sys.entity.Tasks;
import com.example.sys.service.ITasksService;
import com.example.sys.entity.SysProtect;
import com.example.sys.service.ISysProtectService;
import com.example.sys.vo.SysProtectExcelVO;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/tasks")
public class TasksController {

    @Autowired
    private ITasksService tasksService;
    @Autowired
    private ISysProtectService sysProtectService;

    /**
     * 保存SysProtect数据并更新任务状态（复杂业务逻辑）
     * @param taskId 任务ID
     * @return 操作结果
     */
    @PostMapping("/saveComplexSysProtect")
    @OperationLog(
            moduleName = "系统保护管理",
            operationType = "新增/更新",
            desc = "处理系统保护记录：" + "#sysProtect.systemName",
            dataId = "#sysProtect.applicationId",
            isSensitive = true
    )
    @ResponseBody
    public Result<?> saveComplexSysProtect(@RequestBody Map<String, Object> param)  {
        Integer taskId = (Integer) param.get("taskId");

        System.out.println(taskId);
        try {
            Tasks task = tasksService.getById(taskId);
            if (task == null) {
                throw new RuntimeException("任务不存在，ID：" + taskId);
            }
            task.setTaskStatus("已完成");
            tasksService.updateById(task);
            return Result.success("操作成功");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.fail("操作失败：" + e.getMessage());
        }
    }

    @GetMapping("/list")
    public Result<Map<String, Object>> getTasksList(
            @RequestParam(value = "taskId", required = false) Integer taskId,
            @RequestParam(value = "taskStatus", required = false) String taskStatus,  // 修正状态字段名
            @RequestParam(value = "taskType", required = false) String taskType,  // 新增任务类型查询
            @RequestParam(value = "responsiblePerson", required = false) String responsiblePerson,  // 新增负责人查询
            @RequestParam(value = "createTime", required = false) LocalDateTime createTime,  // 新增创建时间查询
            @RequestParam(value = "endTime", required = false) LocalDateTime endTime,  // 新增结束时间查询
            @RequestParam(value = "systemOwnerOrg", required = false) String systemOwnerOrg,  // 新增所属组织查询
            @RequestParam(value = "systemName", required = false) String systemName,  // 新增系统名称查询
            @RequestParam(value = "systemStatus", required = false) String systemStatus,  // 新增系统状态查询
            @RequestParam(value = "pageNo", defaultValue = "1") Long pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Long pageSize
    ) {
        LambdaQueryWrapper<Tasks> wrapper = new LambdaQueryWrapper<>();

        // 修正查询条件为实际存在的字段
        if (taskId != null) {
            wrapper.eq(Tasks::getTaskId, taskId);
        }
        if (taskStatus != null) {
            wrapper.eq(StringUtils.hasLength(taskStatus),Tasks::getTaskStatus, taskStatus);  // 修正为taskStatus字段
        }
        if (taskType!= null) {
            wrapper.eq(StringUtils.hasLength(taskType),Tasks::getTaskType, taskType);  // 修正为taskType字段
        }
        if (responsiblePerson != null) {
            wrapper.like(StringUtils.hasLength(responsiblePerson),Tasks::getResponsiblePerson, responsiblePerson);
        }
        if (createTime != null) {
            wrapper.ge(Tasks::getCreateTime, createTime);  // 建议使用范围查询（大于等于创建时间）
        }
        if (endTime != null) {
            wrapper.le(Tasks::getEndTime, endTime);  // 建议使用范围查询（小于等于结束时间）
        }
        if (systemOwnerOrg!= null) {
            wrapper.like(StringUtils.hasLength(systemOwnerOrg),Tasks::getSystemOwnerOrg, systemOwnerOrg);
        }
        if (systemName!= null) {
            wrapper.like(StringUtils.hasLength(systemName),Tasks::getSystemName, systemName);
        }
        if (systemStatus!= null) {
            wrapper.eq(StringUtils.hasLength(systemStatus),Tasks::getSystemStatus, systemStatus);  // 修正为systemStatus字段
        }

        Page<Tasks> page = new Page<>(pageNo, pageSize);
        Page<Tasks> tasksPage = tasksService.page(page, wrapper);

        Map<String, Object> data = new HashMap<>();
        data.put("tasks", tasksPage.getRecords());
        data.put("total", tasksPage.getTotal());
        data.put("pages", tasksPage.getPages());

        return Result.success(data);
    }

    //测评报告导入
    @PostMapping("/importReport")
    @OperationLog(
            moduleName = "任务管理",
            operationType = "测评报告导入",
            desc = "导入PDF测评报告"
    )
    @ResponseBody
    public Result<?> importPdf(@RequestPart("file") MultipartFile file) throws IOException {
        // 1. 读取PDF内容
        String pdfContent = readPdfContent(file.getInputStream());

        // 2. 解析PDF内容并提取关键信息
        SysProtect entity = parsePdfContent(pdfContent);

        System.out.println(pdfContent);
        System.out.println(entity);
        System.out.println(Arrays.asList(entity));
        // 3. 返回解析结果
        return Result.success(Arrays.asList(entity));
    }

    private String readPdfContent(InputStream inputStream) throws IOException {
        PDDocument document = PDDocument.load(inputStream);
        PDFTextStripper pdfStripper = new PDFTextStripper();
        String text = pdfStripper.getText(document);
        document.close();
        return text;
    }

    private SysProtect parsePdfContent(String pdfContent) {
        SysProtect entity = new SysProtect();

        // 使用正则表达式提取关键信息
        extractField(pdfContent, "所属单位[:：]\\s*(.*?)\\s*\\n", entity::setSystemOwnerOrg);
        extractField(pdfContent, "成员单位[:：]\\s*(.*?)\\s*\\n", entity::setMemberUnit);
        extractField(pdfContent, "系统名称[:：]\\s*(.*?)\\s*\\n", entity::setSystemName);
        extractField(pdfContent, "系统简称[:：]\\s*(.*?)\\s*\\n", entity::setSystemShortName);
        extractField(pdfContent, "网络归属[:：]\\s*(.*?)\\s*\\n", entity::setNetworkBelonging);
        extractField(pdfContent, "业务类型[:：]\\s*(.*?)\\s*\\n", entity::setBusinessType);
        extractField(pdfContent, "备案号[:：]\\s*(.*?)\\s*\\n", entity::setFilingNumber);
        extractField(pdfContent, "定级级别[:：]\\s*(.*?)\\s*\\n", entity::setClassificationLevel);
        extractField(pdfContent, "测评时间[:：]\\s*(.*?)\\s*\\n", entity::setEvaluationTime);
        extractField(pdfContent, "测评结果[:：]\\s*(.*?)\\s*\\n", entity::setEvaluationResult);
        extractField(pdfContent, "测评单位[:：]\\s*(.*?)\\s*\\n", entity::setEvaluationOrganization);
        extractField(pdfContent, "测评状态[:：]\\s*(.*?)\\s*\\n", entity::setEvaluationStatus);
        extractField(pdfContent, "系统状态[:：]\\s*(.*?)\\s*\\n", entity::setSystemStatus);

        // 解析问题清单部分
        parseIssuesList(pdfContent, entity);

        return entity;
    }

    private void extractField(String content, String regexPattern, Consumer<String> setter) {
        Pattern pattern = Pattern.compile(regexPattern);
        Matcher matcher = pattern.matcher(content);
        if (matcher.find()) {
            setter.accept(matcher.group(1).trim());
        }
    }

    private void parseIssuesList(String content, SysProtect entity) {
        // 解析分类
        extractField(content, "分类[:：]\\s*(.*?)\\s*\\n", entity::setCategory);
        extractField(content, "控制点[:：]\\s*(.*?)\\s*\\n", entity::setControlPoint);
        extractField(content, "测评项[:：]\\s*(.*?)\\s*\\n", entity::setEvaluationItem);
        extractField(content, "符合情况[:：]\\s*(.*?)\\s*\\n", entity::setComplianceStatus);
        extractField(content, "结果记录[:：]\\s*(.*?)\\s*\\n", entity::setResultRecord);

        // 自动填充plannedEvaluationTime逻辑（与Excel导入相同）
        String classificationLevel = entity.getClassificationLevel();
        String evaluationTimeStr = entity.getEvaluationTime();

        if (classificationLevel != null && evaluationTimeStr != null && !evaluationTimeStr.isEmpty()) {
            try {
                LocalDate evaluationDate = LocalDate.parse(evaluationTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                LocalDate plannedDate = null;

                if ("三级".equals(classificationLevel)) {
                    plannedDate = evaluationDate.plusMonths(8);
                } else if ("二级".equals(classificationLevel)) {
                    plannedDate = evaluationDate.plusMonths(20);
                }

                if (plannedDate != null) {
                    entity.setPlannedEvaluationTime(plannedDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                }
            } catch (DateTimeParseException e) {
                // 日期格式错误时忽略
            }
        }
    }

    //问题清单导入
    @PostMapping("/importIssues")
    @OperationLog(
            moduleName = "任务管理",
            operationType = "问题清单导入",
            desc = "导入问题清单"
    )
    @ResponseBody
    public Result<?> importExcel(@RequestPart("file") MultipartFile file) throws IOException {
        // 1. 读取Excel数据到VO列表
        List<SysProtectExcelVO> voList = EasyExcel.read(file.getInputStream())
                .head(SysProtectExcelVO.class)
                .sheet()
                .doReadSync();

        // 2. VO转Entity并添加自动填充逻辑
        List<SysProtect> entityList = voList.stream().map(vo -> {
            SysProtect entity = new SysProtect();

            // 手动映射基础字段
            entity.setSystemOwnerOrg(vo.getSystemOwnerOrg());
            entity.setMemberUnit(vo.getMemberUnit());
            entity.setSystemName(vo.getSystemName());
            entity.setSystemShortName(vo.getSystemShortName());
            entity.setNetworkBelonging(vo.getNetworkBelonging());
            entity.setBusinessType(vo.getBusinessType());
            entity.setFilingNumber(vo.getFilingNumber());
            entity.setClassificationLevel(vo.getClassificationLevel());
            entity.setEvaluationTime(vo.getEvaluationTime());
            entity.setEvaluationResult(vo.getEvaluationResult());
            entity.setEvaluationOrganization(vo.getEvaluationOrganization());
            entity.setEvaluationStatus(vo.getEvaluationStatus());
            entity.setSystemStatus(vo.getSystemStatus());
            entity.setCategory(vo.getCategory());
            entity.setControlPoint(vo.getControlPoint());
            entity.setEvaluationItem(vo.getEvaluationItem());
            entity.setComplianceStatus(vo.getComplianceStatus());
            entity.setResultRecord(vo.getResultRecord());


            // ------------------- 自动填充plannedEvaluationTime逻辑 -------------------
            String plannedEvaluationTime = vo.getPlannedEvaluationTime();
            String classificationLevel = vo.getClassificationLevel();
            String evaluationTimeStr = vo.getEvaluationTime();

            // 当plannedEvaluationTime为空时执行自动填充
            if (plannedEvaluationTime == null || plannedEvaluationTime.trim().isEmpty()) {
                if (classificationLevel != null && evaluationTimeStr != null && !evaluationTimeStr.isEmpty()) {
                    try {
                        // 解析日期（假设格式为yyyy-MM-dd，可根据实际情况调整格式）
                        LocalDate evaluationDate = LocalDate.parse(evaluationTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd")); // 调整格式
                        LocalDate plannedDate;

                        // 根据等级计算时间
                        if ("三级".equals(classificationLevel)) {
                            plannedDate = evaluationDate.plusMonths(8);
                        } else if ("二级".equals(classificationLevel)) {
                            plannedDate = evaluationDate.plusMonths(20);
                        } else {
                            // 非二级/三级不处理（保持为空）
                            plannedDate = null;
                        }

                        // 填充计算后的时间
                        if (plannedDate != null) {
                            entity.setPlannedEvaluationTime(plannedDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        }
                    } catch (DateTimeParseException e) {
                        // 日期格式错误时抛出异常（终止当前数据导入）
                        throw new RuntimeException("evaluationTime格式错误，需为yyyy-MM-dd格式", e);
                    }
                }
            } else {
                // 非空时直接赋值
                entity.setPlannedEvaluationTime(plannedEvaluationTime);
            }

            return entity;
        }).collect(Collectors.toList());

        // 3. 批量保存数据
        //sysProtectService.saveBatch(entityList);

        return Result.success(entityList);
    }


    // 新增任务
    @PostMapping("/add")
    @OperationLog(
            moduleName = "任务管理",
            operationType = "新增",
            desc = "'新增任务：' + #tasks.systemName",  // 修正为实际存在的systemName字段
            dataId = "#tasks.taskId",
            isSensitive = true
    )
    public Result<Tasks> addTask(@RequestBody Tasks tasks) {
        tasksService.save(tasks);
        return Result.success(tasks, "新增成功");
    }

    // 删除任务（逻辑删除）
    @DeleteMapping("/{taskId}")
    @OperationLog(
            moduleName = "任务管理",
            operationType = "删除",
            desc = "'删除任务：ID=' + #taskId",  // 直接使用ID更安全
            dataId = "#taskId",
            isSensitive = true
    )
    public Result<?> delete(@PathVariable("taskId") Integer taskId) {
        tasksService.removeById(taskId);  // 配合@TableLogic自动实现逻辑删除
        return Result.success("删除成功");
    }

    // 修改任务
    @PutMapping("/update")
    @OperationLog(
            moduleName = "任务管理",
            operationType = "修改",
            desc = "'修改任务：' + #tasks.systemName + '（ID=' + #tasks.taskId + '）'",  // 使用systemName+ID更清晰
            dataId = "#tasks.taskId"
    )
    public Result<Tasks> update(@RequestBody Tasks tasks) {
        tasksService.updateById(tasks);
        return Result.success(tasks, "修改成功");
    }
}
