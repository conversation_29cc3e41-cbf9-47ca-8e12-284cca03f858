<template>
    <el-card>
      <h2>系统总览</h2>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <h3>系统总数</h3>
            <p>{{ summaryData.totalSystems }}</p>
          </el-card>
        </el-col>
  
        <el-col :span="12">
          <el-card>
            <h3>漏洞总数</h3>
            <p>{{ summaryData.totalVulnerabilities }}</p>
          </el-card>
        </el-col>
      </el-row>
  
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="24">
          <el-card>
            <h3>漏洞分类</h3>
            <el-table :data="vulnerabilities">
              <el-table-column prop="level" label="级别"></el-table-column>
              <el-table-column prop="count" label="数量"></el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
  
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="24">
          <el-card>
            <h3>任务状态分类</h3>
            <el-table :data="taskStatuses">
              <el-table-column prop="status" label="状态"></el-table-column>
              <el-table-column prop="count" label="数量"></el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </el-card>
  </template>
  
  <script>
  export default {
    name: 'SystemSummary',
    props: ['summaryData'],
    computed: {
      vulnerabilities() {
        return Object.entries(this.summaryData.vulnerabilities).map(([level, count]) => ({
          level,
          count
        }));
      },
      taskStatuses() {
        return Object.entries(this.summaryData.taskStatuses).map(([status, count]) => ({
          status,
          count
        }));
      }
    }
  };
  </script>
  