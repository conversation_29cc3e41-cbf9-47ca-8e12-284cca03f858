package com.example.sys.service;

import com.example.sys.entity.Logs;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 用于记录管理系统中各种操作的详细日志信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-14
 */
public interface ILogsService extends IService<Logs> {
    void saveLog(Logs log);

    Map<String, Object> getLogsList(Long pageNo,
                                Long pageSize,
                                Integer id,
                                String username,
                                String realname,
                                String moduleName,
                                String operationType,
                                String operationDescription,
                                String operationIp,
                                String operationResult,
                                Boolean isSensitive);
    // 新增导出专用方法，包含过滤条件
    List<Logs> exportLogsList(
        Boolean isSensitive,
        LocalDateTime startTime,
        LocalDateTime endTime,
        String username,
        String realname
    );
}
