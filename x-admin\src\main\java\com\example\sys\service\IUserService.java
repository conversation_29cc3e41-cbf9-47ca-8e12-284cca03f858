package com.example.sys.service;

import com.example.commom.vo.Result;
import com.example.sys.entity.User;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
public interface IUserService extends IService<User> {

    Map<String, Object> login(User user);

    Map<String, Object> getUserInfo(String token);

    void logout(String token);

    List<String> getEmailsByOrganization(String organization);

    List<String> getUserNamesByOrganization(String organization);

}
