<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header">
        <span class="card-title">操作日志列表</span>
      </div>

      <!-- 搜索表单 -->
      <el-form :model="queryForm" :inline="true" class="search-form" size="small" label-width="85px">
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item label="日志ID">
              <el-input v-model="queryForm.id" placeholder="请输入日志ID" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="操作用户名">
              <el-input v-model="queryForm.username" placeholder="请输入操作用户名" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="操作人姓名">
              <el-input v-model="queryForm.realname" placeholder="请输入操作人姓名" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="模块名称">
              <el-input v-model="queryForm.moduleName" placeholder="请输入模块名称" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item label="操作类型">
              <el-input v-model="queryForm.operationType" placeholder="请输入操作类型" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="操作描述">
              <el-input v-model="queryForm.operationDescription" placeholder="请输入操作描述" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="操作IP">
              <el-input v-model="queryForm.operationIp" placeholder="请输入操作IP" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="操作结果">
              <el-input v-model="queryForm.operationResult" placeholder="请输入操作结果" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item label="敏感操作">
              <el-select v-model="queryForm.isSensitive" placeholder="是否为敏感操作" clearable style="width: 100%">
                <el-option label="是" value="true"></el-option>
                <el-option label="否" value="false"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24" style="text-align: center; margin-top: 20px;">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 数据表格 -->
      <el-table
          v-loading="loading"
          :data="tableData"
          border
          style="width: 100%; margin-top: 20px;"
          :header-cell-style="{ background: '#f5f7fa' }"
      >
        <el-table-column prop="id" label="日志ID" width="80" align="center" />
        <el-table-column prop="username" label="操作用户名" min-width="120" align="center" />
        <el-table-column prop="realname" label="操作人姓名" min-width="120" align="center" />
        <el-table-column prop="moduleName" label="模块名称" min-width="120" align="center" />
        <el-table-column prop="operationType" label="操作类型" min-width="120" align="center" />
        <el-table-column prop="operationDescription" label="操作描述" min-width="120" align="center" />
        <el-table-column prop="operationIp" label="操作IP" min-width="120" align="center" />
        <el-table-column prop="operationResult" label="操作结果" min-width="120" align="center" />
        <el-table-column prop="isSensitive" label="是否敏感操作" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isSensitive === 'true' ? 'success' : 'info'">
              {{ scope.row.isSensitive === 'true' ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParams.pageNo"
          :page-sizes="[5, 10, 20, 40]"
          :page-size="queryParams.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
      />
    </el-card>
  </div>
</template>

<script>
import * as logOperation from '@/api/logOperation'

export default {
  name: 'LogOperationIndex',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 5
      },
      // 查询表单
      queryForm: {
        id: '',
        username: '',
        realname: '',
        moduleName: '',
        operationType: '',
        operationDescription: '',
        operationIp: '',
        operationResult: '',
        isSensitive: ''
      },
      // 表格数据
      tableData: []
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 获取列表数据
    getList() {
      this.loading = true;
      logOperation.listLogs(this.queryParams)
          .then(response => {
            console.log('API响应数据:', response);
            if (response?.data) {
              this.tableData = response.data.logs || [];
              this.total = response.data.total || 0;
            } else {
              this.tableData = [];
              this.total = 0;
            }
          })
          .catch(error => {
            console.error('获取数据失败:', error);
            this.$message.error('获取数据失败');
          })
          .finally(() => {
            this.loading = false;
          });
    },

    // 处理查询
    handleQuery() {
      this.queryParams.pageNo = 1;
      // 合并查询表单数据到查询参数
      this.queryParams = {
        ...this.queryParams,
        ...this.queryForm
      };
      this.getList();
    },

    // 处理重置
    handleReset() {
      this.queryForm = {
        id: '',
        username: '',
        realname: '',
        moduleName: '',
        operationType: '',
        operationDescription: '',
        operationIp: '',
        operationResult: '',
        isSensitive: ''
      };
      this.queryParams = {
        pageNo: 1,
        pageSize: this.queryParams.pageSize
      };
      this.getList();
    },

    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.getList();
    },

    handleCurrentChange(val) {
      this.queryParams.pageNo = val;
      this.getList();
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f0f2f5;
}

.box-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .card-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }
}

.search-form {
  padding: 20px 0;

  .el-form-item {
    margin-bottom: 20px;
    width: 100%;

    .el-form-item__label {
      color: #606266;
      font-weight: 500;
      padding-right: 12px;
      text-align: right;
    }

    .el-form-item__content {
      width: calc(100% - 85px); // 减去label的宽度
    }
  }

  .el-input, .el-select, .el-date-picker {
    width: 100%;
  }
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 20px;
}

.el-button {
  padding: 8px 15px;
  margin: 0 8px;
}

.el-table {
  margin-top: 20px;

  th {
    background-color: #f5f7fa !important;
    color: #606266;
    font-weight: 500;
  }
}

// 响应式布局
@media screen and (max-width: 1400px) {
  .el-col {
    width: 100% !important;
    margin-bottom: 10px;
  }
}
</style>
