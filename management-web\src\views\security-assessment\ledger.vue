<template>
  <div class="app-container">
    <!-- 顶部筛选区域 -->
    <el-card class="box-card">
      <div slot="header">
        <span class="card-title">等保概览</span>
      </div>
      <div class="filter-container">
        <el-form :model="queryForm" :inline="true" class="search-form" size="small" label-width="85px">
          <el-row :gutter="16" align="middle">
            <el-col :span="6">
              <el-form-item label="所属单位">
                <el-select v-model="queryForm.systemOwnerOrg" placeholder="请选择所属单位" clearable style="width: 100%">
                  <el-option label="中国核电" value="中国核电"></el-option>
                  <el-option label="秦山核电" value="秦山核电"></el-option>
                  <el-option label="江苏核电" value="江苏核电"></el-option>
                  <el-option label="福清核电" value="福清核电"></el-option>
                  <el-option label="海南核电" value="海南核电"></el-option>
                  <el-option label="三门核电" value="三门核电"></el-option>
                  <el-option label="霞浦核电" value="霞浦核电"></el-option>
                  <el-option label="漳州核电" value="漳州核电"></el-option>
                  <el-option label="中核武汉" value="中核武汉"></el-option>
                  <el-option label="中核能源" value="中核能源"></el-option>
                  <el-option label="运行研究院" value="运行研究院"></el-option>
                  <el-option label="研究运维" value="研究运维"></el-option>
                  <el-option label="辽宁核电" value="辽宁核电"></el-option>
                  <el-option label="中核山东" value="中核山东"></el-option>
                  <el-option label="中核储能" value="中核储能"></el-option>
                  <el-option label="中核苏能" value="中核苏能"></el-option>
                  <el-option label="中核海得" value="中核海得"></el-option>
                  <el-option label="河北核电" value="河北核电"></el-option>
                  <el-option label="庄河核电" value="庄河核电"></el-option>
                  <el-option label="中核光电" value="中核光电"></el-option>
                  <el-option label="桃花江核电" value="桃花江核电"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item label="测评时间">
                <el-date-picker
                    v-model="queryForm.assessmentTimeRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd"
                    style="width: 100%">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="系统名称">
                <el-input v-model="queryForm.systemName" placeholder="请输入系统名称" clearable style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24" style="text-align: center; margin-top: 10px;">
              <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
              <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>
    <!-- 数据表格 -->
    <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%; margin-top: 20px;"
        :header-cell-style="{ background: '#f5f7fa' }"
    >
      <el-table-column prop="systemOwnerOrg" label="系统等级" min-width="120" align="center">
        <template slot-scope="scope">
          {{ ['一级', '二级', '三级', '四级', '五级', '总计'][scope.$index] }}
        </template>
      </el-table-column>
      <el-table-column prop="中国核电" label="中国核电" min-width="120" align="center" />
      <el-table-column prop="秦山核电" label="秦山核电" min-width="120" align="center" />
      <el-table-column prop="江苏核电" label="江苏核电" min-width="120" align="center" />
      <el-table-column prop="福清核电" label="福清核电" min-width="120" align="center" />
      <el-table-column prop="海南核电" label="海南核电" min-width="120" align="center" />
      <el-table-column prop="三门核电" label="三门核电" min-width="120" align="center" />
      <el-table-column prop="霞浦核电" label="霞浦核电" min-width="120" align="center" />
      <el-table-column prop="漳州核电" label="漳州核电" min-width="120" align="center" />
      <el-table-column prop="中核武汉" label="中核武汉" min-width="120" align="center" />
      <el-table-column prop="中核能源" label="中核能源" min-width="120" align="center" />
      <el-table-column prop="运行研究院" label="运行研究院" min-width="120" align="center" />
      <el-table-column prop="研究运维" label="研究运维" min-width="120" align="center" />
      <el-table-column prop="辽宁核电" label="辽宁核电" min-width="120" align="center" />
      <el-table-column prop="中核山东" label="中核山东" min-width="120" align="center" />
      <el-table-column prop="中核储能" label="中核储能" min-width="120" align="center" />
      <el-table-column prop="中核苏能" label="中核苏能" min-width="120" align="center" />
      <el-table-column prop="中核海得" label="中核海得" min-width="120" align="center" />
      <el-table-column prop="河北核电" label="河北核电" min-width="120" align="center" />
      <el-table-column prop="庄河核电" label="庄河核电" min-width="120" align="center" />
      <el-table-column prop="中核光电" label="中核光电" min-width="120" align="center" />
      <el-table-column prop="桃花江核电" label="桃花江核电" min-width="120" align="center" />
    </el-table>
    <!-- 数据统计图表区域 -->
    <div class="charts-container">
      <!-- 上部分图表 -->
      <div class="charts-row-top">
        <!-- 左侧柱状图 -->
        <el-card class="box-card chart-item-large">
          <div slot="header">
            <span>控制点合规分布TOP10</span>
          </div>
          <div ref="barChart" style="height: 400px"></div>
        </el-card>
        <!-- 右侧折线图 -->
        <el-card class="box-card chart-item-large">
          <div slot="header">
            <span>测评项目年度分布趋势</span>
          </div>
          <div ref="lineChart" style="height: 400px"></div>
        </el-card>
      </div>

      <!-- 下部分三个图表 -->
      <div class="charts-row">
        <!-- 测评结果分布饼图 -->
        <el-card class="box-card chart-item">
          <div slot="header">
            <span>测评结果分布</span>
          </div>
          <div ref="pieChart" style="height: 300px"></div>
        </el-card>

        <!-- 业务类型圆环图 -->
        <el-card class="box-card chart-item">
          <div slot="header">
            <span>业务类型</span>
          </div>
          <div ref="doughnutChart" style="height: 300px"></div>
        </el-card>

        <!-- 等级数量显示图 -->
        <el-card class="box-card chart-item">
          <div slot="header">
            <span>等级数量显示</span>
          </div>
          <div ref="stackBarChart" style="height: 300px"></div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import * as securityAssessment from '@/api/securityAssessment'

export default {
  name: 'SecurityAssessmentLedger',
  data() {
    return {
      queryForm: {
        systemOwnerOrg: '',
        assessmentTimeRange: [],
        systemName: ''
      },
      tableData: [],
      charts: {
        bar: null,
        line: null,
        pie: null,
        doughnut: null,
        stackBar: null
      },
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        dateRange: []
      },
      loading: false
    }
  },
  mounted() {
    this.initCharts()
    this.getList(true) // 首次加载页面时更新表格数据
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    Object.keys(this.charts).forEach(key => {
      if (this.charts[key]) {
        this.charts[key].dispose()
      }
    })
  },
  methods: {
    getList(updateTable = false) {
      this.loading = true
      const params = {
        systemOwnerOrg: this.queryForm.systemOwnerOrg,
        startTime: this.queryForm.assessmentTimeRange && this.queryForm.assessmentTimeRange[0] ? this.queryForm.assessmentTimeRange[0] : '',
        endTime: this.queryForm.assessmentTimeRange && this.queryForm.assessmentTimeRange[1] ? this.queryForm.assessmentTimeRange[1] : '',
        systemName: this.queryForm.systemName
      }
      console.log('请求参数:', params)
      securityAssessment.getSysProtectSta(params)
          .then(response => {
            const data = response.data
            console.log('响应数据:', data)
            this.updateCharts(data)
            if (updateTable) {
              this.updateTableData(data)
            }
            this.loading = false
          })
          .catch(error => {
            console.error('请求出错:', error)
            this.loading = false
            this.$message.error('数据加载失败，请检查网络连接')
          })
    },
    handleResize() {
      Object.keys(this.charts).forEach(key => {
        if (this.charts[key]) {
          this.charts[key].resize()
        }
      })
    },
    initCharts() {
      this.initBarChart()
      this.initLineChart()
      this.initPieChart()
      this.initDoughnutChart()
      this.initStackBarChart()
    },
    initBarChart() {
      this.charts.bar = echarts.init(this.$refs.barChart)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLabel: {
            interval: 0,
            rotate: 30
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '数量',
          type: 'bar',
          barWidth: '40%',
          data: [],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 1, color: '#188df0' }
            ])
          },
          emphasis: {
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#2378f7' },
                { offset: 0.7, color: '#2378f7' },
                { offset: 1, color: '#83bff6' }
              ])
            }
          }
        }]
      }
      this.charts.bar.setOption(option)
    },
    initLineChart() {
      this.charts.line = echarts.init(this.$refs.lineChart)
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: []
        },
        yAxis: {
          type: 'value',
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: [{
          name: '测评项目数',
          type: 'line',
          smooth: true,
          data: [],
          symbolSize: 8,
          lineStyle: {
            width: 4,
            color: '#409EFF'
          },
          itemStyle: {
            color: '#409EFF'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(64,158,255,0.3)' },
              { offset: 1, color: 'rgba(64,158,255,0.1)' }
            ])
          }
        }]
      }
      this.charts.line.setOption(option)
    },
    initPieChart() {
      this.charts.pie = echarts.init(this.$refs.pieChart)
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'horizontal',
          bottom: 10,
          data: []
        },
        grid: {
          top: '5%',    // 增加顶部边距，将图表整体下移
          bottom: '20%' // 增加底部边距，为图例留出更多空间
        },
        series: [{
          name: '测评结果分布',
          type: 'pie',
          radius: '55%',
          center: ['50%', '50%'],
          data: [],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      }
      this.charts.pie.setOption(option)
    },
    initDoughnutChart() {
      this.charts.doughnut = echarts.init(this.$refs.doughnutChart)
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'horizontal',
          bottom: 10, // 保持图例在底部
          data: []
        },
        grid: {
          top: '5%',    // 增加顶部边距，将图表整体下移
          bottom: '20%' // 增加底部边距，为图例留出更多空间
        },
        series: [{
          name: '业务类型',
          type: 'pie',
          radius: ['40%', '65%'], // 适当调整圆环大小，使中间空心部分更小
          center: ['50%', '40%'], // 将圆环位置上移
          avoidLabelOverlap: false,
          label: {
            show: true,
            position: 'inside',
            formatter: '{c}'
          },
          data: []
        }]
      }
      this.charts.doughnut.setOption(option)
    },
    initStackBarChart() {
      this.charts.stackBar = echarts.init(this.$refs.stackBarChart)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: [],
          bottom: 10
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '20%',
          containLabel: true,
          top: '5%'    // 增加顶部边距，将图表整体下移
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: []
      }
      this.charts.stackBar.setOption(option)
    },
    handleQuery() {
      this.getList(false) // 查询时不更新表格数据
    },
    handleReset() {
      this.queryForm = {
        systemOwnerOrg: '',
        assessmentTimeRange: [],
        systemName: ''
      }
      this.getList(false) // 重置时不更新表格数据
    },
    updateCharts(data) {
      // 更新柱状图（控制点合规分布TOP10）
      if (data.controlPointStatistics) {
        const barOption = this.charts.bar.getOption()
        const top10 = Object.entries(data.controlPointStatistics)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10)
        barOption.xAxis[0].data = top10.map(item => item[0])
        barOption.series[0].data = top10.map(item => item[1])
        this.charts.bar.setOption(barOption)
      }
      // 更新折线图（测评项目年度分布趋势）
      if (data.evaluationYearStatistics) {
        const lineOption = this.charts.line.getOption()

        // 获取当前年份，并生成近三年的年份数组
        const currentYear = new Date().getFullYear()
        const recentYears = [currentYear - 2, currentYear - 1, currentYear].map(String)

        // 构建包含近三年数据的数组，缺失年份补0
        const yearData = recentYears.map(year => {
          return data.evaluationYearStatistics[year] || 0
        })

        lineOption.xAxis[0].data = recentYears
        lineOption.series[0].data = yearData
        this.charts.line.setOption(lineOption)
      }
      // 更新饼图（测评结果分布）
      if (data.evaluationResultStatistics) {
        const pieOption = this.charts.pie.getOption()
        pieOption.legend[0].data = Object.keys(data.evaluationResultStatistics)
        pieOption.series[0].data = Object.entries(data.evaluationResultStatistics).map(item => ({ value: item[1], name: item[0] }))
        this.charts.pie.setOption(pieOption)
      }
      // 更新圆环图（业务类型）
      if (data.businessTypeStatistics) {
        const doughnutOption = this.charts.doughnut.getOption()
        doughnutOption.legend[0].data = Object.keys(data.businessTypeStatistics)
        doughnutOption.series[0].data = Object.entries(data.businessTypeStatistics).map(item => ({ value: item[1], name: item[0] }))
        this.charts.doughnut.setOption(doughnutOption)
      }
      // 更新堆叠柱状图（等级数量显示）
      if (data.orgClassificationStatistics) {
        const stackBarOption = this.charts.stackBar.getOption()
        const levels = ['一级', '二级', '三级', '四级', '五级']
        const orgs = Object.keys(data.orgClassificationStatistics)
        stackBarOption.xAxis[0].data = orgs
        stackBarOption.legend[0].data = levels
        stackBarOption.series = levels.map(level => ({
          name: level,
          type: 'bar',
          stack: 'total',
          data: orgs.map(org => data.orgClassificationStatistics[org][level] || 0)
        }))
        this.charts.stackBar.setOption(stackBarOption)
      }
    },
    updateTableData(data) {
      const levels = ['一级', '二级', '三级', '四级', '五级'];
      const orgs = [
        '中国核电', '秦山核电', '江苏核电', '福清核电', '海南核电',
        '三门核电', '霞浦核电', '漳州核电', '中核武汉', '中核能源',
        '运行研究院', '研究运维', '辽宁核电', '中核山东', '中核储能',
        '中核苏能', '中核海得', '河北核电', '庄河核电', '中核光电', '桃花江核电'
      ];

      // 生成一到五级的数据行
      const levelRows = levels.map(level => {
        const row = { systemOwnerOrg: level };
        orgs.forEach(org => {
          row[org] = data.orgClassificationStatistics && data.orgClassificationStatistics[org] ? data.orgClassificationStatistics[org][level] || 0 : 0;
        });
        return row;
      });

      // 生成总计行
      const totalRow = { systemOwnerOrg: '总计' };
      orgs.forEach(org => {
        // 计算每个单位的总计
        const total = levels.reduce((sum, level) => {
          return sum + (data.orgClassificationStatistics && data.orgClassificationStatistics[org] ? data.orgClassificationStatistics[org][level] || 0 : 0);
        }, 0);
        totalRow[org] = total;
      });

      // 合并数据
      this.tableData = [...levelRows, totalRow];
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f0f2f5;
}

.box-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .card-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }
}

.filter-container {
  padding: 10px 0 0 0;
}

.search-form {
  .el-form-item {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
  }
  .el-input, .el-select, .el-date-picker, .el-date-editor {
    width: 100%;
    height: 36px !important;
    line-height: 36px !important;
    display: block !important;
    vertical-align: middle !important;
    box-sizing: border-box;
  }
  .el-input__inner {
    height: 36px !important;
    line-height: 36px !important;
    box-sizing: border-box;
    display: block !important;
    vertical-align: middle !important;
  }
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    height: 36px !important;
    line-height: 36px !important;
    box-sizing: border-box;
    display: block !important;
    vertical-align: middle !important;
  }
}

.charts-container {
  margin-top: 20px;
}

.charts-row-top {
  display: flex;
  justify-content: space-between;
  margin: 0 -10px 20px;
}

.charts-row {
  display: flex;
  justify-content: space-between;
  margin: 0 -10px;
}

.chart-item {
  flex: 1;
  margin: 0 10px;
  transition: all 0.3s;
}

.chart-item-large {
  flex: 1;
  margin: 0 10px;
  transition: all 0.3s;
}

.chart-item:hover,
.chart-item-large:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.box-card :deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  font-weight: bold;
  color: #303133;
}

.box-card :deep(.el-card__body) {
  padding: 20px;
}

/* 响应式布局 */
@media screen and (max-width: 1400px) {
  .charts-row {
    flex-wrap: wrap;
  }

  .chart-item {
    flex: 1 1 calc(50% - 20px);
    margin-bottom: 20px;
  }
}

@media screen and (max-width: 768px) {
  .charts-row-top {
    flex-direction: column;
  }

  .chart-item-large {
    margin-bottom: 20px;
  }

  .chart-item {
    flex: 1 1 100%;
  }
}

/* 日期范围选择器样式优化 */
:deep(.el-date-editor--daterange) {
  .el-range-separator {
    padding: 0 5px;
  }

  .el-range__icon {
    margin-top: -2px;
  }
}
</style>
