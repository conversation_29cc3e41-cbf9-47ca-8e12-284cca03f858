package com.example.sys.service;

import com.example.sys.service.IMailService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import java.util.Arrays;

@SpringBootTest // 加载完整 Spring 上下文（包括邮件发送器 Bean）
@TestPropertySource(properties = {
        // 邮件服务器配置（根据你的邮箱服务商修改）
        "spring.mail.username=<EMAIL>",   // 替换为你的发件人邮箱
        "spring.mail.password=your-auth-code",       // 替换为邮箱授权码
        "spring.mail.host=smtp.163.com",             // 163 邮箱 SMTP 地址
        "spring.mail.port=465",                      // SSL 端口
        "spring.mail.properties.mail.smtp.ssl.enable=true" // 启用 SSL
})
public class MailServiceIntegrationTest {

    @Autowired
    private IMailService mailService; // 注入真实的邮件服务实现

    // 测试收件邮箱（建议使用临时邮箱，避免干扰真实业务）
    private static final String TEST_RECIPIENT = "<EMAIL>";

    @Test
    public void testRealSendMail() {
        // 邮件内容
        String subject = "集成测试：真实邮件发送";
        String content = "这是通过集成测试发送的真实邮件，请勿回复。";

        // 调用真实发送方法
        mailService.sendMail(TEST_RECIPIENT, subject, content);

        // 无异常即表示发送成功（可添加日志或等待时间后手动检查邮箱）
        System.out.println("真实邮件发送测试完成，请检查收件箱（包括垃圾邮件箱）！");
    }

    @Test
    public void testRealSendBatchMail() {
        // 批量收件人列表
        String[] recipients = {
                "<EMAIL>",
                "<EMAIL>",
                TEST_RECIPIENT // 包含测试邮箱
        };

        // 邮件内容
        String subject = "集成测试：批量真实邮件发送";
        String content = "这是批量集成测试发送的真实邮件。";

        // 调用批量发送方法
// 导入 java.util.Arrays 以使用 Arrays.asList 方法
mailService.sendBatchMail(Arrays.asList(recipients), subject, content);

        System.out.println("批量真实邮件发送测试完成，请检查收件箱！");
    }
}
