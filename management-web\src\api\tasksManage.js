import { title } from '@/settings'
import request from '@/utils/request'



// 定义接口服务对象
export default {
  /**
   * 获取任务列表
   * @param {Object} listQuery - 查询参数对象，包含 taskId、title、publisher、dueDate、importance、status 等查询条件，
   *                           以及 pageNo（默认值为1）和 pageSize（默认值为10）用于分页
   * @returns {Promise} - 返回一个 Promise 对象，成功时 resolve 为服务器返回的数据，失败时 reject 错误信息
   */


  /**
   * 根据ID获取单个任务
   * @param {number} id - 任务ID
   * @returns {Promise} - 返回一个 Promise 对象，成功时 resolve 为服务器返回的数据，失败时 reject 错误信息
   */
  getTaskById(id) {
    return request({
      url: `/tasks/${id}`,
      method: 'get'
    })
      .catch(error => {
        console.error('获取单个任务失败', error);
        return Promise.reject(error);
      });
  },

  /**
   * 新增任务
   * @param {Object} tasks - 包含任务信息的对象，具体字段根据后端Tasks实体类定义
   * @returns {Promise} - 返回一个 Promise 对象，成功时 resolve 为服务器返回的数据，失败时 reject 错误信息
   */
  addTasks(tasks) {
    return request({
      url: '/tasks/add',
      method: 'post',
      data: tasks
    })
      .catch(error => {
        console.error('新增任务失败', error);
        return Promise.reject(error);
      });
  },

  /**
   * 更新任务
   * @param {Object} tasks - 包含更新后任务信息的对象，具体字段根据后端Tasks实体类定义
   * @returns {Promise} - 返回一个 Promise 对象，成功时 resolve 为服务器返回的数据，失败时 reject 错误信息
   */
  updateTasks(tasks) {
    return request({
      url: '/tasks/update',
      method: 'put',
      data: tasks
    })
      .catch(error => {
        console.error('更新任务失败', error);
        return Promise.reject(error);
      });
  },

  /**
   * 根据ID删除任务
   * @param {number} taskId - 任务ID
   * @returns {Promise} - 返回一个 Promise 对象，成功时 resolve 为服务器返回的数据，失败时 reject 错误信息
   */
  deleteTask(taskId) {
    return request({
      url: '/tasks/' + taskId,
      method: 'delete'
    })
      .catch(error => {
        console.error('删除任务失败', error);
        return Promise.reject(error);
      });
  }
}
export function getTasksList(query) {
  return request({
    url: '/tasks/list',
    method: 'get',
    params: query
  })
}
export function saveComplexSysProtect(taskId) {
  return request({
    url: '/tasks/saveComplexSysProtect',
    method: 'post',
    data: { taskId }
  })
}

