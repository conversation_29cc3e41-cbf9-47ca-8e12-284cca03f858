package com.example.sys.utils;

import eu.bitwalker.useragentutils.UserAgent;
import org.springframework.stereotype.Component;

@Component
public class UserAgentUtils {
    public String parseUserAgent(String userAgentHeader) {
        UserAgent userAgent = UserAgent.parseUserAgentString(userAgentHeader);
        return userAgent.getBrowser().getName() + " " + 
               userAgent.getBrowserVersion() + "/" + 
               userAgent.getOperatingSystem().getName();
    }
}