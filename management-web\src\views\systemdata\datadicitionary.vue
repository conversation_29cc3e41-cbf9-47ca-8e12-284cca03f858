<template>
  <div class="app-container">
    <!-- 顶部菜单 -->
    <el-menu
        class="el-menu-horizontal"
        :default-active="activeMenu"
        @select="handleSelect"
        mode="horizontal"
    >
      <el-menu-item index="1">数据字典</el-menu-item>
      <el-menu-item index="2">版本管理</el-menu-item>
    </el-menu>

    <div class="content">
      <!-- 数据字典界面 -->
      <div v-if="activeMenu === '1'">
        <h2>数据字典</h2>
        <div class="filter-container">
          <el-input
              v-model="searchTerm"
              placeholder="搜索术语或数据格式..."
              style="width: 200px;"
              class="filter-item"
          />
        </div>

        <el-table
            :data="filteredDictionary"
            v-loading="loading"
            style="width: 100%"
        >
          <el-table-column prop="term" label="术语" width="180"></el-table-column>
          <el-table-column prop="form" label="数据格式"></el-table-column>
          <el-table-column prop="descibe" label="描述"></el-table-column>
          <el-table-column label="操作" width="220">
            <template slot-scope="scope">
              <el-button size="mini" @click="handleUpdate(scope.row)">编辑</el-button>
              <el-button
                  size="mini"
                  type="danger"
                  @click="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-button
            type="primary"
            @click="handleCreate"
            style="margin-top: 20px;"
        >添加</el-button>
      </div>

      <!-- 版本管理界面（保持原样） -->
      <div v-if="activeMenu === '2'">
        <h2>版本管理</h2>
        <el-table :data="versions">
          <el-table-column prop="version" label="版本号" width="120"></el-table-column>
          <el-table-column prop="date" label="发布日期" width="180"></el-table-column>
          <el-table-column prop="description" label="更新描述"></el-table-column>
          <el-table-column label="操作" width="220">
            <template slot-scope="scope">
              <el-button @click="handleVersionEdit(scope.row)">编辑</el-button>
              <el-button
                  @click="handleVersionDelete(scope.row)"
                  type="danger"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-button
            type="primary"
            @click="openNewVersionDialog"
            style="margin-top: 20px;"
        >创建新版本</el-button>
      </div>
    </div>

    <!-- 数据字典对话框 -->
    <el-dialog
        :title="dialogStatus === 'update' ? '编辑条目' : '新建条目'"
        :visible.sync="dialogFormVisible"
    >
      <el-form :model="temp" :rules="dictRules" ref="dictForm">
        <el-form-item label="术语" prop="term">
          <el-input v-model="temp.term"></el-input>
        </el-form-item>
        <el-form-item label="数据格式" prop="form">
          <el-input v-model="temp.form"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="descibe">
          <el-input v-model="temp.descibe"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button
            type="primary"
            @click="dialogStatus === 'update' ? updateDictEntry() : createDictEntry()"
        >确认</el-button>
      </div>
    </el-dialog>

    <!-- 版本管理对话框（保持原样） -->
    <el-dialog
        :title="currentEditVersion ? '编辑版本' : '创建新版本'"
        :visible.sync="versionDialogVisible"
    >
      <el-form :model="newVersion" :rules="versionRules" ref="versionForm">
        <el-form-item label="版本号" prop="version">
          <el-input v-model="newVersion.version"></el-input>
        </el-form-item>
        <el-form-item label="发布日期" prop="date">
          <el-input
              v-model="newVersion.date"
              placeholder="请输入发布日期 (YYYY-MM-DD)"
          ></el-input>
        </el-form-item>
        <el-form-item label="更新描述" prop="description">
          <el-input v-model="newVersion.description"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="versionDialogVisible = false">取消</el-button>
        <el-button
            type="primary"
            @click="submitVersionData"
        >{{ currentEditVersion ? '更新' : '创建' }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import dictionaryApi from '@/api/dictionary'

export default {
  data() {
    return {
      activeMenu: '1',
      loading: false,
      searchTerm: '',

      // 数据字典相关数据
      dictionary: [],
      temp: {
        dictionId: null,
        term: '',
        form: '',
        descibe: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      dictRules: {
        term: [{ required: true, message: '术语不能为空', trigger: 'blur' }],
        form: [{ required: true, message: '数据格式不能为空', trigger: 'blur' }],
        descibe: [{ required: true, message: '描述不能为空', trigger: 'blur' }]
      },

      // 版本管理相关数据（保持原样）
      versions: [
        { version: '1.0', date: '2024-08-01', description: '初始版本' },
        { version: '1.1', date: '2024-09-01', description: '增加新术语和数据格式' },
      ],
      versionDialogVisible: false,
      newVersion: {
        version: '',
        date: '',
        description: ''
      },
      currentEditVersion: null,
      versionRules: {
        version: [{ required: true, message: '版本号不能为空', trigger: 'blur' }],
        date: [{ required: true, message: '发布日期不能为空', trigger: 'blur' }],
        description: [{ required: true, message: '更新描述不能为空', trigger: 'blur' }]
      }
    }
  },

  computed: {
    // 前端本地过滤
    filteredDictionary() {
      return this.dictionary.filter(item =>
          item.term.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
          item.form.toLowerCase().includes(this.searchTerm.toLowerCase())
      )
    }
  },

  created() {
    this.fetchDictionaryData()
  },

  methods: {
    // 数据字典方法
    async fetchDictionaryData() {
      this.loading = true
      try {
        const response = await dictionaryApi.getAllDictionary()
        this.dictionary = response.data
      } catch (error) {
        this.$notify.error({
          title: '错误',
          message: '获取数据失败: ' + (error.message || '未知错误')
        })
      } finally {
        this.loading = false
      }
    },

    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
    },

    handleUpdate(row) {
      this.temp = { ...row }
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
    },

    async createDictEntry() {
      try {
        await this.$refs.dictForm.validate()
        await dictionaryApi.addDictionary(this.temp)
        this.dialogFormVisible = false
        this.fetchDictionaryData()
        this.$notify.success('创建成功')
      } catch (error) {
        console.error('创建失败:', error)
      }
    },

    async updateDictEntry() {
      try {
        await this.$refs.dictForm.validate()
        await dictionaryApi.updateDictionary(this.temp)
        this.dialogFormVisible = false
        this.fetchDictionaryData()
        this.$notify.success('更新成功')
      } catch (error) {
        console.error('更新失败:', error)
      }
    },

    handleDelete(row) {
      this.$confirm('确认删除该条目?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await dictionaryApi.deleteDictionary(row.dictionId)
          this.fetchDictionaryData()
          this.$notify.success('删除成功')
        } catch (error) {
          this.$notify.error('删除失败: ' + error.message)
        }
      }).catch(() => {})
    },

    resetTemp() {
      this.temp = {
        dictionId: null,
        term: '',
        form: '',
        descibe: ''
      }
    },

    // 版本管理方法（保持原样）
    openNewVersionDialog() {
      this.newVersion = { version: '', date: '', description: '' }
      this.currentEditVersion = null
      this.versionDialogVisible = true
    },

    submitVersionData() {
      this.$refs.versionForm.validate(valid => {
        if (valid) {
          if (this.currentEditVersion) {
            const index = this.versions.findIndex(
                v => v.version === this.currentEditVersion.version
            )
            this.$set(this.versions, index, { ...this.newVersion })
          } else {
            this.versions.push({ ...this.newVersion })
          }
          this.versionDialogVisible = false
          this.$notify.success({
            title: '成功',
            message: this.currentEditVersion ? '更新成功' : '创建成功'
          })
        }
      })
    },

    handleVersionEdit(row) {
      this.newVersion = { ...row }
      this.currentEditVersion = row
      this.versionDialogVisible = true
    },

    handleVersionDelete(row) {
      this.versions = this.versions.filter(item => item.version !== row.version)
      this.$notify.success({
        title: '成功',
        message: '删除成功'
      })
    },

    // 公共方法
    handleSelect(key) {
      this.activeMenu = key
    }
  }
}
</script>

<style scoped>
.app-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.el-menu-horizontal {
  margin-top: 20px;
}

.content {
  margin-top: 20px;
}

.filter-container {
  margin-bottom: 20px;
}
</style>
