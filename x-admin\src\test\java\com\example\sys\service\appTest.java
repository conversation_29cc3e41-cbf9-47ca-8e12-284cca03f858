package com.example.sys.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.SimpleMailMessage;

@SpringBootTest
public class appTest {

    @Autowired
    private JavaMailSender sender;

    private String subject = "测试邮件主题";
    private String content = "这是一封测试邮件的内容。";
    private String to = "<EMAIL>";
    private String form = to;

    @Test
    public void testSendSimpleMail() {
// 推测正确的类名可能是 SimpleMailMessage，导入对应的类
SimpleMailMessage mail = new SimpleMailMessage();
        mail.setTo(to);
        mail.setSubject(subject);
        mail.setText(content);
        mail.setFrom(form);
        sender.send(mail);
        System.out.println("邮件发送成功！");
    }
}
