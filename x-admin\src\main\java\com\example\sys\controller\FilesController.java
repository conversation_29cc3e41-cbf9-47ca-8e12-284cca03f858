package com.example.sys.controller;

import com.example.sys.annotation.OperationLog;
import com.example.sys.service.IFilesService;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.stereotype.Controller;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

@Controller
@RequestMapping("/files")
public class FilesController {

    @Autowired
    private IFilesService filesService;

    // 上传文件
    @PostMapping("/upload")
    @OperationLog(
        moduleName = "文件管理",
        operationType = "上传",
        desc = "'上传文件：' + #file.originalFilename", // 使用 originalFilename 获取文件名
        // dataId = "#file.originalFilename",
        dataId = "#result",
        isSensitive = false
    )
    @ResponseBody
    public String upload(@RequestParam("file") MultipartFile file) {
        return filesService.uploadFile(file);
    }

    // 删除文件
    @DeleteMapping("/delete/{fileId}")
    @OperationLog(
        moduleName = "文件管理",
        operationType = "删除",
        desc = "'删除文件'", // 调用服务层方法获取文件名
        dataId = "#fileId",
        isSensitive = false
    )
    @ResponseBody
    public String deleteFile(@PathVariable Integer fileId) {
        return filesService.deleteFile(fileId);
    }

    // 新的下载文件接口
    @GetMapping("/download/{fileId}")
    @OperationLog(
        moduleName = "文件管理",
        operationType = "下载",
        desc = "'下载文件'", // 调用服务层方法获取文件名
        dataId = "#fileId",
        isSensitive = false
    )
    public ResponseEntity<Resource> downloadFile(@PathVariable Integer fileId) {
        return filesService.downloadFile(fileId);
    }
}