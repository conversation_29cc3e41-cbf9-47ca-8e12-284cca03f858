package com.example.sys.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;
import com.example.sys.service.IMailService;
import javax.annotation.Resource;
import java.util.List;

@Service
public class MailServiceImpl implements IMailService {
    private static final Logger logger = LoggerFactory.getLogger(MailServiceImpl.class);

    @Resource
    private JavaMailSender mailSender;

    // 从配置文件读取发件人地址（例如：spring.mail.username=<EMAIL>）
    @Value("${spring.mail.username}") 
    private String from; 

    /**
     * 发送单封邮件（添加 from 发件人）
     */
    @Override
    public void sendMail(String to, String subject, String content) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(to);       // 收件人
            message.setSubject(subject);  // 主题
            message.setText(content);     // 内容
            message.setFrom(from);       // 新增：发件人（从配置读取）
            mailSender.send(message);
            logger.info("邮件发送成功，发件人：{}，收件人：{}", from, to);
        } catch (Exception e) {
            logger.error("邮件发送失败，发件人：{}，收件人：{}", from, to, e);
        }
    }

    /**
     * 批量发送邮件（无需修改，复用单封逻辑）
     */
    @Override
    public void sendBatchMail(List<String> toList, String subject, String content) {
        for (String to : toList) {
            sendMail(to, subject, content); // 单封发送已包含 from
        }
    }
}