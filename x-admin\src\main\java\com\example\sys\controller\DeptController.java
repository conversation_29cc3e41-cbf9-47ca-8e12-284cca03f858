package com.example.sys.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.example.commom.vo.Result;
import com.example.sys.annotation.OperationLog;
import com.example.sys.entity.Dept;
import com.example.sys.service.IDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 部门 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-26
 */
@RestController
@RequestMapping("/dept")
public class DeptController {
    @Autowired
    private IDeptService deptService;

    @GetMapping("/list")
    public Result<Map<String, Object>> getDeptList(
            @RequestParam(value = "deptId", required = false) Integer deptId,
            @RequestParam(value = "pid", required = false) Integer pid,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "phone", required = false) Integer phone,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "address", required = false) String address
    ) {
        LambdaQueryWrapper<Dept> wrapper = new LambdaQueryWrapper<>();
        if (deptId != null) {
            wrapper.eq(Dept::getDeptId, deptId);
        }
        if (pid != null) {
            wrapper.eq(Dept::getPid, pid);
        }
        if (phone != null) {
            wrapper.eq(Dept::getPhone, phone.toString());
        }
        wrapper.eq(StringUtils.hasLength(name), Dept::getName, name);
        wrapper.eq(StringUtils.hasLength(status), Dept::getStatus, status);
        wrapper.eq(StringUtils.hasLength(address), Dept::getAddress, address);

        List<Dept> depts = deptService.list(wrapper);

        Map<String, Object> data = new HashMap<>();
        data.put("depts", depts);

        return Result.success(data);
    }

    @PostMapping("/add")
    @OperationLog(
            moduleName = "部门管理",
            operationType = "新增",
            desc = "'新增部门：' + #data.name",
            dataId = "#data.deptId",
            isSensitive = true 
    )
    public Result<?> addDept(@RequestBody Dept dept) {
        deptService.save(dept);
        return Result.success(dept,"新增部门成功");
    }

    @PutMapping("/updata")
    @OperationLog(
            moduleName = "部门管理",
            operationType = "修改",
            desc = "'修改部门：' + #data.name",
            dataId = "#data.deptId", 
            isSensitive = true 
    )
    public Result<?> updataDept(@RequestBody Dept dept) {
        deptService.updateById(dept);
        return Result.success(dept,"修改部门成功");
    }

    @DeleteMapping("/{deptId}")
    @OperationLog(
            moduleName = "部门管理",
            operationType = "删除",
            desc = "'删除部门：' + #data.name",
            dataId = "#data.deptId",
            isSensitive = true
    )
    public Result<?> deleteDept(@PathVariable("deptId") Integer deptId) {
        Dept dept = deptService.getById(deptId);
        deptService.removeById(deptId);
        return Result.success(dept,"删除部门成功");
    }

    // 新增搜索部门名称接口
    @GetMapping("/search")
    public Result<Map<String, Object>> searchDeptByName(@RequestParam("name") String name) {
        System.out.println("正在搜索部门名称: " + name); // 添加日志
        LambdaQueryWrapper<Dept> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(Dept::getName, name);

        List<Dept> depts = deptService.list(wrapper);

        Map<String, Object> data = new HashMap<>();
        data.put("depts", depts);

        return Result.success(data);
    }
}
