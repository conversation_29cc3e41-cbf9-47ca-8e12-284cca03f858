package com.example.sys.service.impl;

import com.example.sys.entity.SysProtect;
import com.example.sys.entity.Tasks;
import com.example.sys.service.ISysProtectService;
import com.example.sys.service.ITasksService;
import com.example.sys.service.IUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;

@Service
public class TaskAutoGenerateService {

    @Autowired
    private ISysProtectService sysProtectService;

    @Autowired
    private ITasksService tasksService;

    @Autowired
    private IUserService userService;

    // 日期格式与 SysProtect 中 plannedEvaluationTime 存储格式一致（假设为 yyyy-MM-dd）
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 每天凌晨 0 点执行任务生成逻辑
     */
    @Scheduled(cron = "0 0 0 * * ?") // Cron 表达式：每天 0:00 执行
    public void autoGenerateTasks() {
        // 查询所有有效的 SysProtect 记录（根据业务需求过滤，如未删除、未生成过任务等）
        List<SysProtect> sysProtectList = sysProtectService.list();

        for (SysProtect sysProtect : sysProtectList) {
            // 跳过无计划测评时间的记录
            String plannedEvaluationTimeStr = sysProtect.getPlannedEvaluationTime();
            if (!StringUtils.hasText(plannedEvaluationTimeStr)) {
                continue;
            }

            // 解析计划测评时间（处理格式错误）
            LocalDate plannedEvaluationTime;
            try {
                plannedEvaluationTime = LocalDate.parse(plannedEvaluationTimeStr, DATE_FORMATTER);
            } catch (Exception e) {
                // 日志记录格式错误，跳过当前记录
                continue;
            }

            // 计算当前日期与计划日期的差值（计划日期 - 当前日期）
            LocalDate currentDate = LocalDate.now();
            long daysUntilPlanned = ChronoUnit.DAYS.between(currentDate, plannedEvaluationTime);

            // 如果剩余 2 天，则生成任务
            if (daysUntilPlanned == 2) {
                Tasks task = buildTaskFromSysProtect(sysProtect);
                tasksService.save(task); // 保存任务到数据库
            }
        }
    }

    /**
     * 将 SysProtect 信息映射到 Tasks 对象
     */
    private Tasks buildTaskFromSysProtect(SysProtect sysProtect) {
        Tasks task = new Tasks();

        // 基础信息映射（根据实际表结构调整）
        task.setApplicationId(sysProtect.getApplicationId());
        task.setSystemName(sysProtect.getSystemName());
        task.setSystemShortName(sysProtect.getSystemShortName());
        task.setSystemOwnerOrg(sysProtect.getSystemOwnerOrg());

        // 计划测评时间（转换为 LocalDate）
        task.setPlannedEvaluationTime(LocalDate.parse(sysProtect.getPlannedEvaluationTime(), DATE_FORMATTER));

        // 任务状态默认 "未开始"（根据业务需求调整）
        task.setTaskStatus("未开始");

        // 任务创建时间（当前时间）
        task.setCreateTime(LocalDateTime.now());

        // 任务描述（可选，根据需求自定义）
        task.setTaskDescription("自动生成：计划测评时间前 2 天提醒");

        // 设置任务负责人
        String organization = sysProtect.getSystemOwnerOrg();
        List<String> emails = userService.getEmailsByOrganization(organization);
        if (!emails.isEmpty()) {
            task.setResponsiblePerson(emails.get(0));
        }

        // 任务重要程度（1=一般，2=重要，3=很重要），可根据实际情况设置
        task.setImportance(2);

        return task;
    }
}