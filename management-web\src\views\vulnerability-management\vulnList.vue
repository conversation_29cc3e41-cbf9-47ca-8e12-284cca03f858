<template>
  <div class="app-container">
    <!-- 顶部筛选区域 -->
    <el-card class="box-card">
      <div slot="header">
        <span class="card-title">漏洞列表</span>
      </div>
      <div class="filter-container">
        <el-form :model="listQuery" :inline="false" class="search-form" size="small" label-width="100px">
          <el-row :gutter="20" align="middle">
            <el-col :span="6">
              <el-form-item label="所属单位">
                <el-select
                  v-model="listQuery.systemOwnerOrg"
                  placeholder="请选择所属单位"
                  clearable
                  style="width: 200px"
                >
                  <el-option label="中国核电" value="中国核电"></el-option>
                  <el-option label="秦山核电" value="秦山核电"></el-option>
                  <el-option label="江苏核电" value="江苏核电"></el-option>
                  <el-option label="福清核电" value="福清核电"></el-option>
                  <el-option label="海南核电" value="海南核电"></el-option>
                  <el-option label="三门核电" value="三门核电"></el-option>
                  <el-option label="霞浦核电" value="霞浦核电"></el-option>
                  <el-option label="漳州核电" value="漳州核电"></el-option>
                  <el-option label="中核武汉" value="中核武汉"></el-option>
                  <el-option label="中核能源" value="中核能源"></el-option>
                  <el-option label="运行研究院" value="运行研究院"></el-option>
                  <el-option label="研究运维" value="研究运维"></el-option>
                  <el-option label="辽宁核电" value="辽宁核电"></el-option>
                  <el-option label="中核山东" value="中核山东"></el-option>
                  <el-option label="中核储能" value="中核储能"></el-option>
                  <el-option label="中核苏能" value="中核苏能"></el-option>
                  <el-option label="中核海得" value="中核海得"></el-option>
                  <el-option label="河北核电" value="河北核电"></el-option>
                  <el-option label="庄河核电" value="庄河核电"></el-option>
                  <el-option label="中核光电" value="中核光电"></el-option>
                  <el-option label="桃花江核电" value="桃花江核电"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="系统名称">
                <el-input
                  v-model="listQuery.systemName"
                  placeholder="请输入系统名称"
                  clearable
                  style="width: 200px"
                  @keyup.enter.native="handleFilter"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="业务类型">
                <el-select
                  v-model="listQuery.businessType"
                  placeholder="请选择业务类型"
                  clearable
                  style="width: 200px"
                >
                  <el-option label="数据分析" value="数据分析"></el-option>
                  <el-option label="安全监控" value="安全监控"></el-option>
                  <el-option label="辐射监测平台" value="辐射监测平台"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="漏洞编号">
                <el-input
                  v-model="listQuery.vulId"
                  placeholder="请输入漏洞编号"
                  clearable
                  style="width: 200px"
                  @keyup.enter.native="handleFilter"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" align="middle">
            <el-col :span="6">
              <el-form-item label="漏洞名称">
                <el-input
                  v-model="listQuery.vulName"
                  placeholder="请输入漏洞名称"
                  clearable
                  style="width: 200px"
                  @keyup.enter.native="handleFilter"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="漏洞类型">
                <el-input
                  v-model="listQuery.vulType"
                  placeholder="请输入漏洞类型"
                  clearable
                  style="width: 200px"
                  @keyup.enter.native="handleFilter"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="漏洞级别">
                <el-select
                  v-model="listQuery.vulLevel"
                  placeholder="漏洞级别"
                  clearable
                  style="width: 200px"
                >
                  <el-option label="高" value="高"></el-option>
                  <el-option label="中" value="中"></el-option>
                  <el-option label="低" value="低"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="漏洞状态">
                <el-select
                  v-model="listQuery.vulStatus"
                  placeholder="漏洞状态"
                  clearable
                  style="width: 200px"
                >
                  <el-option label="已修复" value="已修复"></el-option>
                  <el-option label="修复中" value="修复中"></el-option>
                  <el-option label="待修复" value="待修复"></el-option>
                  <el-option label="未修复" value="未修复"></el-option>
                  <el-option label="无需修复" value="无需修复"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" align="middle">
            <el-col :span="6">
              <el-form-item label="披露日期">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                  style="width: 240px"
                />
              </el-form-item>
            </el-col>
            <el-col :span="18">
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24" style="text-align: center; margin-top: 20px;">
              <el-button type="primary" icon="el-icon-search" @click="handleFilter">查询</el-button>
              <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
      @sort-change="sortChange"
    >
      <el-table-column
        label="所属单位"
        prop="systemOwnerOrg"
        align="center"
        width="120"
      />
      <el-table-column
        label="系统名称"
        prop="systemName"
        align="center"
        min-width="180"
      />
      <el-table-column
        label="业务类型"
        prop="businessType"
        align="center"
        width="120"
      />
      <el-table-column
        label="漏洞编号"
        prop="vulId"
        sortable="custom"
        align="center"
        width="150"
      />
      <el-table-column
        label="漏洞名称"
        prop="vulName"
        align="center"
        min-width="250"
      >
        <template slot-scope="{row}">
          <span class="link-type" @click="handleViewDetail(row)">{{ row.vulName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="漏洞类型"
        prop="vulType"
        align="center"
        width="120"
      />
      <el-table-column
        label="披露日期"
        prop="disclosureDate"
        align="center"
        width="120"
        sortable="custom"
      >
        <template slot-scope="{row}">
          <span>{{ row.disclosureDate | parseTime('{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="漏洞级别"
        prop="vulLevel"
        align="center"
        width="120"
      >
        <template slot-scope="{row}">
          <el-tag :type="getVulLevelType(row.vulLevel)">
            {{ row.vulLevel }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="漏洞状态"
        prop="vulStatus"
        align="center"
        width="120"
      >
        <template slot-scope="{row}">
          <el-tag :type="getVulStatusType(row.vulStatus)">
            {{ row.vulStatus }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="120"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{row,$index}">
          <el-button
            type="primary"
            size="mini"
            @click="handleUpdate(row)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
        style="width: 400px; margin-left:50px;"
      >
        <el-form-item label="所属单位" prop="systemOwnerOrg">
          <el-input v-model="temp.systemOwnerOrg" />
        </el-form-item>
        <el-form-item label="系统名称" prop="systemName">
          <el-input v-model="temp.systemName" />
        </el-form-item>
        <el-form-item label="业务类型" prop="businessType">
          <el-input v-model="temp.businessType" />
        </el-form-item>
        <el-form-item label="漏洞ID" prop="vulId">
          <el-input v-model="temp.vulId" />
        </el-form-item>
        <el-form-item label="漏洞名称" prop="vulName">
          <el-input v-model="temp.vulName" />
        </el-form-item>
        <el-form-item label="漏洞类型" prop="vulType">
          <el-input v-model="temp.vulType" />
        </el-form-item>
        <el-form-item label="披露日期" prop="disclosureDate">
          <el-date-picker
            v-model="temp.disclosureDate"
            type="date"
            placeholder="选择日期"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="漏洞级别" prop="vulLevel">
          <el-select v-model="temp.vulLevel" class="filter-item" placeholder="请选择">
            <el-option label="高" value="高" />
            <el-option label="中" value="中" />
            <el-option label="低" value="低" />
          </el-select>
        </el-form-item>
        <el-form-item label="漏洞状态" prop="vulStatus">
          <el-select v-model="temp.vulStatus" class="filter-item" placeholder="请选择">
            <el-option label="已修复" value="已修复" />
            <el-option label="未修复" value="未修复" />
            <el-option label="修复中" value="修复中" />
          </el-select>
        </el-form-item>
        <el-form-item label="漏洞描述" prop="vulDescription">
          <el-input
            v-model="temp.vulDescription"
            :autosize="{ minRows: 2, maxRows: 4}"
            type="textarea"
            placeholder="请输入漏洞描述"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { fetchVulnerabilityList, createVulnerability, updateVulnerability, deleteVulnerability } from '@/api/vuln'
import Pagination from '@/components/Pagination'
import { parseTime } from '@/utils'

export default {
  name: 'VulnerabilityManagement',
  components: { Pagination },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        pageNo: 1,
        pageSize: 20,
        systemOwnerOrg: undefined,
        systemName: undefined,
        businessType: undefined,
        vulId: undefined,
        vulName: undefined,
        vulType: undefined,
        vulLevel: undefined,
        vulStatus: undefined,
        startDate: undefined,
        endDate: undefined
      },
      dateRange: [],
      temp: {
        id: undefined,
        systemOwnerOrg: '',
        systemName: '',
        businessType: '',
        vulId: '',
        vulName: '',
        vulType: '',
        disclosureDate: '',
        vulLevel: '',
        vulStatus: '',
        vulDescription: '',
        cveNumber: '',
        cnvdNumber: '',
        cnnvdNumber: '',
        cvssScore: '',
        patchSituation: '',
        impactScope: '',
        solutionSuggestions: '',
        referenceLink: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑漏洞',
        create: '新增漏洞'
      },
      rules: {
        vulName: [{ required: true, message: '漏洞名称必填', trigger: 'blur' }],
        vulLevel: [{ required: true, message: '漏洞级别必选', trigger: 'change' }],
        vulStatus: [{ required: true, message: '漏洞状态必选', trigger: 'change' }],
        disclosureDate: [{ required: true, message: '披露日期必填', trigger: 'change' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      // 调用真实API获取漏洞列表数据
      fetchVulnerabilityList(this.listQuery)
        .then(response => {
          if (response && response.data) {
            this.list = response.data.vulList || []
            this.total = response.data.total || 0
          } else {
            this.list = []
            this.total = 0
          }
        })
        .catch(error => {
          console.error('获取漏洞列表失败:', error)
          this.$message.error('获取漏洞列表失败')
          this.list = []
          this.total = 0
        })
        .finally(() => {
          this.listLoading = false
        })
    },
    handleViewDetail(row) {
      this.$router.push({
        path: `/vulnerability-management/detail/${row.id}`,
        query: { id: row.id }
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.listQuery.startDate = this.dateRange ? this.dateRange[0] : undefined
      this.listQuery.endDate = this.dateRange ? this.dateRange[1] : undefined
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageNo: 1,
        pageSize: 20,
        systemOwnerOrg: undefined,
        systemName: undefined,
        businessType: undefined,
        vulId: undefined,
        vulName: undefined,
        vulType: undefined,
        vulLevel: undefined,
        vulStatus: undefined,
        startDate: undefined,
        endDate: undefined
      }
      this.dateRange = []
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
      if (prop === 'vulId') {
        this.sortByVulId(order)
      }
    },
    sortByVulId(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+vulId'
      } else {
        this.listQuery.sort = '-vulId'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        systemOwnerOrg: '',
        systemName: '',
        businessType: '',
        vulId: '',
        vulName: '',
        vulType: '',
        disclosureDate: '',
        vulLevel: '',
        vulStatus: '',
        vulDescription: '',
        cveNumber: '',
        cnvdNumber: '',
        cnnvdNumber: '',
        cvssScore: '',
        patchSituation: '',
        impactScope: '',
        solutionSuggestions: '',
        referenceLink: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.id = parseInt(Math.random() * 100) + 1024
          createVulnerability(this.temp).then(() => {
            this.list.unshift(this.temp)
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '创建成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateVulnerability(tempData.id, tempData).then(() => {
            const index = this.list.findIndex(v => v.id === this.temp.id)
            this.list.splice(index, 1, this.temp)
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleDelete(row, index) {
      this.$confirm('确认删除该漏洞?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteVulnerability(row.id).then(() => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.list.splice(index, 1)
        })
      })
    },
    getVulLevelType(level) {
      switch (level) {
        case '高':
          return 'danger'
        case '中':
          return 'warning'
        case '低':
          return 'info'
        default:
          return ''
      }
    },
    getVulStatusType(status) {
      switch (status) {
        case '已修复':
          return 'success'
        case '修复中':
          return 'warning'
        case '待修复':
          return 'danger'
        case '未修复':
          return 'danger'
        case '无需修复':
          return 'info'
        default:
          return 'info'
      }
    }
  }
}
</script>

<style scoped>
.app-container {
    padding: 5px;
}

.box-card {
    margin-bottom: 20px;
}

.card-title {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
}

.filter-container {
    padding: 20px;
    background: transparent;
    border-radius: 8px;
    margin-bottom: 20px;
}

.search-form {
    margin: 0;
}

.search-form .el-form-item {
    margin-bottom: 20px;
}

.search-form .el-form-item__label {
    font-weight: 500;
    color: #606266;
    text-align: right;
    padding-right: 12px;
    line-height: 32px;
}

.search-form .el-form-item__content {
    line-height: 32px;
}

.search-form .el-row {
    margin-bottom: 0;
}

.search-form .el-col {
    padding: 0 10px;
}

.search-form .el-input,
.search-form .el-select,
.search-form .el-date-editor {
    width: 200px;
}

.search-form .el-date-editor--daterange {
    width: 240px;
}

.filter-item {
    display: inline-block;
    vertical-align: middle;
}

.link-type {
    color: #409EFF;
    cursor: pointer;
    text-decoration: none;
}

.link-type:hover {
    color: #66b1ff;
    text-decoration: underline;
}

.el-table {
    margin-top: 20px;
}

.el-table th {
    background-color: #fafafa;
    color: #606266;
    font-weight: 600;
}

.el-tag {
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 12px;
}

.el-button {
    border-radius: 6px;
}

.el-input, .el-select, .el-date-editor {
    border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .search-form .el-col {
        margin-bottom: 8px;
    }
}

@media (max-width: 768px) {
    .filter-container {
        padding: 16px;
    }
    
    .search-form .el-form-item {
        margin-bottom: 12px;
    }
}
</style>
