<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header">
        <span class="card-title">等保详情</span>
      </div>

      <!-- 搜索表单 -->
      <el-form :model="queryForm" :inline="true" class="search-form" size="small" label-width="85px">
        <el-row :gutter="16" align="middle">
          <el-col :span="6">
            <el-form-item label="所属单位">
              <el-select v-model="queryForm.systemOwnerOrg" placeholder="请选择所属单位" clearable style="width: 100%">
                <el-option label="中国核电" value="中国核电"></el-option>
                <el-option label="秦山核电" value="秦山核电"></el-option>
                <el-option label="江苏核电" value="江苏核电"></el-option>
                <el-option label="福清核电" value="福清核电"></el-option>
                <el-option label="海南核电" value="海南核电"></el-option>
                <el-option label="三门核电" value="三门核电"></el-option>
                <el-option label="霞浦核电" value="霞浦核电"></el-option>
                <el-option label="漳州核电" value="漳州核电"></el-option>
                <el-option label="中核武汉" value="中核武汉"></el-option>
                <el-option label="中核能源" value="中核能源"></el-option>
                <el-option label="运行研究院" value="运行研究院"></el-option>
                <el-option label="研究运维" value="研究运维"></el-option>
                <el-option label="辽宁核电" value="辽宁核电"></el-option>
                <el-option label="中核山东" value="中核山东"></el-option>
                <el-option label="中核储能" value="中核储能"></el-option>
                <el-option label="中核苏能" value="中核苏能"></el-option>
                <el-option label="中核海得" value="中核海得"></el-option>
                <el-option label="河北核电" value="河北核电"></el-option>
                <el-option label="庄河核电" value="庄河核电"></el-option>
                <el-option label="中核光电" value="中核光电"></el-option>
                <el-option label="桃花江核电" value="桃花江核电"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="系统名称">
              <el-input v-model="queryForm.systemName" placeholder="请输入系统名称" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="业务类型">
              <el-select v-model="queryForm.businessType" placeholder="请选择业务类型" clearable style="width: 100%">
                <el-option label="生产作业" value="生产作业" />
                <el-option label="指挥调度" value="指挥调度" />
                <el-option label="内部办公" value="内部办公" />
                <el-option label="公众服务" value="公众服务" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="备案号">
              <el-input v-model="queryForm.filingNumber" placeholder="请输入备案号" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16" align="middle">
          <el-col :span="6">
            <el-form-item label="定级级别">
              <el-select v-model="queryForm.classificationLevel" placeholder="请选择定级级别" clearable style="width: 100%">
                <el-option label="一级" value="一级" />
                <el-option label="二级" value="二级" />
                <el-option label="三级" value="三级" />
                <el-option label="四级" value="四级" />
                <el-option label="五级" value="五级" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="测评时间">
              <el-date-picker
                  v-model="queryForm.evaluationTime"
                  type="date"
                  placeholder="选择测评时间"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="测评结果">
              <el-select v-model="queryForm.evaluationResult" placeholder="请选择测评结果" clearable style="width: 100%">
                <el-option label="符合" value="符合" />
                <el-option label="基本符合" value="基本符合" />
                <el-option label="不符合" value="不符合" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="测评单位">
              <el-input v-model="queryForm.evaluationOrganization" placeholder="请输入测评单位" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: center; margin-top: 10px;">
            <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 数据表格 -->
      <el-table
          v-loading="loading"
          :data="tableData"
          border
          style="width: 100%; margin-top: 20px;"
          :header-cell-style="{ background: '#f5f7fa' }"
      >
        <!--        <el-table-column prop="id" label="序号" width="60" align="center" />-->
        <el-table-column prop="systemOwnerOrg" label="所属单位" min-width="120" align="center" />
        <el-table-column prop="systemName" label="系统名称" min-width="120" align="center" />
        <el-table-column prop="businessType" label="业务类型" min-width="120" align="center" />
        <el-table-column prop="filingNumber" label="备案号" min-width="120" align="center" />
        <el-table-column prop="classificationLevel" label="定级级别" width="100" align="center" />
        <el-table-column prop="evaluationTime" label="测评时间" min-width="120" align="center" />
        <el-table-column prop="evaluationResult" label="测评结果" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getResultType(scope.row.evaluationResult)">
              {{ scope.row.evaluationResult }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="evaluationOrganization" label="测评单位" min-width="120" align="center" />
        <el-table-column prop="evaluationStatus" label="测评状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag>{{ scope.row.evaluationStatus }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="systemStatus" label="系统状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getSystemStatusType(scope.row.systemStatus)">
              {{ scope.row.systemStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="分类" width="100" align="center" />
        <el-table-column prop="controlPoint" label="控制点" min-width="120" align="center" />
        <el-table-column prop="evaluationItem" label="测评项" min-width="120" align="center" />
        <el-table-column prop="resultRecord" label="结果记录" min-width="120" align="center" />
        <el-table-column prop="complianceStatus" label="符合性状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.complianceStatus === '符合' ? 'success' : 'danger'">
              {{ scope.row.complianceStatus }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 分页 -->
    <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNo"
        :page-sizes="[5, 10, 20, 40]"
        :page-size="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
    </el-pagination>
  </div>
</template>

<script>
import { getSysProtectList, exportSysProtect } from '@/api/securityAssessment'

import axios from 'axios'
export default {
  name: 'SecurityAssessmentDetail',
  data() {
    return {
      exportLoading: false, // 导出加载状态
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 5
      },
      // 查询表单
      queryForm: {
        systemOwnerOrg: '',
        memberUnit: '',
        systemName: '',
        systemShortName: '',
        networkBelonging: '',
        businessType: '',
        filingNumber: '',
        classificationLevel: '',
        evaluationTime: '',
        evaluationResult: '',
        plannedEvaluationTime: '',
        evaluationOrganization: '',
        evaluationStatus: '',
        systemStatus: '',
        category: '',
        controlPoint: '',
        evaluationItem: '',
        complianceStatus: ''
      },
      // 表格数据
      tableData: []
    }
  },
  created() {
    // 处理路由参数，设置筛选条件
    this.handleRouteQuery()
    this.getList()
  },
  methods: {
    // 处理路由参数
    handleRouteQuery() {
      console.log("接收的路由参数:", this.$route.query)
      const query = this.$route.query
      if (query) {
        // 将路由参数设置到查询表单中
        Object.keys(query).forEach(key => {
          if (query[key] && this.queryForm.hasOwnProperty(key)) {
            this.queryForm[key] = query[key]
          }
        })
        // 同时设置到查询参数中
        this.queryParams = {
          ...this.queryParams,
          ...this.queryForm
        }
        console.log("详情参数:",this.queryParams)
      }
    },

    // 获取列表数据
    getList() {
      this.loading = true
      // 添加排序参数，按测评时间从大到小排序
      const params = {
        ...this.queryParams,
        sortBy: 'evaluationTime',
        sortOrder: 'desc'
      }

      getSysProtectList(params)
          .then(response => {
            console.log('API响应数据:', response)
            if (response?.data) {
              this.tableData = response.data.sysProtect || []
              this.total = response.data.total || 0
            } else {
              this.tableData = []
              this.total = 0
            }
          })
          .catch(error => {
            console.error('获取数据失败:', error)
            this.$message.error('获取数据失败')
          })
          .finally(() => {
            this.loading = false
          })
    },

    // 处理查询
    handleQuery() {
      this.queryParams.pageNo = 1
      // 合并查询表单数据到查询参数
      this.queryParams = {
        ...this.queryParams,
        ...this.queryForm
      }
      this.getList()
    },

    // 处理重置
    handleReset() {
      this.queryForm = {
        systemOwnerOrg: '',
        memberUnit: '',
        systemName: '',
        systemShortName: '',
        networkBelonging: '',
        businessType: '',
        filingNumber: '',
        classificationLevel: '',
        evaluationTime: '',
        evaluationResult: '',
        plannedEvaluationTime: '',
        evaluationOrganization: '',
        evaluationStatus: '',
        systemStatus: '',
        category: '',
        controlPoint: '',
        evaluationItem: '',
        complianceStatus: ''
      }
      this.queryParams = {
        pageNo: 1,
        pageSize: this.queryParams.pageSize
      }
      this.getList()
    },

    async handleExport() {
      try {
        this.exportLoading = true
        console.log('开始导出，查询参数:', this.queryForm)
        // 1. 发起导出请求（携带当前所有查询条件）
        const response = await axios.get('/sysProtect/export', {
          params: this.queryForm, // 直接传递查询表单参数
          responseType: 'blob'    // 关键：接收二进制流
        })
        console.log('导出请求响应状态:', response.status)
        // 2. 解析文件名（处理中文编码）
        const fileName = this.parseFileName(response)
        console.log('解析得到的文件名:', fileName)
        // 3. 创建下载链接
        this.triggerFileDownload(response.data, fileName)
      } catch (error) {
        // 4. 统一错误处理
        console.error('导出过程中出现错误:', error)
        this.handleExportError(error)
      } finally {
        this.exportLoading = false
      }
    },

    // 解析响应头中的文件名
    parseFileName(response) {
      const disposition = response.headers['content-disposition']
      const defaultName = '系统保护数据.xlsx'

      if (!disposition) {
        console.log('响应头中未找到content-disposition，使用默认文件名')
        return defaultName
      }

      const utf8FilenameRegex = /filename\*=utf-8''([\w%\-\.]+)/i
      const matches = utf8FilenameRegex.exec(disposition)

      if (matches) {
        const decodedName = decodeURIComponent(matches[1])
        console.log('解析得到的文件名:', decodedName)
        return decodedName
      } else {
        console.log('未匹配到文件名，使用默认文件名')
        return defaultName
      }
    },

    // 触发文件下载
    triggerFileDownload(data, fileName) {
      const blob = new Blob([data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })

      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(link.href)
    },

    // 错误处理
    handleExportError(error) {
      // 网络错误
      if (!error.response) {
        console.error('网络连接异常:', error)
        this.$message.error('网络连接异常，请检查网络')
        return
      }

      // Blob类型错误响应（后端返回的JSON错误）
      if (error.response.data instanceof Blob) {
        const reader = new FileReader()
        reader.onload = () => {
          try {
            const errorData = JSON.parse(reader.result)
            console.error('后端返回的错误信息:', errorData)
            this.$message.error(errorData.message || '导出失败')
          } catch {
            console.error('解析Blob数据为JSON时出错')
            this.$message.error('生成导出文件时发生错误')
          }
        }
        reader.readAsText(error.response.data)
        return
      }

      // HTTP状态码处理
      const status = error.response.status
      const messageMap = {
        400: '请求参数错误',
        401: '登录已过期',
        403: '没有操作权限',
        404: '导出接口未找到，请检查接口地址或联系后端开发人员',
        500: '服务器内部错误'
      }

      console.error(`导出失败，HTTP状态码: ${status}`)
      this.$message.error(messageMap[status] || `导出失败（${status}）`)
    },


    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },

    handleCurrentChange(val) {
      this.queryParams.pageNo = val
      this.getList()
    },

    getResultType(result) {
      const typeMap = {
        '符合': 'success',
        '基本符合': 'warning',
        '不符合': 'danger'
      }
      return typeMap[result] || 'info'
    },

    getSystemStatusType(status) {
      const typeMap = {
        '运行中': 'success',
        '已下线': 'info',
        '已注销': 'danger'
      }
      return typeMap[status] || 'info'
    }
  }
  // computed: {
  //   // 计算当前页的数据
  //   currentPageData() {
  //     const start = (this.queryParams.pageNo - 1) * this.queryParams.pageSize
  //     const end = start + this.queryParams.pageSize
  //     return this.tableData.slice(start, end)
  //   }
  // }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f0f2f5;
}

.box-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .card-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }
}

.search-form {
  padding: 10px 0 0 0;
  .el-form-item {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
  }
  .el-input, .el-select, .el-date-picker, .el-date-editor {
    width: 100%;
    height: 36px !important;
    line-height: 36px !important;
    display: block !important;
    vertical-align: middle !important;
    box-sizing: border-box;
  }
  .el-input__inner {
    height: 36px !important;
    line-height: 36px !important;
    box-sizing: border-box;
    display: block !important;
    vertical-align: middle !important;
  }
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    height: 36px !important;
    line-height: 36px !important;
    box-sizing: border-box;
    display: block !important;
    vertical-align: middle !important;
  }
}

.pagination-container {
  padding: 15px;
  background: white;
  border-top: 1px solid #e8e8e8;
  text-align: right;
}

:deep(.el-pagination) {
  padding: 0;
  margin: 0;
  font-weight: normal;
}
</style>

